log4j.rootLogger=info,error

log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss}[%t][%c][%p]-%m%n

# file \u8F93\u51FA
log4j.appender.file=org.apache.log4j.DailyRollingFileAppender
log4j.appender.file.file=./logs/sup.log
log4j.appender.file.DatePattern='.'yyyy-MM-dd
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n

log4j.logger.info=info
log4j.appender.info=org.apache.log4j.DailyRollingFileAppender
log4j.appender.info.layout=org.apache.log4j.PatternLayout
log4j.appender.info.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n
log4j.appender.info.datePattern='.'yyyy-MM-dd
log4j.appender.info.Threshold = info
log4j.appender.info.append=true
log4j.appender.info.File=./logs/sup.log

log4j.logger.error=error
log4j.appender.error=org.apache.log4j.DailyRollingFileAppender
log4j.appender.error.layout=org.apache.log4j.PatternLayout
log4j.appender.error.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n
log4j.appender.error.datePattern='.'yyyy-MM-dd
log4j.appender.error.Threshold = error
log4j.appender.error.append=true
log4j.appender.error.File=./logs/error.log

#log4j.logger.job=info
#log4j.appender.job=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.job.layout=org.apache.log4j.PatternLayout
#log4j.appender.job.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n
#log4j.appender.job.datePattern='.'yyyy-MM-dd
#log4j.appender.job.append=true
#log4j.appender.job.File=./logs/sup_job.log

### Debug
log4j.logger.org.springframework=info
log4j.logger.com.inspur=info
log4j.logger.org=info
log4j.logger.org.apache.ibatis=error
log4j.logger.com.alibaba.dubbo.config=info
log4j.logger.com.alibaba.dubbo=info
log4j.logger.com.alibaba.dubbo.config.AbstractConfig=info
log4j.logger.com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry=info
