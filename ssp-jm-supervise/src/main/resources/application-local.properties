#\u57FA\u7840\u914D\u7F6E
#\u670D\u52A1\u5668\u7AEF\u53E3\u53F7
server.port=9333
server.servlet.context-path=/supapi
adminRole=SUPER_ADMIN
socialSecurityRole=SOCIAL_SECURITY_ADMIN
medicalRole=MEDICAL_INSURANCE_ADMIN
deliveryAdmin=DELIVERY_ADMIN
hospitalAdmin=HOSPITAL_ADMIN
#\u65E5\u5FD7\u76F8\u5173\u914D\u7F6E
logging.level.root=INFO
logging.level.com.inspur=DEBUG
logging.levle.com.alibaba.dubbo=INFO
logging.level.com.alibaba.dubbo.config.AbstractConfig=INFO
logging.level.com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry=INFO

# \u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.loginUsername=true
spring.datasource.druid.stat-view-servlet.loginPassword=true
spring.datasource.druid.destroy-scheduler=close
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=5000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.dynamic.strict=true
spring.autoconfigure.exclude=com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
spring.datasource.dynamic.datasource.master.driverClassName=com.mysql.jdbc.Driver
#spring.datasource.dynamic.datasource.master.url=*************************************************************************************************************************
#spring.datasource.dynamic.datasource.master.url=************************************************************************************************************************************************
#spring.datasource.dynamic.datasource.master.username=root
#spring.datasource.dynamic.datasource.master.password=root

#???
spring.datasource.dynamic.datasource.master.url=*************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=!QAZ1edf@2022  
#????
#spring.datasource.dynamic.datasource.master.url=************************************************************************************************************************************************
#spring.datasource.dynamic.datasource.master.username=sup
#spring.datasource.dynamic.datasource.master.password=!QAZ1edf@2022


#????
spring.datasource.dynamic.datasource.slave.driverClassName=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.slave.url=************************************************************************************************************************************************
spring.datasource.dynamic.datasource.slave.username=gpo_user_jm
spring.datasource.dynamic.datasource.slave.password=y2022@JM
# \u8BBE\u7F6Emybatis\u8FD4\u56DE\u503C\u4E3A\u7A7A\u662F\u4E5F\u8FD4\u56DEkey
mybatis.configuration.call-setters-on-nulls=true
#\u67E5\u8BE2\u7ED3\u679C\u81EA\u52A8\u6620\u5C04 \u5230 java\u7C7B\u4E2D\u9A7C\u5CF0\u547D\u540D
mybatis.configuration.map-underscore-to-camel-case=true
#\u5F00\u542F\u4E00\u7EA7\u7F13\u5B58
app.l1ClassName=org.jangod.iweb.plugin.cache.ehcache.EhCacheProvider

app.userConvertClz=com.inspur.ssp.bsp.utils.V3UserConvert

asf.token=A0001
app.cloudToken=A0001

#\u9ED8\u8BA4\u5BC6\u7801  123456\u7684md5\u52A0\u5BC6
defaultPassword=e10adc3949ba59abbe56e057f20f883e
#\u6587\u4EF6\u5B58\u653E\u5730\u5740
webdisk.uploadPath=file
webdisk.fileTypes=gif,jpg,jpeg,bmp,png,doc,docx,txt,xlsx,xls,pdf
#\u8857\u9547\u7236\u7EA7\u7F16\u7801
streeParentDeptCode=100002

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size = 15Mb
spring.servlet.multipart.max-request-size = 15Mb


#ç§»å¨äºmaså¹³å°å°å
mas.sms.url = http://************:1992/sms/norsubmit
mas.maxNum = 1000
mas.ecName = é¹¤å±±å¸å»çä¿éå±
mas.apId = hsuser
mas.sign = cvB1M3syE
mas.secretKey = 12qw?34er?

# æ·±å³GPOæ¥å£å¯¹æ¥
#æ­£å¼
#shenzhen.webServiceWsdl=https://gateway.qywgpo.com/tradejm/webservice/hsInterfaceWsService?wsdl
shenzhen.webServiceWsdl=https://gateway.qywgpo.com/tradejm/webservice/jmSuperviseInterfaceService?wsdl

# æµè¯ ??
#shenzhen.webServiceWsdl=https://gateway.test.api.qywgpo.com/tradejm/webservice/hsInterfaceWsService?wsdl

shenzhen.hospitalOrder.interface=getAllOrder
shenzhen.delivery.interface=getDeliverBills
shenzhen.invoice.interface=getInvoice
shenzhen.stockIn.interface=getStockinBills
shenzhen.drug.interface=getDrugsUsable
shenzhen.contract.interface=getAllContract
areaRegionAdmin=DELIVERY_ADMIN
#shenzhen.userCode=hsybj
shenzhen.userCode=jmybj
#shenzhen.iocode=S<EpEFX1K50iEqq@$HDxjx?
shenzhen.iocode=S<EpEFX1K50iEqq@$HDxjx?



# å¹¿å·å¯¹æ¥
## æ­£å¼
#gz.baseUrl.interface=https://gpo.gzggzy.cn/api/v1/healthInsuranceInterface/heShanCity
gz.baseUrl.interface=https://gpo.gzggzy.cn/fsCity/v1/heShanNewInterface/heShanCity

##æµè¯
#gz.baseUrl.interface=https://gpo.gzggzy.cn/testinterface/v1/healthInsuranceInterface/heShanCity


gz.hospitalOrder.interface=/order/getPurchaseOrderInfo
gz.hospitalOrderItem.interface=/order/getPurchaseOrderDetail
gz.delivery.interface=/order/getDistributeInfo
gz.invoice.interface=/invoice/getInvoiceInfo
gz.stockIn.interface=/order/getWarehouseInfo
gz.drug.interface=/contract/getContractInfo
gz.drugDetail.interface=/procurecatalog/getProcurecatalogInfo
gz.accessToken.interface=/accessToken/getPlatformToken
gz.invoiceDetail.interface=/invoice/getInvoiceDetailInfo
gz.invoicePayInfo.interface=/procurecatalog/getInvoicePayInfo
gz.orderDelete.interface=/deleteOrder
gz.accessToken.orgUserName=gpoheshan
gz.accessToken.secret=15241X
#æ¯å¦å¨éæåæ°æ®
gz.interface.task.isall=1

#å®æ¶ä»»å¡å¼å³
jobFlag=1

pfx_storepass_yjs=jmhsybj
memberid=u049978
loginaccount=jmhsybj
jksfile=src/resources/cert/u049978.jks
jksfile2=/cert/u049978.jks
#service_path=http://183.63.72.94:8020/services/buyerservice?wsdl
service_path=http://119.147.171.238:9015/services/buyerservice?wsdl
## å¬å±
#base path
base_path=src/resources/

isSecret=false

server.name=server1


