#sign config
#jksfile=src/resources/certificate/u008000.jks
#jksfile2=/certificate/u008000.jks
#pfx_storepass_yjs=123456
#
#memberid=u008000
#loginaccount=2019haocaitest
#
##environment config
##local
##service_path=http://localhost:8080/ecps/services/hcbservice?wsdl
##test
#service_path=http://************:8000/services/hcbservice?wsdl
#
##file path
##base_path=src/file/
#base_path=src/resources/



## ï¿½ï¿½Ê½
pfx_storepass_yjs=jmsybjAa123456
memberid=u074720
loginaccount=jmsybjhc
jksfile=E:/whproject/jm/ssp-jm/ssp-jm-supervise/src/main/resources/certificate/u074720.jks
jksfile2=E:/whproject/jm/ssp-jm/ssp-jm-supervise/src/main/resources/certificate/u074720.jks
#service_path=http://************:8020/services/buyerservice?wsdl
service_path=http://***************:9016/services/hcbservice?wsdl
## ï¿½ï¿½ï¿½ï¿½
#base path
base_path=src/resources/