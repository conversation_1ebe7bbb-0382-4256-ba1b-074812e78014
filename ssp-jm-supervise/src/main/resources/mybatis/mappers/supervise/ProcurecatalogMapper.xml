<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ProcurecatalogMapper">
    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.Procurecatalog">
        select p.*,g.medicare_code,g.drug_standard_code as drugStandardCode ,g.national_drug_code as nationalDrugCode
        from drugpur_procurecatalog p LEFT join std_goods g on p.goods_id = g.goods_id
        where 1 = 1
        <if test=" procurecatalogId!=null and procurecatalogId !='' ">
            and  p.procurecatalog_id = #{procurecatalogId}
        </if>

       <if test=" productName!=null and productName !='' ">
            and  p.product_name = #{productName}
        </if>

        <if test=" outlook!=null and outlook !='' ">
            and  p.outlook = #{outlook}
        </if>

        <if test=" medicinemodel!=null and medicinemodel !='' ">
            and  p.medicinemodel = #{medicinemodel}
        </if>

        <if test="submitTimeFrom !=null">
            <![CDATA[ and  p.last_update_time  >=  #{submitTimeFrom}  ]]>
        </if>
        <if test="submitTimeTo !=null "  >
            <![CDATA[ and  p.last_update_time <=  #{submitTimeTo}  ]]>
        </if>
    </select>
</mapper>
