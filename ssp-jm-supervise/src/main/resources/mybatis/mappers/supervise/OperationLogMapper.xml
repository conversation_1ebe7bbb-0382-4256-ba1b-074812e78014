<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.OperationLogMapper">

    <select id="getList" parameterType="map" resultType="com.inspur.ssp.supervise.bean.entity.OperationLog">
        SELECT id,user_id,user_name,operation_name,invoice_no,file_name,code_type,code,
        sum(F_COUNT) as F_COUNT, sum(S_COUNT) as S_COUNT, batch,DATE_FORMAT(ADD_START_TIME, '%Y-%m-%d') as start_time,
        ADD_START_TIME,ADD_END_TIME,CREATE_TIME FROM operation_log
        <where>
            <if test="code != null and code != ''">
                and code=#{code}
            </if>
            <if test="codeType != null and codeType != ''">
                and code_type=#{codeType}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                and CREATE_TIME BETWEEN #{beginTime} AND #{endTime}
            </if>
            <if test="checked == 'true' "> <!--按树形结构展示统计-->
                GROUP BY code ,start_time
            </if>
<!--            <if test="checked == 'false' ">-->
<!--                GROUP BY code-->
<!--            </if>-->
            ORDER BY CREATE_TIME DESC
        </where>
    </select>


    <select id="getListUserLogin" parameterType="map" resultType="com.inspur.ssp.supervise.bean.entity.OperationLog">
        SELECT id,user_id,user_name,operation_name,invoice_no,file_name,code_type,code, count(*) as sumCount,
        batch,DATE_FORMAT(CREATE_TIME, '%Y-%m-%d') as start_time, ADD_START_TIME,ADD_END_TIME,CREATE_TIME
        FROM operation_log WHERE code_type='userLogin' and CREATE_TIME BETWEEN #{beginTime} AND #{endTime}

            <if test="checked == 'true' "> <!--按树形结构展示统计-->
                GROUP BY code ,start_time ,user_name
            </if>
            <if test="checked == 'false' ">
                GROUP BY code
            </if>

        ORDER BY CREATE_TIME DESC
    </select>

</mapper>
