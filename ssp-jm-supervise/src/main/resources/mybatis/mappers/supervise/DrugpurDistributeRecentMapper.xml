<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.DrugpurDistributeRecentMapper">

    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.DrugpurDistributeRecent">
        select * from  drugpur_distribute_recent

        <where>
            <if test="warehouseTimeFrom !=null ">
                <![CDATA[ and last_update_time  >=  #{warehouseTimeFrom}  ]]>
            </if>
            <if test="warehouseTimeTo !=null "  >
                <![CDATA[ and last_update_time <=  #{warehouseTimeTo}  ]]>
            </if>
            <if test="orderNum != null and orderNum != ''">
                and order_id =#{orderNum}
            </if>
            <if test="iswarehouse != null and iswarehouse != ''">
                and warehouse_count > 0
            </if>
        </where>
    </select>
</mapper>
