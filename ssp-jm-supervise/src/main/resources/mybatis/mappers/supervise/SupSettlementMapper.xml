<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupSettlementMapper">
    <select id="list" resultType="map">
        SELECT
            ss.*
        FROM
            sup_settlements   ss
        LEFT JOIN
            sup_settlement_item  ssi
        where sc.user_id = #{userId} and sc.ACTIVE = '1'
        <if test="num!=''and num!=null">
            AND s.num = #{num}
        </if>
        <if test="deliveryName!=''and deliveryName!=null">
            <bind name="tDeliveryName" value="'%' + deliveryName + '%'"/>
            AND s.CREATE_NAME like #{tDeliveryName}
        </if>
        <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
            AND s.CREATION_TIME between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="settlementTodo" resultType="map">
        SELECT
            sc.*
        FROM
            sup_settlement_course sc
        where sc.user_id = #{userId} and sc.ACTIVE = '1'
        <if test="num!=''and num!=null">
            AND s.num = #{num}
        </if>
        <if test="deliveryName!=''and deliveryName!=null">
            <bind name="tDeliveryName" value="'%' + deliveryName + '%'"/>
            AND s.CREATE_NAME like #{tDeliveryName}
        </if>
        <if test="startDate !=null and startDate !='' and endDate !=null and endDate !=''">
            AND s.CREATION_TIME between #{startDate} and #{endDate}
        </if>
    </select>
</mapper>
