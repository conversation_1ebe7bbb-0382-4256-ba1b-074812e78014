<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupInsuranceLogMapper">

    <select id="getInsuranceList" resultType="map">
        SELECT
            il.*,
            ii.*,
            i.*,
            c.NAME AS 'CATALOG_NAME',
            sii.STOCK_IN_TIME,
            e.COUNTRY,
            o.CREATION_TIME orderCreationTime,
            ( 25 - DATEDIFF( NOW(), sii.STOCK_IN_TIME )) overDay
        FROM
            sup_insurance_log il
        LEFT JOIN sup_invoice_item ii ON il.INVOICE_ITEM_ID = ii.ID
        LEFT JOIN sup_delivery_item di ON di.id = ii.DELIVERY_ITEM_ID
        INNER JOIN sup_order_item oi ON di.ORDER_ITEM_ID = oi.ID
        INNER JOIN sup_order o ON oi.ORDER_ID = o.ID
        LEFT JOIN sup_stock_in_item sii ON di.id = sii.DELIVERY_ITEM_ID
        LEFT JOIN sup_drug_detail e ON e.id = ii.drug_id
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_invoice i ON ii.INVOICE_ID = i.id
        <where>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND  c.NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="deductionCode != null and deductionCode != ''">
                and il.deduction_code = #{deductionCode}
            </if>
            <if test="bankCard != null and bankCard != ''">
                and il.bankCard = #{bankCard}
            </if>
            <if test="deliveryItemId != null and deliveryItemId != ''">
                and ii.DELIVERY_ITEM_ID = #{deliveryItemId}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and ii.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and ii.INVOICE_CODE = #{invoiceCode}
            </if>
            <if test="itemNo != null and itemNo != ''">
                and ii.ITEM_NO = #{itemNo}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and i.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and i.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and i.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
            </if>

        </where>
        order by i.INVOICE_DATE desc,i.HOSPITAL_NAME,ii.ITEM_NO desc
    </select>

</mapper>
