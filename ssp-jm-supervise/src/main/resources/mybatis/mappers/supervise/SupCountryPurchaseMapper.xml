<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupCountryPurchaseMapper">


    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.SupCountryPurchase">
        select * from  SUP_COUNTRY_PURCHASE
        <where>
            1=1
            <if test="hospitalId != null and hospitalId != ''">
                and HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="batch != null and batch != ''">
                and BATCH = #{batch}
            </if>
            <if test="countryTaskStatus != null and countryTaskStatus != ''">
                <if test='countryTaskStatus=="0"'>
                    and (TOTAL_STOCK_PURCHASE/PURCHASE)&lt;1
                </if>
                <if test='countryTaskStatus=="1"'>
                    and (TOTAL_STOCK_PURCHASE/PURCHASE)>=1
                </if>
            </if>
        </where>
    </select>
    <select id="selectCountryPurchaseList" resultType="map">
        SELECT q.* FROM (
        SELECT
        a.*,
        c.NAME CATALOG_NAME,
        D.SPECS,
        D.DOSAGE_FORM,
        D.MEDICAL_INSURANCE_CODE,
        H.NAME HOSPITAL_NAME,
        H.SORT_ORDER,
        D.STANDARD_CODE,
        Y.NAME COMPANY_NAME,
        IF(
        (CASE
        WHEN TIMESTAMPDIFF(DAY,a.TASK_END_TIME,SYSDATE())>0 THEN
        a.TOTAL_STOCK_PURCHASE/a.purchase - 1
        ELSE
        (a.TOTAL_STOCK_PURCHASE/a.purchase)-(TIMESTAMPDIFF(DAY,a.TASK_START_TIME,SYSDATE())/TIMESTAMPDIFF(DAY,a.TASK_START_TIME,a.TASK_END_TIME))
        END) >=0,'0','-1') AS 'TASK_STATUS'
        FROM
        SUP_COUNTRY_PURCHASE a
        LEFT JOIN sup_drug_detail D ON a.DRUG_ID = D.ID
        LEFT JOIN sup_drug_catalog c on c.id=D.CATALOG_ID
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        LEFT JOIN sup_drug_company y on D.DRUG_COMPANY_ID=y.ID
        <where>
            1=1
            <if test="regionId != null and regionId != ''">
                and H.REGION_ID = #{regionId}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and a.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="taskStartTime != null and taskStartTime != ''">
                and  DATE_FORMAT( a.TASK_START_TIME, "%Y-%m-%d" ) = #{taskStartTime}
            </if>
            <if test="taskNum != null and taskNum != ''">
                and a.TASK_NUM = #{taskNum}
            </if>
            <if test="batch != null and batch != ''">
                and a.BATCH = #{batch}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and h.name like #{hHospitalName}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dCatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{dCatalogName}
            </if>
            <if test="countryTaskStatus != null and countryTaskStatus != ''">
                <if test='countryTaskStatus=="0"'>
                    and (a.TOTAL_STOCK_PURCHASE/a.PURCHASE)&lt;1
                </if>
                <if test='countryTaskStatus=="1"'>
                    and (a.TOTAL_STOCK_PURCHASE/a.PURCHASE)>=1
                </if>
                <if test='countryTaskStatus=="3"'>
                    and (a.TOTAL_DELIVERY_PURCHASE/a.PURCHASE)&lt;1
                </if>
                <if test='countryTaskStatus=="4"'>
                    and (a.TOTAL_DELIVERY_PURCHASE/a.PURCHASE)>=1
                </if>
            </if>
        </where>
        ) q
        <where>
            <if test="taskStatus != null and taskStatus != ''">
                and q.TASK_STATUS = #{taskStatus}
            </if>
        </where>
        order by  q.SORT_ORDER ASC,q.TASK_NUM DESC
    </select>



    <select id="selectListToExcel" parameterType="map"  resultType="com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseExcel">

        SELECT q.* FROM (
        SELECT
        a.*,
        c.NAME CATALOG_NAME,
        D.SPECS,
        D.DOSAGE_FORM,
        H.NAME HOSPITAL_NAME,
        R.REGION_NAME,
        H.SORT_ORDER,
        D.STANDARD_CODE,
        DB.BATCH_NAME,
        dc.NAME DRUG_COMPANY_NAME,
        IF(
        (CASE
        WHEN TIMESTAMPDIFF(DAY,a.TASK_END_TIME,SYSDATE())>0 THEN
        a.TOTAL_STOCK_PURCHASE/a.purchase - 1
        ELSE
        (a.TOTAL_STOCK_PURCHASE/a.purchase)-(TIMESTAMPDIFF(DAY,a.TASK_START_TIME,SYSDATE())/TIMESTAMPDIFF(DAY,a.TASK_START_TIME,a.TASK_END_TIME))
        END) >=0,'0','-1') AS 'TASK_STATUS'
        FROM
        SUP_COUNTRY_PURCHASE a
        LEFT JOIN sup_drug_detail D ON a.DRUG_ID = D.ID
        LEFT JOIN sup_drug_company dc ON D.DRUG_COMPANY_ID=dc.ID
        LEFT JOIN sup_drug_catalog c on c.id=D.CATALOG_ID
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        LEFT JOIN sup_region_info R ON H.REGION_ID=R.REGION_CODE
        LEFT JOIN sup_drug_batch DB ON A.BATCH = DB.CODE
        <where>
            1=1
            <if test="regionId != null and regionId != ''">
                and H.REGION_ID = #{regionId}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and a.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="taskStartTime != null and taskStartTime != ''">
                and  DATE_FORMAT( a.TASK_START_TIME, "%Y-%m-%d" ) = #{taskStartTime}
            </if>
            <if test="taskNum != null and taskNum != ''">
                and a.TASK_NUM = #{taskNum}
            </if>
            <if test="batch != null and batch != ''">
                and a.BATCH = #{batch}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and h.name like #{hHospitalName}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dCatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{dCatalogName}
            </if>
            <if test="countryTaskStatus != null and countryTaskStatus != ''">
                <if test='countryTaskStatus=="0"'>
                    and (a.TOTAL_STOCK_PURCHASE/a.PURCHASE)&lt;1
                </if>
                <if test='countryTaskStatus=="1"'>
                    and (a.TOTAL_STOCK_PURCHASE/a.PURCHASE)>1
                </if>
                <if test='countryTaskStatus=="3"'>
                    and (a.TOTAL_DELIVERY_PURCHASE/a.PURCHASE)&lt;1
                </if>
                <if test='countryTaskStatus=="4"'>
                    and (a.TOTAL_DELIVERY_PURCHASE/a.PURCHASE)>=1
                </if>
            </if>
        </where>
        ) q
        <where>
            <if test="taskStatus != null and taskStatus != ''">
                and q.TASK_STATUS = #{taskStatus}
            </if>
        </where>
        order by  q.SORT_ORDER ASC,q.TASK_NUM DESC
    </select>



    <select id="selectPurchaseInvoiceToExcel" parameterType="map"  resultType="com.inspur.ssp.supervise.bean.dto.purchase.SupPurchaseInvoiceExcel">

        SELECT q.* FROM (
        SELECT
        a.*,
        c.NAME CATALOG_NAME,
        D.SPECS,
        D.DOSAGE_FORM,
        H.NAME HOSPITAL_NAME,
        H.SORT_ORDER,
        b.BATCH_NAME,
        IF(
        (CASE
        WHEN TIMESTAMPDIFF(DAY,a.TASK_END_TIME,SYSDATE())>0 THEN
        a.TOTAL_STOCK_PURCHASE/a.purchase - 1
        ELSE
        (a.TOTAL_STOCK_PURCHASE/a.purchase)-(TIMESTAMPDIFF(DAY,a.TASK_START_TIME,SYSDATE())/TIMESTAMPDIFF(DAY,a.TASK_START_TIME,a.TASK_END_TIME))
        END) >=0,'0','-1') AS 'TASK_STATUS'
        FROM
        SUP_COUNTRY_PURCHASE a
        LEFT JOIN sup_drug_detail D ON a.DRUG_ID = D.ID
        LEFT JOIN sup_drug_catalog c on c.id=D.CATALOG_ID
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        LEFT JOIN sup_drug_batch b ON a.BATCH=b.CODE
        <where>
            1=1
            <if test="hospitalId != null and hospitalId != ''">
                and a.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="taskStartTime != null and taskStartTime != ''">
                and  DATE_FORMAT( a.TASK_START_TIME, "%Y-%m-%d" ) = #{taskStartTime}
            </if>
            <if test="taskNum != null and taskNum != ''">
                and a.TASK_NUM = #{taskNum}
            </if>
            <if test="batch != null and batch != ''">
                and a.BATCH = #{batch}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and h.name like #{hHospitalName}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dCatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{dCatalogName}
            </if>
        </where>
        ) q
        <where>
            <if test="taskStatus != null and taskStatus != ''">
                and q.TASK_STATUS = #{taskStatus}
            </if>
        </where>
        order by  q.SORT_ORDER ASC,q.TASK_NUM DESC


    </select>

    <select id="selectListByHospital" resultType="map">
        select  H.NAME HOSPITAL_NAME,a.HOSPITAL_ID,A.BATCH,a.TASK_START_TIME,a.TASK_END_TIME ,sum(a.HAVE_PAY) HAVE_PAY,
        sum(a.TOTAL_PRICE) TOTAL_PRICE,  sum(a.TOTAL_STOCK_PURCHASE) TOTAL_STOCK_PURCHASE,  sum(a.PURCHASE) PURCHASE
        from (SELECT
        P.HOSPITAL_ID,
        p.TASK_START_TIME,
        p.TASK_END_TIME,
        IFNULL(sum(t.TAXES_AMOUNT),0) as HAVE_PAY,
        p.DRUG_ID,
        p.TOTAL_PRICE,
        P.TOTAL_STOCK_PURCHASE,
        P.PURCHASE,
        p.BATCH
        FROM
        SUP_COUNTRY_PURCHASE P
        LEFT JOIN (
        SELECT
        i.id AS INVOICE_ID,
        s.HOSPITAL_ID AS HOSPITAL_ID,
        i.TAXES_AMOUNT AS TAXES_AMOUNT,
        i.PAY_TIME AS PAY_TIME,
        i.DRUG_ID,
        o.SUBMIT_TIME AS submitTime
        FROM
        sup_invoice_item i
        LEFT JOIN sup_invoice s ON i.INVOICE_ID = s.ID
        LEFT JOIN sup_drug_detail d ON i.drug_id = d.id
        left join sup_order o on i.ORDER_CODE = o.ORDER_NUM
        WHERE
        i.pay_status = '1'
        ) t ON p.DRUG_ID = t.DRUG_ID
        AND p.HOSPITAL_ID = t.HOSPITAL_ID
        AND t.submitTime BETWEEN p.TASK_START_TIME
        AND p.TASK_END_TIME
        <where>
            <if test="batch != null and batch != ''">
                and   P.BATCH = #{batch}
            </if>
        </where>
        GROUP BY p.ID) a
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                and a.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="regionId != null and regionId != ''">
                and H.REGION_ID = #{regionId}
            </if>
        </where>
        group by a.HOSPITAL_ID,a.TASK_START_TIME,a.TASK_END_TIME
        ORDER BY H.SORT_ORDER ASC
    </select>

    <select id="batchSummary" resultType="map">
        SELECT
        H.NAME AS 'hospitalName',
        a.BATCH AS 'countryBatch',
        b.BATCH_NAME 'batchName',
        SUM(a.PURCHASE) AS 'purchase',
        SUM(a.TOTAL_STOCK_PURCHASE) AS 'totalPurchase',
        SUM(a.TOTAL_DELIVERY_PURCHASE) AS 'totalDeliveryPurchase',
        SUM(a.TOTAL_PRICE) AS 'countryPrice',
        a.TASK_START_TIME AS 'startTime',
        a.TASK_END_TIME AS 'endTime'

        FROM
        SUP_COUNTRY_PURCHASE a
        LEFT JOIN sup_drug_batch b ON a.BATCH = b.CODE
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        <where>

            <if test="startTime !=null and startTime !='' and startTime!='undefined'">
                AND a.TASK_START_TIME=#{startTime}
            </if>
            <if test="endTime !=null and endTime !='' and  endTime!='undefined'">
                AND a.TASK_END_TIME=#{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and H.NAME like #{dhospitalName}
            </if>
            <if test="regionId != null and regionId != ''">
                and H.REGION_ID = #{regionId}
            </if>
            <if test="batch != null and batch != ''">
                and a.BATCH in
                <foreach collection="batchs" item="batch" index="index" open="(" close=")" separator=",">
                    #{batch}
                </foreach>
            </if>
        </where>
        GROUP BY H.NAME,a.BATCH
        ORDER BY H.SORT_ORDER,a.TASK_START_TIME
    </select>

    <select id="hospitalBatchSummary" resultType="map">
        SELECT
        H.NAME AS 'hospitalName',
        a.BATCH AS 'countryBatch',
        b.BATCH_NAME 'batchName',
        SUM(DISTINCT a.PURCHASE) AS 'purchase',
        SUM(DISTINCT a.TOTAL_STOCK_PURCHASE) AS 'totalPurchase',
        SUM(DISTINCT a.TOTAL_PRICE) AS 'countryPrice'

        FROM
        SUP_COUNTRY_PURCHASE a
        LEFT JOIN sup_drug_batch b ON a.BATCH = b.CODE
        LEFT JOIN SUP_HOSPITAL H ON a.HOSPITAL_ID = H.ID
        <where>
            <if test="startTime !=null and startTime !='' and startTime!='undefined'">
                AND a.TASK_START_TIME=#{startTime}
            </if>
            <if test="endTime !=null and endTime !='' and  endTime!='undefined'">
                AND a.TASK_END_TIME=#{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and H.NAME like #{dhospitalName}
            </if>
            <if test="batch != null and batch != ''">
                and a.BATCH = #{batch}
            </if>
        </where>
        GROUP BY H.NAME,a.BATCH
        ORDER BY  a.BATCH
    </select>

</mapper>
