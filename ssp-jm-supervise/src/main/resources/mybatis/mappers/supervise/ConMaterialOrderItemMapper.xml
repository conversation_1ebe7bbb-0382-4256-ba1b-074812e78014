<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialOrderItemMapper">


    <select id="getOrderItemList" resultType="map">
        SELECT
        i.*,
        i.HOSPITAL_NAME,
        e.REG_CRED_NUM,
        e.REG_CRED_SPEC,
        e.REG_CRED_NAME,
        e.REG_CRED_MODEL,
        e.DOSAGE_FORM,
        y.NAME AS 'drugCompanyName',
        e.NAME AS 'catalogName',
        i.ORDER_NUM AS 'orderNum',
        i.SUBMIT_TIME,
        b.BATCH_NAME AS 'meterialBatchName'
        FROM
            con_material_order_item i
            LEFT JOIN con_material_detail e ON e.id = i.DETAIL_ID
            LEFT JOIN sup_drug_company Y ON e.CON_COMPANY_ID=y.ID
            LEFT JOIN con_material_batch b ON i.COUNTRY_BATCH=b.CODE
        <where>
            <if test="regionId != null and regionId != ''">
                AND i.REGION_ID = #{regionId}
            </if>
            <if test="systemContrast !=null and systemContrast !=''">
                AND i.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
            </if>
            <if test="orderItemStatus != null and orderItemStatus != ''">
                AND i.ORDER_ITEM_STATUS = #{orderItemStatus}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                AND i.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="payStatus != null and payStatus != ''">
                AND i.PAY_STATUS = #{payStatus}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="catalogName" value="'%'+catalogName+'%'"/>
                AND i.DETAIL_NAME like #{catalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                <bind name="horderCode" value="'%'+orderCode+'%'"/>
                AND i.ORDER_NUM like #{horderCode}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                AND i.HOSPITAL_NAME = #{hospitalName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="batch !=null and batch !=''">
                AND i.COUNTRY_BATCH = #{batch}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="specs != null and specs != ''">
                <bind name="dSpecs" value="'%'+specs+'%'"/>
                AND e.SPECS like #{dSpecs}
            </if>
            <if test="drugCompanyName != null and drugCompanyName != ''">
                <bind name="dDrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND y.Name like #{dDrugCompanyName}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="regCredNum != null and regCredNum != ''">
                AND e.REG_CRED_NUM = #{regCredNum}
            </if>
            <if test="regCredSpec != null and regCredSpec != ''">
                AND e.REG_CRED_SPEC = #{regCredSpec}
            </if>
            <if test="regCredName != null and regCredName != ''">
                AND e.REG_CRED_NAME = #{regCredName}
            </if>
            <if test="regCredModel != null and regCredModel != ''">
                AND e.REG_CRED_MODEL = #{regCredModel}
            </if>
        </where>
        order by i.SUBMIT_TIME desc,i.HOSPITAL_NAME desc

    </select>


    <select id="getOrderItemListToExcel" resultType="com.inspur.ssp.supervise.bean.dto.purchase.ConMaterialOrderItemExportExcel" parameterType="map">
        SELECT
        i.*,
        i.HOSPITAL_NAME,
        e.REG_CRED_NUM ,
        e.REG_CRED_SPEC ,
        e.REG_CRED_NAME ,
        e.REG_CRED_MODEL ,
        e.DOSAGE_FORM,
        y.NAME AS 'drugCompanyName',
        e.NAME AS 'catalogName',
        i.ORDER_NUM AS 'orderNum',
        i.SUBMIT_TIME,
        b.BATCH_NAME AS 'meterialBatchName'
        FROM
        con_material_order_item i
        LEFT JOIN con_material_detail e ON e.id = i.DETAIL_ID
        LEFT JOIN sup_drug_company Y ON e.CON_COMPANY_ID=y.ID
        LEFT JOIN con_material_batch b ON i.COUNTRY_BATCH=b.CODE
        <where>
            <if test="regionId != null and regionId != ''">
                AND i.REGION_ID = #{regionId}
            </if>
            <if test="systemContrast !=null and systemContrast !=''">
                AND i.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
            </if>
            <if test="orderItemStatus != null and orderItemStatus != ''">
                AND i.ORDER_ITEM_STATUS = #{orderItemStatus}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                AND i.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="payStatus != null and payStatus != ''">
                AND i.PAY_STATUS = #{payStatus}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="catalogName" value="'%'+catalogName+'%'"/>
                AND i.DETAIL_NAME like #{catalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                <bind name="horderCode" value="'%'+orderCode+'%'"/>
                AND i.ORDER_NUM like #{horderCode}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                AND i.HOSPITAL_NAME = #{hospitalName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="batch !=null and batch !=''">
                AND i.COUNTRY_BATCH = #{batch}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="specs != null and specs != ''">
                <bind name="dSpecs" value="'%'+specs+'%'"/>
                AND e.SPECS like #{dSpecs}
            </if>
            <if test="drugCompanyName != null and drugCompanyName != ''">
                <bind name="dDrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND y.Name like #{dDrugCompanyName}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="regCredNum != null and regCredNum != ''">
                AND e.REG_CRED_NUM = #{regCredNum}
            </if>
            <if test="regCredSpec != null and regCredSpec != ''">
                AND e.REG_CRED_SPEC = #{regCredSpec}
            </if>
            <if test="regCredName != null and regCredName != ''">
                AND e.REG_CRED_NAME = #{regCredName}
            </if>
            <if test="regCredModel != null and regCredModel != ''">
                AND e.REG_CRED_MODEL = #{regCredModel}
            </if>
        </where>
        order by i.SUBMIT_TIME desc,i.HOSPITAL_NAME desc

    </select>

    <select id="getByPurchaseMaterialId" resultType="com.inspur.ssp.supervise.bean.vo.PurchaseDrugDetailVo"
            parameterType="com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto">
        SELECT
        T1.*,
        sdi.DELIVERY_CODE,
        DATE_FORMAT( T1.Last_SubmitTime, '%Y-%m-%d' ) AS SUBMIT_TIME
        FROM
        (
        SELECT
        soi.`CODE`,
        soi.DETAIL_ID,
        soi.HOSPITAL_NAME,
        soi.UNIT_PRICE,
        soi.ORDER_NUM,
        soi.DETAIL_NAME,
        MAX( soi.SUBMIT_TIME ) AS Last_SubmitTime,
        soi.SOURCE,
        soi.ORDER_ITEM_STATUS
        FROM
        con_material_order_item soi
        <where>
            <if test="detailId!=null and detailId!=''">
                soi.DETAIL_ID = #{detailId}
            </if>
            <if test="unitPrice!=null and unitPrice!=''">
                AND soi.UNIT_PRICE like concat('%', #{unitPrice},'%')
            </if>
            <if test="source!=null and source!=''">
                AND soi.source =#{source}
            </if>
            <if test="hospitalName!=null and hospitalName!=''">
                AND soi.HOSPITAL_NAME like concat('%', #{hospitalName},'%')
            </if>
            <if test="submitTime!=null and submitTime!=''">
                AND SUBMIT_TIME BETWEEN #{startSubmitTime} and #{endSubmitTime}
            </if>
        </where>
        GROUP BY
        soi.HOSPITAL_NAME,
        soi.UNIT_PRICE
        ) AS T1
        inner JOIN con_material_delivery_item sdi ON sdi.ORDER_ITEM_NO = T1.`CODE`
        order by SUBMIT_TIME desc
    </select>



    <select id="getMaterialSourceAmountByHospital" resultType="com.inspur.ssp.supervise.bean.vo.MaterialSourceAmountVo">
        SELECT
        sri.REGION_NAME AS 'regionName',
        o.HOSPITAL_NAME AS 'hospitalName',
        sum(
        IFNULL( s.ITEM_PRICE, 0 )) AS 'totalPrice',
        sum( IFNULL( s.AMOUNT, 0 ) * d.FACTOR ) AS 'totalPackingAmount',
        sum(
        IF
        ( d.COUNTRY = '1', s.ITEM_PRICE, 0 )) AS 'countryPrice',
        sum(IFNULL(  d.FACTOR, 0 )  *
        IF
        ( d.COUNTRY = '1', IFNULL( s.AMOUNT, 0 ) , 0 )) ' countryPackingAmount',
        sum(
        IF
        ( d.COUNTRY = '0', s.ITEM_PRICE, 0 )) AS 'noCountryPrice',
        sum(IFNULL(  d.FACTOR, 0 )  *
        IF
        ( d.COUNTRY = '0', IFNULL( s.AMOUNT, 0 ) , 0 )) 'noCountryPackingAmount'
        FROM
        con_material_order_item s
        LEFT JOIN con_material_order o ON o.id = s.order_id
        LEFT JOIN sup_hospital h on h.ID=o.HOSPITAL_ID
        LEFT JOIN con_material_detail d on s.DETAIL_ID=d.id
        LEFT JOIN sup_region_info sri on sri.REGION_CODE = s.REGION_ID
        <where>
            s.ORDER_ITEM_STATUS NOT IN ('5')
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source !=null and source !='' ">
                AND o.SOURCE =#{source}
            </if>
            <if test="attribute !=null and attribute !=''">
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
            <if test="regionId != null and regionId != ''">
                and s.REGION_ID = #{regionId}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and h.id = #{hospitalId}
            </if>
            <if test="userHospitalId != null and userHospitalId != ''">
                and o.HOSPITAL_ID = #{userHospitalId}
            </if>

        </where>
        GROUP BY
        o.HOSPITAL_NAME
        ORDER BY h.SORT_ORDER ASC
    </select>



    <select id="getMaterialPurchaseList" resultType="map">
        SELECT
        i.*,
        i.HOSPITAL_NAME,
        e.REG_CRED_NUM,
        e.REG_CRED_SPEC,
        e.REG_CRED_NAME,
        e.REG_CRED_MODEL,
        e.DOSAGE_FORM,
        y.NAME AS 'drugCompanyName',
        e.NAME AS 'catalogName',
        i.ORDER_NUM AS 'orderNum',
        i.SUBMIT_TIME,
        b.BATCH_NAME AS 'meterialBatchName'
        FROM
        con_material_order_item i
        INNER JOIN con_material_detail e ON e.id = i.DETAIL_ID
        INNER JOIN sup_drug_company Y ON e.CON_COMPANY_ID=y.ID
        INNER JOIN con_material_batch b ON i.COUNTRY_BATCH=b.CODE
        INNER JOIN con_material_delivery_item cmdi ON cmdi.ORDER_ITEM_NO = i.CODE
        <where>
            <if test="regionId != null and regionId != ''">
                AND i.REGION_ID = #{regionId}
            </if>
            <if test="systemContrast !=null and systemContrast !=''">
                AND i.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
            </if>
            <if test="orderItemStatus != null and orderItemStatus != ''">
                AND i.ORDER_ITEM_STATUS = #{orderItemStatus}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                AND i.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="payStatus != null and payStatus != ''">
                AND i.PAY_STATUS = #{payStatus}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="catalogName" value="'%'+catalogName+'%'"/>
                AND i.DETAIL_NAME like #{catalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                <bind name="horderCode" value="'%'+orderCode+'%'"/>
                AND i.ORDER_NUM like #{horderCode}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                AND i.HOSPITAL_NAME = #{hospitalName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="batch !=null and batch !=''">
                AND i.COUNTRY_BATCH = #{batch}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="specs != null and specs != ''">
                <bind name="dSpecs" value="'%'+specs+'%'"/>
                AND e.SPECS like #{dSpecs}
            </if>
            <if test="drugCompanyName != null and drugCompanyName != ''">
                <bind name="dDrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND y.Name like #{dDrugCompanyName}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                AND i.id = #{orderItemId}
            </if>
            <if test="regCredNum != null and regCredNum != ''">
                AND e.REG_CRED_NUM = #{regCredNum}
            </if>
            <if test="regCredSpec != null and regCredSpec != ''">
                AND e.REG_CRED_SPEC = #{regCredSpec}
            </if>
            <if test="regCredName != null and regCredName != ''">
                AND e.REG_CRED_NAME = #{regCredName}
            </if>
            <if test="regCredModel != null and regCredModel != ''">
                AND e.REG_CRED_MODEL = #{regCredModel}
            </if>
--             GROUP BY i.DETAIL_ID
        </where>

        order by i.SUBMIT_TIME desc,i.HOSPITAL_NAME desc

    </select>
</mapper>
