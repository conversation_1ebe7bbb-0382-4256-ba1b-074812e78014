<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.DrugpurInvoiceMapper">

    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.DrugpurInvoice">
        select t.*,a.purchase_price,a.purchase_count from  drugpur_invoice t inner join drugpur_distribute_recent a on a.invoice_code = t.invoice_code and a.invoice_id = t.invoice_id

        <where>
            <if test="distributeTimeFrom !=null  ">
                <![CDATA[ and t.last_update_time  >=  #{distributeTimeFrom}  ]]>
            </if>
            <if test="distributeTimeTo !=null "  >
                <![CDATA[ and t.last_update_time <=  #{distributeTimeTo}  ]]>
            </if>
             <if test="invoice_id !=null  and invoice_id != ''"  >
                 and t.invoice_id = #{invoice_id}
            </if>
        </where>
    </select>
</mapper>
