<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupOrderItemMapper">
    <select id="getOverDeliveryOrderItem" resultType="com.inspur.ssp.supervise.bean.entity.SupOrderItem">
        SELECT
        o.ID,
        o.ORDER_ID,
        o.CATALOG_ID,
        o.CATALOG_NAME,
        o.DETAIL_ID,
        o.DRUG_VERSION,
        o.SOURCE_ID,
        o.SOURCE,
        o.BUSI_CODE,
        o.PRICE_ID,
        o.UNIT_PRICE,
        o.AMOUNT,
        o.ITEM_PRICE,
        o.SYSTEM_CONTRAST,
        o.REASON,
        o.CREATION_TIME,
        o.CREATOR,
        o.LAST_MODIFITOR,
        o.LAST_MODIFITION_TIME,
        o.STATUS,
        o.CONTRACT_DETAILS_NUMBER,
        o.CODE,
        o.CONTRACT_NUMBER,
        o.ORDER_NUM,
        o.ORDER_ITEM_STATUS,
        o.PAY_STATUS,
        o.STOCK_STATUS,
        o.WARNING,
        o.PID,
        (DATEDIFF( NOW(), o.CREATION_TIME )) AS DELIVERY_SURPLUS,
        di.ORDER_ITEM_ID
        FROM
        sup_order_item o
        left join sup_drug_detail e on e.id=o.DETAIL_ID
        left join sup_delivery_item di on di.ORDER_ITEM_ID = o.id
        WHERE
        o.ORDER_ITEM_STATUS NOT IN ('5') and  di.ORDER_ITEM_ID is null
        <if test="isCountry != null and isCountry != ''">
            and  e.COUNTRY = #{isCountry}
        </if>
        <if test="submitFrom != null and submitFrom != ''">
            and  o.SUBMIT_TIME > #{submitFrom}
        </if>
    </select>

    <select id="getOverDeliveryList" resultType="map">
        SELECT
        i.ID,i.ORDER_ID,i.CATALOG_ID,I.CATALOG_NAME,i.DETAIL_ID,i.SOURCE_ID,i.SOURCE,i.BUSI_CODE,i.PRICE_ID,
        i.UNIT_PRICE,i.AMOUNT,i.ITEM_PRICE,i.SYSTEM_CONTRAST,i.REASON,i.CREATION_TIME,i.STATUS,i.CODE,i.CONTRACT_NUMBER,
        i.ORDER_NUM,i.ORDER_ITEM_STATUS,i.PAY_STATUS,i.STOCK_STATUS,i.WARNING,i.PID,i.COUNTRY_TAG,i.COUNTRY_BATCH,i.SUBMIT_TIME,
        o.HOSPITAL_NAME,
        o.HOSPITAL_ID,
        o.DELIVERY_COMPANY,
        e.CODE eCode,e.COMPANY_ID,e.COUNTRY,e.COUNTRY_TYPE,e.ELECTION_SCOPE,e.DOSAGE_FORM,e.GOODS_NAME,
        e.SPECS,e.PACKING,e.PACKING_SPECS,e.SPECS_ATTR,e.UNIT,e.APPROVAL_NUMBER,e.STANDARD_CODE,
        min(d.DELIVERY_TIME) DELIVERY_TIME
        FROM
        sup_order_item i
        LEFT JOIN sup_order o ON o.ORDER_NUM=i.ORDER_NUM
        LEFT JOIN sup_drug_detail e ON e.id = i.DETAIL_ID
        LEFT JOIN sup_delivery_item d on  d.order_item_id = i.ID
        WHERE
        i.DELIVERY_SURPLUS > 0
        and i.ORDER_ITEM_STATUS != '5'
        <if test="regionId != null and regionId != ''">
            and i.REGION_ID = #{regionId}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
            and o.HOSPITAL_NAME like #{hHospitalName}
        </if>
        <if test="orderItemId != null and orderItemId != ''">
            and i.id = #{orderItemId}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and o.HOSPITAL_ID = #{hospitalId}
        </if>
        <if test="country != null and country != ''">
            <if test='country =="1"'>
                and  i.COUNTRY_TAG = '1'
            </if>
            and  e.COUNTRY = #{country}
        </if>
        <if test="orderCode != null and orderCode != ''">
            and o.ORDER_NUM = #{orderCode}
        </if>
        <if test="orderStartTime !=null and orderStartTime !='' and orderEndTime !=null and orderEndTime !=''">
            AND o.SUBMIT_TIME BETWEEN #{orderStartTime} and #{orderEndTime}
        </if>
        <if test="decide != null and decide != ''">
            <if test = "decide == '0'.toString()">
                and d.DELIVERY_TIME is null
            </if>
            <if test = "decide == '1'.toString()">
                and d.DELIVERY_TIME is not null
            </if>
        </if>
        group by id
    </select>


    <select id="getPurchaseAmountBySource" resultType="Map">
        SELECT s.SOURCE as 'source',sum(ITEM_PRICE) as 'amount' ,
        d.ITEM_NAME as 'sourceName'from sup_order_item s
        LEFT JOIN (SELECT ITEM_VALUE,ITEM_NAME from SUP_DICT_ITEM
        WHERE DICT_ID=( SELECT ID FROM SUP_DICT WHERE CODE='SOURCE')) d
        ON s.SOURCE=d.ITEM_VALUE
        LEFT JOIN sup_order o ON o.id = s.order_id
        <where>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
        </where>
        GROUP BY s.SOURCE
    </select>

    <select id="getOrderItemByHospitalIdAndDetailId" resultType="map">
        select i.id from sup_order_item i left join sup_order o on  i.ORDER_ID = o.id

        where 1 = 1 and i.DETAIL_ID in
        <foreach collection="drugIds" item="drugId" index="index" open="(" close=")" separator=",">
            #{drugId}
        </foreach>

        and o.HOSPITAL_ID=#{hospitalId}
        <if test="batch != null and batch != ''">
            and i.COUNTRY_BATCH=#{batch}
        </if>
    </select>


    <select id="getOrderItemPurchase" resultType="map">
        SELECT
        i.*,
        IFNULL( i.AMOUNT, 0 ) orderSumNum,
        IFNULL( i.ITEM_PRICE, 0 ) sumOrderAmount,
        IFNULL( p.sumTaxesAmount, 0 ) sumTaxesAmount,
        IFNULL( p.sumNum, 0 ) stockSumNum,
        IFNULL( q.sumPay, 0 ) sumPay,
        IFNULL( d.deliverySumNum, 0 ) deliverySumNum,
        IFNULL( d.sumDeliveryAmount,0) sumDeliveryAmount
        FROM
        sup_order_item i
        LEFT JOIN sup_order o ON i.ORDER_ID = o.id
        LEFT JOIN (
        SELECT
        ii.ORDER_ITEM_ID,
        sum(IF(ii.PAY_STATUS = '1',ii.PAY_PRICE,0)) sumPay
        FROM
        sup_invoice_item ii where ORDER_ITEM_ID in
        <foreach collection="orderItemIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        GROUP BY
        ii.ORDER_ITEM_ID
        ) q ON i.id = q.ORDER_ITEM_ID
        LEFT JOIN (
        SELECT
        sii.ORDER_ITEM_ID,
        SUM( sii.AMOUNT ) sumTaxesAmount,
        SUM( sii.NUM ) sumNum
        FROM
        sup_stock_in_item sii where ORDER_ITEM_ID in
        <foreach collection="orderItemIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        GROUP BY
        sii.ORDER_ITEM_ID
        ) p ON i.id = p.ORDER_ITEM_ID

        LEFT JOIN (
        SELECT
        dii.ORDER_ITEM_ID,
        SUM( dii.AMOUNT ) sumDeliveryAmount,
        SUM( dii.NUM ) deliverySumNum
        FROM
        sup_delivery_item dii where ORDER_ITEM_ID in
        <foreach collection="orderItemIds" item="itemId" index="index" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        GROUP BY
        dii.ORDER_ITEM_ID
        ) d ON i.id = d.ORDER_ITEM_ID

        WHERE
        i.DETAIL_ID in
        <foreach collection="drugIds" item="drugId" index="index" open="(" close=")" separator=",">
            #{drugId}
        </foreach>
        and o.HOSPITAL_ID=#{hospitalId}
        <if test="batch != null and batch != ''">
            and i.COUNTRY_BATCH=#{batch}
        </if>
        <if test="taskStartTime != null  and taskEndTime != null">
            and o.SUBMIT_TIME &gt;= #{taskStartTime}
            and o.SUBMIT_TIME &lt; #{taskEndTime}
        </if>

    </select>

    <select id="getDrugAmountByMonth" resultType="map">
        SELECT
        t.*,
        IF(t.year_ratio &gt; #{grewExceptionRate} or t.year_ratio &lt; -#{grewExceptionRate}, '1','0') grew_Exception,
        IF(t.month_ratio &gt; #{chainExceptionRate} or t.month_ratio &lt; -#{chainExceptionRate}, '1','0') chain_Exception
        from (
        SELECT
        now_sale.DETAIL_ID,
        now_sale.CATALOG_NAME,
        now_sale.now_time,
        now_sale.now_year,
        d.DOSAGE_FORM,
        d.SPECS,
        d.PACKING_SPECS,
        CASE

        WHEN a_drug_item_price IS NULL
        OR a_drug_item_price = 0 THEN
        0 ELSE a_drug_item_price
        END this_drug_item_price,
        CASE

        WHEN a_drug_sum IS NULL
        OR a_drug_sum = 0 THEN
        0 ELSE a_drug_sum
        END this_drug_sum,
        CASE

        WHEN b_drug_sum IS NULL
        OR b_drug_sum = 0 THEN
        0 ELSE b_drug_sum
        END last_month_drug_sum,
        CASE

        WHEN last_year_drug_sum IS NULL
        OR last_year_drug_sum = 0 THEN
        0 ELSE last_year_drug_sum
        END last_year_drug_sum,

        CASE

        WHEN b_drug_sum IS NULL
        OR b_drug_sum = 0 THEN
        0 ELSE ( CONVERT ( ( ( a_drug_sum - b_drug_sum ) / b_drug_sum ) * 100, DECIMAL ( 10, 2 ) ) )
        END month_ratio,
        CASE

        WHEN last_year_drug_sum IS NULL
        OR last_year_drug_sum = 0 THEN
        0 ELSE ( CONVERT ( ( ( a_drug_sum - last_year_drug_sum ) / last_year_drug_sum ) * 100, DECIMAL ( 10, 2 ) ) )
        END year_ratio
        FROM
        (SELECT
        date_format(o.SUBMIT_TIME,'%Y-%m') as now_time,
        date_format(o.SUBMIT_TIME, '%Y' ) AS now_year,
        sum(o.AMOUNT) a_drug_sum,
        sum(o.ITEM_PRICE) a_drug_item_price,
        o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{year}
            </if>
            <if test="month != null and month != ''">
                and  MONTH(o.SUBMIT_TIME)=#{month}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="regionId != null and regionId != ''">
                and o.HOSPITAL_ID in (select id from sup_hospital where region_id = #{regionId})
            </if>
        </where>
        GROUP BY date_format(o.SUBMIT_TIME,'%Y-%m'),date_format( o.SUBMIT_TIME, '%Y' ),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        ORDER BY
        date_format(o.SUBMIT_TIME, '%Y-%m' ) ASC) now_sale
        left join
        (SELECT
        date_format(DATE_ADD(o.SUBMIT_TIME,INTERVAL 1 MONTH ), '%Y-%m') 	as now_time,
        sum(o.AMOUNT) b_drug_sum,
        o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                <choose>
                    <when test="acrossYear != null and acrossYear != ''">
                        and  YEAR(o.SUBMIT_TIME) =#{lastYear}
                    </when>
                    <otherwise>
                        and  YEAR(o.SUBMIT_TIME) =#{year}
                    </otherwise>
                </choose>
            </if>
            <if test="month != null and month != ''">
                and  MONTH(o.SUBMIT_TIME)=#{lastMonth}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY date_format(DATE_ADD(o.SUBMIT_TIME,INTERVAL 1 MONTH ), '%Y-%m'),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        ORDER BY
        date_format( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 MONTH ), '%Y-%m' ) ASC  )
        old_sale ON now_sale.now_time = old_sale.now_time and now_sale.DETAIL_ID = old_sale.DETAIL_ID

        LEFT JOIN (
        SELECT
        date_format( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 YEAR ), '%Y-%m' ) AS last_year_time,
        sum(o.AMOUNT) AS last_year_drug_sum,
        o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{lastYear}
            </if>
            <if test="month != null and month != ''">
                and  MONTH(o.SUBMIT_TIME)=#{month}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY date_format(DATE_ADD(o.SUBMIT_TIME,INTERVAL 1 YEAR ), '%Y-%m'),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        ORDER BY
        date_format( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 YEAR ), '%Y-%m' ) ASC
        ) last_year_sale ON now_sale.now_time = last_year_sale.last_year_time
        and now_sale.DETAIL_ID = last_year_sale.DETAIL_ID

        LEFT JOIN
        sup_drug_detail d on now_sale.DETAIL_ID = d.ID
        )t
        <where>
            <if test='grewException != null and grewException == "1"'>
                and (t.year_ratio &gt; #{grewExceptionRate} or t.year_ratio &lt; -#{grewExceptionRate})
            </if>
            <if test='chainException != null and chainException == "1"'>
                and (t.month_ratio &gt; #{chainExceptionRate} or t.month_ratio &lt; -#{chainExceptionRate})
            </if>

            <if test='grewException != null and grewException == "0"'>
                and (t.year_ratio &gt; -#{grewExceptionRate} and t.year_ratio &lt; #{grewExceptionRate})
            </if>
            <if test='chainException != null and chainException == "0"'>
                and (t.month_ratio &gt; -#{chainExceptionRate} and t.month_ratio &lt; #{chainExceptionRate})
            </if>

        </where>
        <if test='sortProp != null and sortProp == "thisDrugItemPrice"'>
            order by this_drug_item_price
        </if>
        <if test='sortProp != null and sortProp == "monthRatio"'>
            order by month_ratio
        </if>
        <if test='sortProp != null and sortProp == "yearRatio"'>
            order by year_ratio
        </if>
        <if test='sortOrder != null and sortOrder == "descending"'>
            desc
        </if>
        <if test='sortOrder != null and sortOrder == "ascending"'>
            asc
        </if>
    </select>



    <select id="getDrugAmountByQuarter" resultType="map">
        SELECT
        t.*,
        IF(t.last_year_ratio &gt; #{grewExceptionRate} or t.last_year_ratio &lt; -#{grewExceptionRate}, '1','0') grew_Exception,
        IF(t.last_quarter_ratio &gt; #{chainExceptionRate} or t.last_quarter_ratio &lt; -#{chainExceptionRate}, '1','0') chain_Exception
        from (

        SELECT
        now_quarter_sale.DETAIL_ID,
        now_quarter_sale.CATALOG_NAME,
        now_quarter_sale.now_year,
        now_quarter_sale.a_quarter,
        now_quarter_sale.now_quarter,
        d.DOSAGE_FORM,
        d.SPECS,
        d.PACKING_SPECS,
        CASE
        WHEN now_quarter_sale.a_drug_item_price IS NULL
        OR now_quarter_sale.a_drug_item_price = 0 THEN
        0 ELSE now_quarter_sale.a_drug_item_price
        END this_drug_item_price,

        CASE
        WHEN now_quarter_sale.a_drug_sum IS NULL
        OR now_quarter_sale.a_drug_sum = 0 THEN
        0 ELSE now_quarter_sale.a_drug_sum
        END this_drug_sum,
        CASE

        WHEN last_quarter_sale.b_drug_sum IS NULL
        OR last_quarter_sale.b_drug_sum = 0 THEN
        0 ELSE last_quarter_sale.b_drug_sum
        END last_drug_sum,

        CASE
        WHEN last_quarter_sale.b_drug_sum IS NULL
        OR last_quarter_sale.b_drug_sum = 0 THEN
        0 ELSE CONVERT ( ( ( now_quarter_sale.a_drug_sum - last_quarter_sale.b_drug_sum ) / last_quarter_sale.b_drug_sum ) * 100, DECIMAL ( 10, 2 ) )
        END last_quarter_ratio ,
        CASE
        WHEN last_year_quarter_sale.c_drug_sum	 IS NULL
        OR last_year_quarter_sale.c_drug_sum	 = 0 THEN
        0 ELSE last_year_quarter_sale.c_drug_sum
        END last_year_drug_sum,
        CASE
        WHEN last_year_quarter_sale.c_drug_sum	 IS NULL
        OR last_year_quarter_sale.c_drug_sum	 = 0 THEN
        0 ELSE CONVERT ( ( ( now_quarter_sale.a_drug_sum - last_year_quarter_sale.c_drug_sum ) / last_year_quarter_sale.c_drug_sum ) * 100, DECIMAL ( 10, 2 ) )
        END last_year_ratio

        FROM
        (SELECT
        date_format(o.SUBMIT_TIME, '%Y' ) AS now_year,
        QUARTER (o.SUBMIT_TIME ) a_quarter,
        CONCAT( YEAR ( o.SUBMIT_TIME ), QUARTER (o.SUBMIT_TIME ) ) now_quarter,
        o.DETAIL_ID,o.CATALOG_NAME,
        sum(o.AMOUNT) a_drug_sum,
        sum(o.ITEM_PRICE) a_drug_item_price
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{year}
            </if>
            <if test="quarter != null and quarter != ''">
                and  QUARTER (o.SUBMIT_TIME )=#{quarter}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY
        CONCAT( YEAR ( o.SUBMIT_TIME ), QUARTER ( o.SUBMIT_TIME )),date_format( o.SUBMIT_TIME, '%Y' ), QUARTER (
        o.SUBMIT_TIME ),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        )now_quarter_sale
        LEFT JOIN
        (SELECT
        <choose>
            <when test="acrossYear != null and acrossYear != ''">
                CONCAT( YEAR ( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 YEAR ) ), QUARTER ( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 QUARTER ) ) ) AS last_quarter,
            </when>
            <otherwise>
                CONCAT( YEAR ( o.SUBMIT_TIME ), QUARTER ( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 QUARTER ) ) ) AS last_quarter,
            </otherwise>
        </choose>
        sum(o.AMOUNT) b_drug_sum, o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                <choose>
                    <when test="acrossYear != null and acrossYear != ''">
                        and  YEAR(o.SUBMIT_TIME) =#{lastYear}
                    </when>
                    <otherwise>
                        and  YEAR(o.SUBMIT_TIME) =#{year}
                    </otherwise>
                </choose>
            </if>
            <if test="quarter != null and quarter != ''">
                and  QUARTER (o.SUBMIT_TIME )=#{lastQuarter}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY
        CONCAT( YEAR (o.SUBMIT_TIME ), QUARTER ( DATE_ADD(o.SUBMIT_TIME, INTERVAL 1 QUARTER ) )),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        )
        last_quarter_sale ON now_quarter_sale.now_quarter = last_quarter_sale.last_quarter
        and now_quarter_sale.DETAIL_ID = last_quarter_sale.DETAIL_ID
        LEFT JOIN
        ( SELECT
        CONCAT(YEAR( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 year )),QUARTER(o.SUBMIT_TIME)) as last_year_quarter,
        sum(o.AMOUNT) AS c_drug_sum,  o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{lastYear}
            </if>
            <if test="quarter != null and quarter != ''">
                and  QUARTER (o.SUBMIT_TIME )=#{quarter}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY CONCAT(YEAR( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 year )),QUARTER(o.SUBMIT_TIME)),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        )last_year_quarter_sale  on now_quarter_sale.now_quarter=last_year_quarter_sale.last_year_quarter
        and now_quarter_sale.DETAIL_ID = last_year_quarter_sale.DETAIL_ID
        LEFT JOIN
        sup_drug_detail d on now_quarter_sale.DETAIL_ID = d.ID
        )t
        <where>
            <if test='grewException != null and grewException == "1"'>
                and (t.last_year_ratio &gt; #{grewExceptionRate} or t.last_year_ratio &lt; -#{grewExceptionRate})
            </if>
            <if test='chainException != null and chainException == "1"'>
                and (t.last_quarter_ratio &gt; #{chainExceptionRate} or t.last_quarter_ratio &lt; -#{chainExceptionRate})
            </if>
            <if test='grewException != null and grewException == "0"'>
                and (t.last_year_ratio &gt; -#{grewExceptionRate} and t.last_year_ratio &lt; #{grewExceptionRate})
            </if>
            <if test='chainException != null and chainException == "0"'>
                and (t.last_quarter_ratio &gt; -#{chainExceptionRate} and t.last_quarter_ratio &lt; #{chainExceptionRate})
            </if>

        </where>
        <if test='sortProp != null and sortProp == "thisDrugItemPrice"'>
            order by this_drug_item_price
        </if>
        <if test='sortProp != null and sortProp == "monthRatio"'>
            order by last_quarter_ratio
        </if>
        <if test='sortProp != null and sortProp == "yearRatio"'>
            order by last_year_ratio
        </if>
        <if test='sortOrder != null and sortOrder == "descending"'>
            desc
        </if>
        <if test='sortOrder != null and sortOrder == "ascending"'>
            asc
        </if>

    </select>

    <select id="getDrugAmountByYear" resultType="map">
        SELECT
        t.*,
        IF(t.year_ratio &gt; #{grewExceptionRate} or t.year_ratio &lt; -#{grewExceptionRate}, '1','0') grew_Exception
        from (
        SELECT
        now_sale.now_year,
        now_sale.DETAIL_ID,
        now_sale.CATALOG_NAME,
        d.DOSAGE_FORM,
        d.SPECS,
        d.PACKING_SPECS,

        CASE
        WHEN a_drug_item_price IS NULL
        OR a_drug_item_price = 0 THEN
        0 ELSE a_drug_item_price
        END this_drug_item_price,


        CASE
        WHEN a_drug_sum IS NULL
        OR a_drug_sum = 0 THEN
        0 ELSE a_drug_sum
        END this_drug_sum,

        CASE
        WHEN last_year_drug_sum IS NULL
        OR last_year_drug_sum = 0 THEN
        0 ELSE last_year_drug_sum
        END last_year_drug_sum,

        CASE
        WHEN last_year_drug_sum IS NULL
        OR last_year_drug_sum = 0 THEN
        0 ELSE ( CONVERT ( ( ( a_drug_sum - last_year_drug_sum ) / last_year_drug_sum ) * 100, DECIMAL ( 10, 2 ) ) )
        END year_ratio
        FROM
        (SELECT
        date_format(o.SUBMIT_TIME, '%Y' ) AS now_year,
        sum(o.AMOUNT) a_drug_sum, o.DETAIL_ID,o.CATALOG_NAME,
        sum(o.ITEM_PRICE) a_drug_item_price
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{year}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY date_format( o.SUBMIT_TIME, '%Y' ),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        ORDER BY
        date_format(o.SUBMIT_TIME, '%Y' ) ASC) now_sale
        LEFT JOIN (
        SELECT
        date_format( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 YEAR ), '%Y' ) AS last_year_time,
        sum(o.AMOUNT) AS last_year_drug_sum, o.DETAIL_ID,o.CATALOG_NAME
        FROM
        sup_drug_amount_temp o
        <where>
            <if test="year != null and year != ''">
                and  YEAR(o.SUBMIT_TIME) =#{lastYear}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                and o.CATALOG_NAME like #{dcatalogName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY date_format(DATE_ADD(o.SUBMIT_TIME,INTERVAL 1 YEAR ), '%Y'),o.DETAIL_ID
        <if test="hospitalId != null and hospitalId != ''">
            ,o.HOSPITAL_ID
        </if>
        ORDER BY
        date_format( DATE_ADD( o.SUBMIT_TIME, INTERVAL 1 YEAR ), '%Y' ) ASC
        ) last_year_sale ON now_sale.now_year = last_year_sale.last_year_time
        and now_sale.DETAIL_ID = last_year_sale.DETAIL_ID
        LEFT JOIN
        sup_drug_detail d on now_sale.DETAIL_ID = d.ID
        ORDER BY
        now_sale.now_year DESC
        )t
        <where>
            <if test='grewException != null and grewException == "1"'>
                and (t.year_ratio &gt; #{grewExceptionRate} or t.year_ratio &lt; -#{grewExceptionRate})
            </if>
        </where>
        <if test='sortProp != null and sortProp == "thisDrugItemPrice"'>
            order by this_drug_item_price
        </if>
        <if test='sortProp != null and sortProp == "yearRatio"'>
            order by year_ratio
        </if>
        <if test='sortOrder != null and sortOrder == "descending"'>
            desc
        </if>
        <if test='sortOrder != null and sortOrder == "ascending"'>
            asc
        </if>
    </select>

    <select id="getAmountByHospital" resultType="map">
        SELECT
        o.HOSPITAL_NAME AS 'hospitalName',
        sum(
        IF
        ( s.SOURCE = '1', s.ITEM_PRICE, 0 )) AS 'szAmount',
        sum(
        IF
        ( s.SOURCE = '2', s.ITEM_PRICE, 0 )) AS 'gdAmount',
        sum(
        IF
        ( s.SOURCE = '3', s.ITEM_PRICE, 0 )) AS 'gzAmount',
        sum( s.ITEM_PRICE ) AS 'totalAmount'
        FROM
        sup_order_item s
        LEFT JOIN sup_order o ON o.id = s.order_id
        <where>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
        </where>
        GROUP BY
        o.HOSPITAL_NAME
    </select>


    <select id="getSourceAmountByHospital" resultType="map">
        SELECT
        o.HOSPITAL_NAME AS 'hospitalName',
        sum(IFNULL( s.ITEM_PRICE, 0 )) AS 'totalPrice',
        sum(IFNULL( s.AMOUNT, 0 )) AS 'totalAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * s.AMOUNT) 'totalPackingAmount',
        count(DISTINCT(s.DETAIL_ID)) AS 'totalCount',
        sum(IF( d.COUNTRY = '1', s.ITEM_PRICE, 0 )) AS 'countryPrice',
        sum(IF( d.COUNTRY  = '1', s.AMOUNT, 0 )) AS 'countryAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  = '1', s.AMOUNT, 0 )) 'countryPackingAmount',
        count(DISTINCT IF(d.COUNTRY  = '1', s.DETAIL_ID, NULL )) AS 'countryCount',
        sum(IF( d.COUNTRY  ='0', s.ITEM_PRICE, 0 )) AS 'noCountryPrice',
        sum(IF( d.COUNTRY  ='0', s.AMOUNT, 0 )) AS 'noCountryAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  ='0', s.AMOUNT, 0 )) 'noCountryPackingAmount',
        count(DISTINCT  IF(d.COUNTRY  ='0', s.DETAIL_ID, NULL )) AS 'noCountryCount',
        sum(IF( d.COUNTRY  ='2', s.ITEM_PRICE, 0 )) AS 'offlinePrice',
        sum(IF( d.COUNTRY  ='2', s.AMOUNT, 0 )) AS 'offlineAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  ='2', s.AMOUNT, 0 )) 'offlinePackingAmount',
        count(DISTINCT IF( d.COUNTRY  ='2', s.DETAIL_ID, null )) AS 'offlineCount'
        FROM
        sup_order_item s
        LEFT JOIN sup_order o ON o.id = s.order_id
        LEFT JOIN sup_hospital h on h.ID=o.HOSPITAL_ID
        LEFT JOIN sup_drug_detail d on s.DETAIL_ID=d.id
        <where>
            s.ORDER_ITEM_STATUS NOT IN ('5')
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source !=null and source !='' ">
                AND o.SOURCE =#{source}
            </if>
            <if test="attribute !=null and attribute !=''">
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
            <if test="regionId != null and regionId != ''">
                and s.REGION_ID = #{regionId}
            </if>
        </where>
        GROUP BY
        o.HOSPITAL_NAME
        ORDER BY h.SORT_ORDER ASC
    </select>

    <select id="getSourceAmountByCity" resultType="map">
        SELECT
        o.HOSPITAL_NAME AS 'hospitalName',
        sum(IFNULL( s.ITEM_PRICE, 0 )) AS 'totalPrice',
        sum(IFNULL( s.AMOUNT, 0 )) AS 'totalAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * s.AMOUNT) 'totalPackingAmount',
        count(DISTINCT(s.DETAIL_ID)) AS 'totalCount',
        sum(IF( d.COUNTRY = '1', s.ITEM_PRICE, 0 )) AS 'countryPrice',
        sum(IF( d.COUNTRY = '1', s.AMOUNT, 0 )) AS 'countryAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  = '1', s.AMOUNT, 0 )) 'countryPackingAmount',
        count(DISTINCT IF(d.COUNTRY = '1', s.DETAIL_ID, NULL )) AS 'countryCount',
        sum(IF( d.COUNTRY  ='0', s.ITEM_PRICE, 0 )) AS 'noCountryPrice',
        sum(IF( d.COUNTRY  ='0', s.AMOUNT, 0 )) AS 'noCountryAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  ='0', s.AMOUNT, 0 )) 'noCountryPackingAmount',
        count(DISTINCT  IF(d.COUNTRY  ='0', s.DETAIL_ID, NULL )) AS 'noCountryCount',
        sum(IF( d.COUNTRY  ='2', s.ITEM_PRICE, 0 )) AS 'offlinePrice',
        sum(IF( d.COUNTRY  ='2', s.AMOUNT, 0 )) AS 'offlineAmount',
        sum(-(-CONCAT(d.PACKING_SPECS,'')) * IF( d.COUNTRY  ='2', s.AMOUNT, 0 )) 'offlinePackingAmount',
        count(DISTINCT IF( d.COUNTRY  ='2', s.DETAIL_ID, null )) AS 'offlineCount'
        FROM
        sup_order_item s
        LEFT JOIN sup_order o ON o.id = s.order_id
        LEFT JOIN sup_hospital h on h.ID=o.HOSPITAL_ID
        LEFT JOIN sup_drug_detail d on s.DETAIL_ID=d.id
        <where>
            s.ORDER_ITEM_STATUS NOT IN ('5')
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source !=null and source !='' ">
                AND o.SOURCE =#{source}
            </if>
            <if test="attribute !=null and attribute !=''">
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>

            <if test="regionId != null and regionId != ''">
                and s.region_id = #{regionId}
            </if>
        </where>
        ORDER BY h.SORT_ORDER ASC
    </select>

    <select id="getOrderItemListToExcel" resultType="com.inspur.ssp.supervise.bean.dto.purchase.SupOrderItemExportExcel">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        e.APPROVAL_NUMBER,
        e.SPECS,
        e.PACKING_SPECS,
        e.DOSAGE_FORM,
        y.NAME AS 'drugCompanyName',
        c.NAME AS 'catalogName',
        d.ORDER_NUM AS 'orderNum',
        d.SUBMIT_TIME,
        e.MEDICAL_INSURANCE_CODE,
        B.BATCH_NAME

        FROM
        sup_order_item i
        LEFT JOIN sup_order d ON i.ORDER_ID = d.id
        LEFT JOIN sup_drug_detail e ON e.id = i.DETAIL_ID
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_drug_company y ON e.DRUG_COMPANY_ID=y.ID
        LEFT JOIN SUP_DRUG_BATCH B ON B.CODE=i.country_batch

        <where>
            <if test="regionId != null and regionId != ''">
                and i.REGION_ID = #{regionId}
            </if>
            <if test="systemContrast !=null and systemContrast !=''">
                AND i.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
            </if>
            <if test="orderItemStatus != null and orderItemStatus != ''">
                and i.ORDER_ITEM_STATUS = #{orderItemStatus}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                and i.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and i.PAY_STATUS = #{payStatus}
            </if>
            <if test="country != null and country != ''">

                <if test='country =="1"'>
                    and  i.COUNTRY_TAG = '1'
                    AND b.BATCH_NAME IS NOT NULL
                </if>

                <if test='country !="1"'>
                    and e.COUNTRY = #{country}
                </if>
            </if>
            <if test="source != null and source != ''">
                and i.source = #{source}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                <bind name="horderCode" value="'%'+orderCode+'%'"/>
                and i.ORDER_NUM like #{horderCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND d.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="batch !=null and batch !=''">
                AND i.COUNTRY_BATCH = #{batch}
            </if>
            <if test="specs != null and specs != ''">
                <bind name="dSpecs" value="'%'+specs+'%'"/>
                and e.SPECS like #{dSpecs}
            </if>
            <if test="drugCompanyName != null and drugCompanyName != ''">
                <bind name="dDrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                and y.Name like #{dDrugCompanyName}
            </if>
            <if test="standardCode != null and standardCode != ''">
                and e.STANDARD_CODE = #{standardCode}
            </if>
            <if test="medicalInsuranceCode != null and medicalInsuranceCode != ''">
                and e.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode}
            </if>

        </where>
        order by d.SUBMIT_TIME desc,d.HOSPITAL_NAME desc
    </select>

    <select id="getOrderItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        e.APPROVAL_NUMBER,
        e.SPECS,
        e.PACKING_SPECS,
        e.DOSAGE_FORM,
        y.NAME AS 'drugCompanyName',
        c.NAME AS 'catalogName',
        d.ORDER_NUM AS 'orderNum',
        d.SUBMIT_TIME,
        e.MEDICAL_INSURANCE_CODE,
        e.STANDARD_CODE

        FROM
        sup_order_item i
        LEFT JOIN sup_order d ON i.ORDER_ID = d.id
        LEFT JOIN sup_drug_detail e ON e.id = i.DETAIL_ID
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_drug_company y ON e.DRUG_COMPANY_ID=y.ID
        <if test="country =='1'.toString()">
            LEFT JOIN SUP_DRUG_BATCH B ON B.CODE=i.country_batch
        </if>

        <where>
            <if test="regionId != null and regionId != ''">
                and i.REGION_ID = #{regionId}
            </if>
            <if test="systemContrast !=null and systemContrast !=''">
                AND i.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
            </if>
            <if test="orderItemStatus != null and orderItemStatus != ''">
                and i.ORDER_ITEM_STATUS = #{orderItemStatus}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                and i.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and i.PAY_STATUS = #{payStatus}
            </if>
            <if test="country != null and country != ''">
                <if test='country =="1"'>
                    and  i.COUNTRY_TAG = '1'
                    and i.COUNTRY_BATCH  in
                    <foreach item="batchCode" collection="batchCodeArray" separator="," open="(" close=")" index="index">
                        #{batchCode}
                    </foreach>
                </if>

                <if test='country !="1"'>
                    and  i.COUNTRY_TAG = #{country}
                </if>

            </if>
            <if test="source != null and source != ''">
                and i.source = #{source}
            </if>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                <bind name="horderCode" value="'%'+orderCode+'%'"/>
                and i.ORDER_NUM like #{horderCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
<!--            <if test="hospitalName != null and hospitalName != ''">-->
<!--                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>-->
<!--                and d.HOSPITAL_NAME like #{hHospitalName}-->
<!--            </if>-->
            <if test="hospitalName != null and hospitalName != ''">
                and d.HOSPITAL_NAME = #{hospitalName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND d.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="batch !=null and batch !=''">
                AND i.COUNTRY_BATCH = #{batch}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                and i.id = #{orderItemId}
            </if>
            <if test="specs != null and specs != ''">
                <bind name="dSpecs" value="'%'+specs+'%'"/>
                and e.SPECS like #{dSpecs}
            </if>
            <if test="drugCompanyName != null and drugCompanyName != ''">
                <bind name="dDrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                and y.Name like #{dDrugCompanyName}
            </if>
            <if test="standardCode != null and standardCode != ''">
                and e.STANDARD_CODE = #{standardCode}
            </if>
            <if test="medicalInsuranceCode != null and medicalInsuranceCode != ''">
                and e.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode}
            </if>
        </where>
        order by i.SUBMIT_TIME desc,i.HOSPITAL_NAME desc
    </select>

    <update id="flushCountryOrderItem" parameterType="map">
        update sup_order_item i set i.CATALOG_ID = #{catalogId}, i.CATALOG_NAME = #{catalogName},
                                    i.DETAIL_ID = #{detailId}, i.DRUG_VERSION = #{drugVersion}, i.COUNTRY_TAG = #{countryTag}, i.COUNTRY_BATCH = #{countryBatch}
        where  i.DETAIL_ID = #{oldDetailId} and i.CATALOG_ID = #{oldCatalogId} and i.SOURCE_ID = #{oldSourceId}
    </update>
    <update id="updateCountryByDetail" parameterType="map">
        update sup_order_item i set i.COUNTRY_TAG = #{country,jdbcType=VARCHAR} , i.COUNTRY_BATCH = #{batch,jdbcType=VARCHAR} where
        i.detail_id = #{detailId}
        <if test="startTime !=null and startTime !=''">
            and submit_time between #{startTime} and #{endTime}
        </if>

        <if test="contractNumber !=null and contractNumber !=''">
            and CONTRACT_NUMBER = #{contractNumber}
        </if>

    </update>


    <select id="groupByContranctInfo" resultType="map">
        select soi.CONTRACT_NUMBER,soi.COUNTRY_TAG,soi.COUNTRY_BATCH
        from sup_order_item soi
        where soi.SOURCE=1 and soi.CONTRACT_NUMBER = 'H20062400089'
        group by  soi.CONTRACT_NUMBER,soi.COUNTRY_TAG,soi.COUNTRY_BATCH
        order by soi.CONTRACT_NUMBER
    </select>


</mapper>
