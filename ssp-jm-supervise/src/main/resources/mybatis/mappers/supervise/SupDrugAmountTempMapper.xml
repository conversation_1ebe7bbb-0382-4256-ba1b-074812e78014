<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugAmountTempMapper">
    <select id="getDrugAmountTempList" resultType="com.inspur.ssp.supervise.bean.entity.SupDrugAmountTemp">
        SELECT
        o.HOSPITAL_NAME,
        o.HOSPITAL_ID,
        i.CATALOG_NAME,
        i.DETAIL_ID,
        sum( i.AMOUNT ) AMOUNT,
        sum( i.ITEM_PRICE ) ITEM_PRICE,
        DATE_FORMAT( o.SUBMIT_TIME, "%Y-%m-%d" ) SUBMIT_TIME
        FROM
        SUP_ORDER_ITEM i
        LEFT JOIN sup_order o ON o.id = i.order_id
        where i.ORDER_ITEM_STATUS  <![CDATA[ <> ]]> '5'
        GROUP BY
        DATE_FORMAT( o.submit_time, "%Y-%m-%d" ),
        o.HOSPITAL_ID,
        i.DETAIL_ID
        <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
            AND i.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
        </if>
    </select>

    <select id="getDrugAmountOnMonth" resultType="map">
            SELECT
                date_format( o.SUBMIT_TIME, '%Y-%m' ) AS submitTime,
                sum( o.AMOUNT ) drugSum
            FROM
                sup_drug_amount_temp o
            WHERE
                1=1
                <if test="startDate != null and startDate != ''">
                    AND o.SUBMIT_TIME >= #{startDate}
                </if>
                <if test="detailId != null and detailId != ''">
                    AND o.DETAIL_ID = #{detailId}
                </if>
                <if test="hospitalId != null and hospitalId != ''">
                    and o.HOSPITAL_ID = #{hospitalId}
                </if>
                <if test="regionId != null and regionId != ''">
                    and o.HOSPITAL_ID in (select id from sup_hospital where region_id = #{regionId})
                </if>
                <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                    AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
                </if>
            GROUP BY
                date_format( o.SUBMIT_TIME, '%Y-%m' )
                <if test="hospitalId != null and hospitalId != ''">
                    ,o.HOSPITAL_ID
                </if>
            ORDER BY
                date_format( o.SUBMIT_TIME, '%Y-%m' ) ASC
    </select>

    <select id="getDrugAmountOnQuarter" resultType="map">
        SELECT
        date_format(o.SUBMIT_TIME, '%Y' ) AS submitYear,
        QUARTER (o.SUBMIT_TIME ) submitQuarter,
        CONCAT( YEAR ( o.SUBMIT_TIME ), QUARTER ( o.SUBMIT_TIME ) ) submitTime,
        sum( o.AMOUNT ) drugSum
        FROM
        sup_drug_amount_temp o
        WHERE
        CONCAT( YEAR ( o.SUBMIT_TIME ), QUARTER ( o.SUBMIT_TIME ) ) >= #{startDate}
        AND o.DETAIL_ID = #{detailId}
        <if test = "hospitalId != null and hospitalId != ''" >
            AND o.HOSPITAL_ID = #{hospitalId}
        </if >
        GROUP BY
        CONCAT(
        YEAR ( o.SUBMIT_TIME ),
        QUARTER ( o.SUBMIT_TIME )) <if test = "hospitalId != null and hospitalId != ''" >,
        o.HOSPITAL_ID </if >
    </select>


</mapper>
