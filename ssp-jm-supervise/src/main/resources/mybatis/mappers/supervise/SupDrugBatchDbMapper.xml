<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugBatchDbMapper">
    <select id="getBatchDrugDb" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugBatchDbVo">
        select db.*,ba.TASK_START_TIME,ba.TASK_END_TIME from sup_drug_batch_db db inner join sup_drug_batch ba on ba.code = db.batch_code
    </select>
</mapper>
