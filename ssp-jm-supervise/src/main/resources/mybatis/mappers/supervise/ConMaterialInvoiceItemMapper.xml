<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialInvoiceItemMapper">

    <select id="getConMaterialInvoiceItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        d.code ,
        e.NAME,
        d.remark INVOICE_REMARK
        FROM
        con_material_invoice_item i
        LEFT JOIN con_material_invoice d ON i.INVOICE_ID = d.id
        LEFT JOIN con_material_detail e ON e.id = i.detail_id
        <where>
            <if test="name != null and name != ''">
                <bind name="hName" value="'%'+name+'%'"/>
                and c.NAME like #{hName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and i.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="code != null and code != ''">
                and i.ITEM_NO = #{code}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="country != null and country != ''">
                and e.country = #{country}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and i.pay_status = #{payStatus}
            </if>
            <if test="payType != null and payType != '' and payType == '1'.toString()">
                and i.doc_info is not null and i.pay_status = '1'
            </if>
            <if test="payType != null and payType != '' and payType == '0'.toString()">
                and i.doc_info is null and i.pay_status = '1'
            </if>
        </where>
        order by i.INVOICE_DATE desc
    </select>

</mapper>
