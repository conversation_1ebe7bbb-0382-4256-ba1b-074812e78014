<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupHospitalUserMapper">
    <select id="getUsersByHospitalId" resultType="com.inspur.ssp.bsp.entity.PubUser">
        select u.* from pub_user u left join sup_hospital_user hu on u.id = hu.user_id
        where hu.HOSPITAL_ID = #{hospitalId}
    </select>
</mapper>
