<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDeliveryItemMapper">
    <select id="getOverStockInDeliveryItem" resultType="com.inspur.ssp.supervise.bean.entity.SupDeliveryItem">
        SELECT
        o.ID,
        o.DELIVERY_ID,
        o.DELIVERY_CODE,
        o.ITEM_NO,
        o.ORDER_ID,
        o.ORDER_CODE,
        o.ORDER_ITEM_ID,
        o.ORDER_ITEM_NO,
        o.DRUG_ID,
        o.UNIT_PRICE,
        o.NUM,
        o.AMOUNT,
        o.CREATOR,
        o.CREATION_TIME,
        o.LAST_MODIFIED_TIME,
        o.LAST_MODIFIER,
        o.CONTRACT_DETAILS_NUMBER,
        o.DELIVERY_TIME,
        o.DRUG_BUSI_CODE,
        o.PAY_SURPLUS,
        o.WARNING,
        o.PID,
        ( DATEDIFF( NOW(), o.DELIVERY_TIME )) AS STOCK_SURPLUS
        FROM
            sup_delivery_item o
        left JOIN sup_order_item m on m.code=o.ORDER_ITEM_NO
        left join sup_drug_detail e on e.id=o.DRUG_ID
        WHERE
            m.STOCK_STATUS != '1'
            and not EXISTS(select ii.ID from sup_stock_in_item ii WHERE ii.DELIVERY_ITEM_ID = o.id and ii.STOCK_STATUS = '1')
        <if test="isCountry != null and isCountry != ''">
            and  e.COUNTRY = #{isCountry}
        </if>
        <if test="submitFrom != null and submitFrom != ''">
            and  m.SUBMIT_TIME > #{submitFrom}
        </if>
    </select>

    <select id="getCompleteStockInDeliveryItem" resultType="com.inspur.ssp.supervise.bean.entity.SupDeliveryItem">
        SELECT
        o.ID,
        o.DELIVERY_ID,
        o.DELIVERY_CODE,
        o.ITEM_NO,
        o.ORDER_ID,
        o.ORDER_CODE,
        o.ORDER_ITEM_ID,
        o.ORDER_ITEM_NO,
        o.DRUG_ID,
        o.UNIT_PRICE,
        o.NUM,
        o.AMOUNT,
        o.CREATOR,
        o.CREATION_TIME,
        o.LAST_MODIFIED_TIME,
        o.LAST_MODIFIER,
        o.CONTRACT_DETAILS_NUMBER,
        o.DELIVERY_TIME,
        o.DRUG_BUSI_CODE,
        o.PAY_SURPLUS,
        o.WARNING,
        o.PID,
        (DATEDIFF( NOW(), o.DELIVERY_TIME )) AS STOCK_SURPLUS
        FROM
        sup_delivery_item o
        LEFT JOIN sup_drug_detail e ON e.id = o.DRUG_ID
        LEFT JOIN
        ( SELECT ii.DELIVERY_ITEM_ID,sum(ii.NUM) stockInNum FROM sup_stock_in_item ii WHERE ii.STOCK_STATUS = '1' GROUP BY ii.DELIVERY_ITEM_ID) t on t.DELIVERY_ITEM_ID = o.id
        WHERE
        1=1
        <if test="country != null and country != ''">
            and  e.COUNTRY = #{country}
        </if>
        <if test="orderCode != null and orderCode != ''">
            and o.ORDER_CODE = #{orderCode}
        </if>
        and o.WARNING like '%7%'
        and t.stockInNum = o.num
    </select>

    <select id="getOverStockList" resultType="map">
        SELECT
        i.id,i.DELIVERY_ID,i.DELIVERY_CODE,i.ITEM_NO,i.ORDER_ID,i.ORDER_CODE,i.ORDER_ITEM_ID,i.ORDER_ITEM_NO,i.DRUG_ID,i.UNIT_PRICE,i.NUM,i.AMOUNT,i.CONTRACT_DETAILS_NUMBER,
        i.DELIVERY_TIME,i.DRUG_BUSI_CODE,i.PAY_SURPLUS,i.WARNING,i.PID,i.STOCK_SURPLUS,
        o.HOSPITAL_NAME,
        o.DELIVERY_NAME,
        e.CODE eCode,e.COMPANY_ID,e.COUNTRY,e.COUNTRY_TYPE,e.ELECTION_SCOPE,e.DOSAGE_FORM,e.GOODS_NAME,
        e.SPECS,e.PACKING,e.PACKING_SPECS,e.SPECS_ATTR,e.UNIT,e.APPROVAL_NUMBER,e.STANDARD_CODE,
        o.HOSPITAL_ID,
        c.NAME AS 'CATALOG_NAME',
        m.SOURCE AS 'SOURCE',
        min(ii.STOCK_IN_TIME) STOCK_IN_TIME
        FROM
        sup_delivery_item i
        LEFT JOIN sup_delivery o ON o.CODE=i.DELIVERY_CODE
        LEFT JOIN sup_drug_detail e ON e.id = i.DRUG_ID
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_order_item m ON m.code=i.ORDER_ITEM_NO
        LEFT JOIN sup_stock_in_item ii ON ii.DELIVERY_ITEM_ID = i.id
        WHERE
        i.STOCK_SURPLUS &lt; 0
        AND m.STOCK_STATUS != '1'
        AND m.STATUS != '5'
    <if test="hospitalName != null and hospitalName != ''">
        <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
        and o.HOSPITAL_NAME like #{hHospitalName}
    </if>
    <if test="hospitalId != null and hospitalId != ''">
        and o.HOSPITAL_ID = #{hospitalId}
    </if>
    <if test="deliveryItemId != null and deliveryItemId != ''">
        and i.id = #{deliveryItemId}
    </if>
    <if test="country != null and country != ''">
        <if test = "country = '1'.toString()">
           and m.COUNTRY_TAG = '1'
        </if>
        and  e.COUNTRY = #{country}
    </if>
    <if test="deliveryCode != null and deliveryCode != ''">
        and o.code = #{deliveryCode}
    </if>
    <if test="orderCode != null and orderCode != ''">
        and i.ORDER_CODE = #{orderCode}
    </if>
    <if test="deliveryStartTime !=null and deliveryStartTime !='' and deliveryEndTime !=null and deliveryEndTime !=''">
        AND o.DELIVERY_TIME BETWEEN #{deliveryStartTime} and #{deliveryEndTime}
    </if>
    <if test="decide != null and decide != ''">
        <if test = "decide == '0'.toString()">
            and ii.STOCK_IN_TIME is null
        </if>
        <if test = "decide == '1'.toString()">
            and ii.STOCK_IN_TIME is not null
        </if>
    </if>
    group by i.id
</select>



    <select id="getDeliveryItemDetail" resultType="map">
      select d.*,c.NAME as 'CATALOG_NAME' ,v.*,s.STOCK_STATUS  from sup_delivery_item  d
      left join sup_drug_detail v on d.DRUG_ID=v.id
      left join sup_drug_catalog c on c.id=v.CATALOG_ID
      left join sup_stock_in_item s on s.DELIVERY_ITEM_ID=d.ID
      where d.DELIVERY_CODE=#{deliveryCode}  order by d.DELIVERY_TIME desc
    </select>

    <select id="getDeliveryItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        e.COUNTRY,
        e.APPROVAL_NUMBER,
        y.NAME AS 'drugCompanyName',
        c.NAME AS 'catalogName',
        s.STOCK_STATUS
        FROM
        sup_delivery_item i
        LEFT JOIN sup_delivery d ON i.DELIVERY_ID = d.id
        LEFT JOIN sup_drug_detail e ON e.id = i.drug_id
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_stock_in_item s ON s.DELIVERY_ITEM_ID = i.ID
        LEFT JOIN sup_drug_company y ON e.DRUG_COMPANY_ID=y.ID
        <where>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                and i.ORDER_ITEM_ID = #{orderItemId}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and i.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.DELIVERY_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="regionId != null and regionId != ''">
                and d.HOSPITAL_ID in (select id from sup_hospital where region_id = #{regionId})
            </if>
        </where>
        order by i.DELIVERY_TIME desc,d.HOSPITAL_NAME desc
    </select>


    <select id="getDeliveryListByHospital" resultType="com.inspur.ssp.supervise.bean.vo.SupDeliveryItemVo">
        SELECT
            d.HOSPITAL_ID,
            d.HOSPITAL_NAME,
            IFNULL(sum(i.AMOUNT),0) AMOUNT,
            o.SOURCE,
            COUNT(distinct(d.id))  DELIVERY_COUNT
        FROM
            sup_delivery_item i
                LEFT JOIN sup_delivery d ON i.DELIVERY_ID = d.ID
                LEFT JOIN sup_order_item o ON o.ID = i.ORDER_ITEM_ID
                LEFT JOIN SUP_HOSPITAL H ON d.HOSPITAL_ID = H.ID
        <where>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND d.DELIVERY_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source != null and source != ''">
                and o.SOURCE =#{source}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                and o.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="regionId != null and regionId != ''">
                and h.REGION_ID = #{regionId}
            </if>
        </where>
        GROUP BY
        d.HOSPITAL_ID,o.source
        ORDER BY H.SORT_ORDER ASC
    </select>

    <select id="getDeliveryListOnHospital" resultType="map">
        SELECT
         <if test="source != null and source != ''">
          e.SOURCE,
        </if>
        <if test="stockStatus != null and stockStatus != ''">
           e.STOCK_STATUS,
        </if>
         e.HOSPITAL_ID,e.HOSPITAL_NAME, IFNULL(sum(e.AMOUNT),0) AMOUNT ,IFNULL(sum(e.DELIVERY_COUNT),0) DELIVERY_COUNT  from (
        SELECT
        d.HOSPITAL_ID,
        d.HOSPITAL_NAME,
        sum( i.AMOUNT ) AMOUNT,
        o.SOURCE,
        o.STOCK_STATUS,
        COUNT(distinct(d.id))  DELIVERY_COUNT
        FROM
        sup_delivery_item i
        LEFT JOIN sup_delivery d ON i.DELIVERY_ID = d.ID
        LEFT JOIN sup_order_item o ON o.ID = i.ORDER_ITEM_ID
        <where>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND d.DELIVERY_TIME BETWEEN #{startTime} and #{endTime}
            </if>
        </where>
        GROUP BY
        d.HOSPITAL_NAME,o.source, o.STOCK_STATUS) e
        <where>
            <if test="source != null and source != ''">
                and e.SOURCE =#{source}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                and e.STOCK_STATUS = #{stockStatus}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and e.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY e.HOSPITAL_NAME
    </select>

    <update id="flushCountryDeliveryItem" parameterType="map">
        update sup_delivery_item i set i.DRUG_ID = #{detailId}
        where  i.DRUG_ID = #{oldDetailId}
    </update>


    <update id="updateWarning" parameterType="map">
        update sup_delivery_item i set i.warning = #{warning}
        where  i.id = #{id}
    </update>

</mapper>
