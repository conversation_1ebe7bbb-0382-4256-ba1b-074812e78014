<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.DrugViewMapper">

        <select id="getDrug" resultType="com.inspur.ssp.supervise.bean.entity.DrugView">
        select d.* from drug_view d
         left join sup_drug_source s on d.code = s.DRUG_CODE
         left join sup_drug_price p on p.SOURCE_ID = s.id
        <where>
        1=1
        <if test="standardCode != null and standardCode != ''">
            and d.STANDARD_CODE = #{standardCode}
        </if>
        <if test="spece != null and spece != ''">
            and d.SPECS = #{spece}
        </if>
        <if test="price != null and price != ''">
            and p.price = #{price}
        </if>
        <if test="catalogName != null and catalogName != ''">
            <bind name="dCatalogName" value="'%'+catalogName+'%'"/>
            AND d.CATALOG_NAME LIKE #{dCatalogName,jdbcType=VARCHAR}
        </if>
        <if test="drugCompanyName !=null and drugCompanyName !=''">
            <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
            AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="source != null and source != ''">
            and s.SOURCE = #{source}
        </if>
        <if test="drugCode != null and drugCode != ''">
            and s.DRUG_CODE = #{drugCode}
        </if>
        <if test="busiCode != null and busiCode != ''">
            and s.BUSI_CODE = #{busiCode}
        </if>
        </where>
    </select>

    <select id="getDrugBySource" resultType="map">
        select d.*,s.source,p.price from drug_view d
                            left join sup_drug_source s on d.code = s.DRUG_CODE
                            left join sup_drug_price p on p.SOURCE_ID = s.id
        <where>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and d.CATALOG_NAME like #{hcatalogName}
            </if>
            <if test="source != null and source != ''">
                and s.SOURCE = #{source}
            </if>
            <if test="country != null and country != ''">
                and d.COUNTRY = #{country}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR}
                AND s.status=#{status,jdbcType=CHAR}
                AND p.status=#{status,jdbcType=CHAR}
            </if>
            <if test='attribute !=null and attribute !=""'>
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>
    <select id="selectOffLineDrug" resultType="com.inspur.ssp.supervise.bean.dto.DrugViewDto">
        SELECT
            d.*,
            s.id sourceId,
            s.source sourceName,
            p.price,
            p.id priceId
        FROM
            drug_view d
        LEFT JOIN sup_drug_source s ON s.drug_code = d.code
        LEFT JOIN sup_hospital_drug h ON h.DRUG_ID = d.id
        LEFT JOIN sup_drug_price p ON p.source_id = s.id
        where h.HOSPITAL_ID=#{hospitalId}
          and d.country='2' and s.source='4' and d.CATALOG_NAME=#{catalogName}
          and d.DOSAGE_FORM=#{dosageForm} and d.SPECS=#{specs}
          and d.PACKING_SPECS=#{packingSpecs} and D.APPROVAL_NUMBER=#{approvalNumber}
          and d.DRUG_COMPANY_ID=#{drugCompanyId}
    </select>
</mapper>
