<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialStockInItemMapper">
    <select id="getConMaterialStockInItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        e.*,
        y.NAME AS 'conCompanyName'
        FROM
        con_material_stock_in_item i
        LEFT JOIN con_material_stock_in d ON i.STOCK_IN_ID = d.id
        LEFT JOIN con_material_detail e ON e.id = i.detail_id
        LEFT JOIN sup_drug_company y ON e.CON_COMPANY_ID=y.ID
        <where>
            <if test="name != null and name != ''">
                <bind name="hName" value="'%'+name+'%'"/>
                and c.NAME like #{hName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="stockInCode != null and stockInCode != ''">
                and i.STOCK_IN_CODE = #{stockInCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.STOCK_IN_TIME BETWEEN #{startTime} and #{endTime}
            </if>
        </where>
        order by i.STOCK_IN_TIME desc,d.HOSPITAL_NAME desc
    </select>
</mapper>
