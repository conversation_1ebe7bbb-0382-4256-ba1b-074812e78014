<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupStockInItemMapper">

    <select id="getStockInItemDetail" resultType="map">
      select d.*,v.*,c.name as 'catalog_name' from sup_stock_in_item  d
      left join sup_drug_detail v on d.DRUG_ID=v.id
      left join sup_drug_catalog c ON c.id = v.CATALOG_ID
      where d.STOCK_IN_CODE=#{stockInCode} order by d.STOCK_IN_TIME desc
    </select>

    <select id="getStockInItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        e.*,
        y.NAME AS 'drugCompanyName',
        c.NAME AS 'catalogName'
        FROM
        sup_stock_in_item i
        LEFT JOIN sup_stock_in d ON i.STOCK_IN_ID = d.id
        LEFT JOIN sup_drug_detail e ON e.id = i.drug_id
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        LEFT JOIN sup_drug_company y ON e.DRUG_COMPANY_ID=y.ID
        <where>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="stockInCode != null and stockInCode != ''">
                and i.STOCK_IN_CODE = #{stockInCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.STOCK_IN_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="regionId != null and regionId != ''">
                and d.HOSPITAL_ID in (select id from sup_hospital where region_id = #{regionId})
            </if>
        </where>
        order by i.STOCK_IN_TIME desc,d.HOSPITAL_NAME desc
    </select>

    <select id="getNoStockAndOnPay" resultType="com.inspur.ssp.supervise.bean.entity.SupStockInItem">
        SELECT
           ii.*
        FROM
            sup_stock_in_item ii
            LEFT JOIN sup_order_item oi ON ii.ORDER_ITEM_ID = oi.ID
        WHERE
            1 = 1
            AND ii.STOCK_STATUS = '1'
            AND oi.STOCK_STATUS = '0'
        <if test="country != null and country != ''">
            and oi.COUNTRY_TAG = #{country}
        </if>
        <if test="orderNum != null and orderNum != ''">
            and oi.ORDER_NUM = #{orderNum}
        </if>

    </select>

    <select id="getCuowuShuju" resultType="com.inspur.ssp.supervise.bean.entity.SupStockInItem">
        SELECT
            ii.*
        FROM
            sup_delivery_item i
            LEFT JOIN sup_delivery o ON o.CODE = i.DELIVERY_CODE
            LEFT JOIN sup_drug_detail e ON e.id = i.DRUG_ID
            LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
            LEFT JOIN sup_order_item m ON m.CODE = i.ORDER_ITEM_NO
            LEFT JOIN sup_stock_in_item ii on ii.DELIVERY_ITEM_ID = i.id
        WHERE
            i.STOCK_SURPLUS &lt; 0
            and m.STOCK_STATUS != '1'
            AND e.COUNTRY = '1'
            and i.WARNING = '1'

    </select>

    <update id="flushCountryStockInItem" parameterType="map">
        update sup_stock_in_item i set i.DRUG_ID = #{detailId}
        where  i.DRUG_ID = #{oldDetailId}
    </update>
</mapper>
