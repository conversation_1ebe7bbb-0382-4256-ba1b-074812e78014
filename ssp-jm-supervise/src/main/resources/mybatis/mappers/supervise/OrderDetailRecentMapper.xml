<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.OrderDetailRecentMapper">
    <select id="getOrderList" resultType="com.inspur.ssp.supervise.bean.entity.OrderDetailRecent">
        select order_id,order_name,hospital_id,hospital_name,add_user_name,add_time,detail_distribute_address,SUM(purchase_amount) as purchase_amount,orderdetail_state from  drugpur_orderdetail_recent where 1 = 1

        <if test="submitTimeFrom !=null ">
            <![CDATA[ and last_update_time  >=  #{submitTimeFrom}  ]]>
        </if>
        <if test="submitTimeTo !=null"  >
            <![CDATA[ and last_update_time <=  #{submitTimeTo}  ]]>
        </if>

        <if test="notCancle != null and notCancle == '1'.toString()">
            and orderdetail_state not in ('0','3','5','9','10','11')
        </if>

        <if test="cancle != null and cancle == '1'.toString()">
            and orderdetail_state in ('0','3','5','9','10','11')
        </if>

        <if test="orderNum != null and orderNum != ''">
            and order_id =#{orderNum}
        </if>
        group by order_id,order_name,hospital_id,hospital_name,add_user_name,add_time,detail_distribute_address,detail_distribute_address
    </select>

    <select id="getOrderDetailList" resultType="com.inspur.ssp.supervise.bean.entity.OrderDetailRecent">
        select * from  drugpur_orderdetail_recent where 1 = 1

        <if test="submitTimeFrom !=null">
            <![CDATA[ and last_update_time  >=  #{submitTimeFrom}  ]]>
        </if>
        <if test="submitTimeTo !=null "  >
            <![CDATA[ and last_update_time <=  #{submitTimeTo}  ]]>
        </if>
        <if test="notCancle != null and notCancle == '1'.toString()">
            and orderdetail_state not in ('0','3','5','9','10','11')
        </if>

        <if test="cancle != null and cancle == '1'.toString()">
            and orderdetail_state in ('0','3','5','9','10','11')
        </if>

        <if test="orderNum != null and orderNum != ''">
            and order_id =#{orderNum}
        </if>

    </select>
</mapper>
