<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.PayDetailMapper">

    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.PayDetail">
        select p.*,d.company_name_sc from pay_detail p inner join drugpur_orderdetail_recent d on p.order_detail_id = d.order_detail_id

        <where>
            <if test="invoice_id !=null and invoice_id !='' ">
               and invoice_id = #{invoice_id}
            </if>
        </where>
    </select>
</mapper>
