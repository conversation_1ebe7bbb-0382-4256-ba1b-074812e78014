<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDeductionItemMapper">



    <select id="queryList" resultType="map">
        SELECT
            s.*,
            s.id as 'DEDUCTION_ITEM_ID',
            ii.*,
            i.*,
            c.NAME AS 'CATALOG_NAME',
            e.COUNTRY,
            oi.SOURCE,
            sii.STOCK_IN_TIME,
            o.CREATION_TIME orderCreationTime,
            (
            30 - DATEDIFF( NOW(), sii.STOCK_IN_TIME )) overDay,
            ofn.NOTICE_NUM noticeNum,
            DATEDIFF( NOW(), sii.STOCK_IN_TIME ) stockDay,
            com.BANK_CARD,
            com.BANK_NAME
        FROM
            SUP_DEDUCTION_ITEM s
            LEFT JOIN  SUP_DEDUCTION d ON d.id=s.DEDUCTION_id
            LEFT JOIN SUP_INVOICE_ITEM ii ON ii.id = s.INVOICE_ITEM_ID
            LEFT JOIN sup_delivery_item di ON di.id = ii.DELIVERY_ITEM_ID
            INNER JOIN sup_order_item oi ON di.ORDER_ITEM_ID = oi.ID
            INNER JOIN sup_order o ON oi.ORDER_ID = o.ID
            INNER JOIN sup_stock_in_item sii ON di.id = sii.DELIVERY_ITEM_ID
            LEFT JOIN sup_drug_detail e ON e.id = ii.drug_id
            LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
            LEFT JOIN sup_invoice i ON ii.INVOICE_ID = i.id
            LEFT JOIN sup_official_hospital_notice ofn ON ii.id = ofn.BUSI_ID
            LEFT JOIN sup_delivery_company com ON s.DELIVERY_COMPANY_ID = com.id
        <where>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="deductionId != null and deductionId != ''">
                and s.DEDUCTION_ID = #{deductionId}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND s.CREATION_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="status != null and status != ''">
                <if test='status=="3,4"'>
                    and d.status in (3,4)
                </if>
                <if test='status!="3,4"'>
                    and d.status = #{status}
                </if>

            </if>
        </where>
        order by s.CREATION_TIME desc

    </select>
</mapper>
