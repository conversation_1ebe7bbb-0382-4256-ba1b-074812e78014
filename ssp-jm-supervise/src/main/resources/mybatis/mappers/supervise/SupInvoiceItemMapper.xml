<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupInvoiceItemMapper">

    <select id="getInvoiceItemDetail" resultType="map">
        select i.*,d.* ,c.NAME as 'catalog_name'  , v.APPROVAL_NUMBER,
               y.NAME AS 'drugCompanyName' from sup_invoice_item i
                                                    left join sup_delivery d on d.CODE=i.DELIVERY_CODE
                                                    left join sup_drug_detail v on v.id=i.drug_id
                                                    left join sup_drug_catalog c on c.id=v.CATALOG_ID
                                                    LEFT JOIN sup_drug_company y ON v.DRUG_COMPANY_ID=y.ID
        where i.ITEM_NO=#{invoiceCode}   order by i.INVOICE_DATE desc
    </select>


    <select id="getInvoiceItemListNew" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        d.code ,
        c.NAME AS 'catalogName',
        d.remark INVOICE_REMARK
        FROM
        sup_invoice_item i
        LEFT JOIN sup_invoice d ON i.INVOICE_ID = d.id
        LEFT JOIN sup_drug_detail e ON e.id = i.drug_id
        LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
        <where>
            <if test="catalogName != null and catalogName != ''">
                <bind name="hcatalogName" value="'%'+catalogName+'%'"/>
                and c.NAME like #{hcatalogName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and i.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="code != null and code != ''">
                and i.ITEM_NO = #{code}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and d.CODE = #{invoiceCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="country != null and country != ''">
                and e.country = #{country}
            </if>
            <if test="payStatus != null and payStatus != ''">
                and i.pay_status = #{payStatus}
            </if>
            <if test="payType != null and payType != '' and payType == '1'.toString()">
                and i.doc_info is not null and i.pay_status = '1'
            </if>
            <if test="payType != null and payType != '' and payType == '0'.toString()">
                and i.doc_info is null and i.pay_status = '1'
            </if>
            <if test="regionId != null and regionId != ''">
                and d.HOSPITAL_ID in (select id from sup_hospital where region_id = #{regionId})
            </if>
        </where>
        order by i.INVOICE_DATE desc
    </select>

    <select id="getInvoiceItemList" resultType="map">
        SELECT
        sii.STOCK_IN_CODE,
        sii.ITEM_NO,
        oi.CATALOG_NAME AS 'CATALOG_NAME',
        oi.COUNTRY_TAG,
        oi.SOURCE,
        i.HOSPITAL_ID,
        i.HOSPITAL_NAME,
        i.DELIVERY_NAME,
        sii.ORDER_CODE,
        ii.TAXES_AMOUNT,
        i.CODE,
        i.NO,
        ii.INVOICE_DATE,
        sii.STOCK_IN_TIME,
        ii.PAY_TIME,
        o.SUBMIT_TIME orderCreationTime,
        di.PAY_SURPLUS overDay,
        ofn.NOTICE_NUM noticeNum,
        DATEDIFF( NOW(), sii.STOCK_IN_TIME ) stockDay,
        oi.COUNTRY_BATCH batch
        FROM

        sup_stock_in_item sii
        LEFT JOIN sup_delivery_item di ON di.id = sii.DELIVERY_ITEM_ID
        INNER JOIN sup_order_item oi ON di.ORDER_ITEM_ID = oi.ID
        INNER JOIN sup_order o ON oi.ORDER_ID = o.ID
        INNER JOIN sup_invoice_item ii ON di.id = ii.DELIVERY_ITEM_ID
        LEFT JOIN sup_invoice i ON ii.INVOICE_ID = i.id
        LEFT JOIN sup_official_hospital_notice ofn ON ii.id = ofn.BUSI_ID
        <where>
            NOT EXISTS ( SELECT 1 FROM sup_settlement_item si WHERE ii.id = si.INVOICE_ITEM_ID )
            and o.ORDER_STATUS != '5'
            <if test="regionId != null and regionId != ''">
                and oi.REGION_ID = #{regionId}
            </if>
            <if test="payStatus != null and payStatus != ''">
                <if test='payStatus =="1"'>
                    and  ii.PAY_STATUS = #{payStatus}
                </if>

                <if test='payStatus =="0"'>
                    and  ii.PAY_STATUS != '1'
                </if>

            </if>
            <if test="source != null and source != ''">
                AND    oi.SOURCE=#{source}
            </if>
            <if test="country != null and country != ''">
                <if test='country =="1"'>
                    and  oi.COUNTRY_TAG = '1'
                </if>
            </if>
            <if test="deliveryItemId != null and deliveryItemId != ''">
                and ii.DELIVERY_ITEM_ID = #{deliveryItemId}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and ii.ORDER_CODE = #{orderCode}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and ii.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and i.NO = #{invoiceCode}
            </if>
            <if test="itemNo != null and itemNo != ''">
                and ii.ITEM_NO = #{itemNo}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and i.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and i.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and i.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND  oi.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="stockInStartTime !=null and stockInStartTime !='' and stockInEndTime !=null and stockInEndTime !=''">
                AND sii.STOCK_IN_TIME BETWEEN #{stockInStartTime} and #{stockInEndTime}
            </if>
            <if test="orderStartTime !=null and orderStartTime !='' and orderEndTime !=null and orderEndTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{orderStartTime} and #{orderEndTime}
            </if>
            <if test="overFlag !=null and overFlag != ''">
                <if test='overFlag == "1"'>
                    AND ii.PAY_STATUS = '0'
                    AND di.PAY_SURPLUS &lt; 0
                </if>
                <if test='overFlag == "2"'>
                    AND ii.PAY_STATUS = '0'
                </if>
            </if>
            <if test="stockDay !=null and stockDay != ''">
                <if test='stockDay == "1"'>
                    AND  DATEDIFF( NOW(), sii.STOCK_IN_TIME ) &gt; 30
                </if>
                <if test='stockDay == "2"'>
                    AND  DATEDIFF( NOW(), sii.STOCK_IN_TIME ) &lt; 30
                </if>
            </if>
            <if test="decide != null and decide != ''">
                <if test = "decide == '0'.toString()">
                    and ii.PAY_TIME is null
                </if>
                <if test = "decide == '1'.toString()">
                    and ii.PAY_TIME is not null
                </if>
            </if>
            <if test="batch != null and batch != ''">
                and oi.COUNTRY_BATCH = #{batch}
            </if>

        </where>
        order by stockDay desc, ofn.NOTICE_NUM desc
    </select>


    <select id="getOverTimeNoPayList" resultType="map">
        SELECT
        di.ID deliveryItemId,
        sii.STOCK_IN_TIME stockInTime,
        ( DATEDIFF( NOW(), sii.STOCK_IN_TIME )) overDay,
        e.batch batch
        FROM
        sup_delivery_item di
        INNER JOIN sup_stock_in_item sii ON di.id = sii.DELIVERY_ITEM_ID
        INNER JOIN sup_invoice_item ii ON di.id = ii.DELIVERY_ITEM_ID
        INNER JOIN sup_order_item oi on di.ORDER_ITEM_ID = oi.ID
        left join sup_drug_detail e on e.id=di.DRUG_ID
        LEFT JOIN sup_drug_batch b ON b.code=e.batch
        WHERE
        oi.ORDER_ITEM_STATUS not in ('0','5')  and ii.PAY_STATUS != '1' and oi.STOCK_STATUS != '0'
        <if test="isCountry != null and isCountry != ''">
            and  e.COUNTRY = #{isCountry}
        </if>
        <if test="submitFrom != null and submitFrom != ''">
            and  oi.SUBMIT_TIME > #{submitFrom}
        </if>
    </select>

    <select id="getNoPayOnItemNoPay" resultType="com.inspur.ssp.supervise.bean.entity.SupInvoiceItem">
        SELECT
        ii.* ,oi.PAY_STATUS
        FROM
        sup_invoice_item ii
        LEFT JOIN sup_order_item oi ON ii.ORDER_ITEM_ID = oi.ID
        WHERE
        1 = 1
        AND ii.PAY_STATUS = '0'
        AND oi.PAY_STATUS = '1'
        and ii.DOC_INFO is not null
        <if test="country != null and country != ''">
            and oi.COUNTRY_TAG = #{country}
        </if>
        <if test="orderNum != null and orderNum != ''">
            and oi.ORDER_NUM = #{orderNum}
        </if>

    </select>

    <update id="flushCountryInvoiceItem" parameterType="map">
        update sup_invoice_item i set i.DRUG_ID = #{detailId}
        where  i.DRUG_ID = #{oldDetailId}
    </update>
</mapper>
