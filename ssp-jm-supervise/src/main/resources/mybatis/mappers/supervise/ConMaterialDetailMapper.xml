<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialDetailMapper">

    <select id="selectConMaterialList" resultType="com.inspur.ssp.supervise.bean.vo.ConMaterialDetailVo">
        SELECT
            d.*,b.BATCH_NAME as batchName
        FROM CON_MATERIAL_VIEW d
        LEFT JOIN con_material_batch b ON d.BATCH=b.CODE
        <where>
            <if test="name !=null and name !=''">
                <bind name="dName" value="'%'+name+'%'"/>
                AND d.NAME LIKE #{dName,jdbcType=VARCHAR}
            </if>
            <if test="regCredName !=null and regCredName !=''">
                <bind name="dregCredName" value="'%'+regCredName+'%'"/>
                AND d.REG_CRED_NAME LIKE #{dregCredName,jdbcType=VARCHAR}
            </if>
            <if test="conCompanyName !=null and conCompanyName !=''">
                <bind name="dconCompanyName" value="'%'+conCompanyName+'%'"/>
                AND d.CON_COMPANY_NAME LIKE #{dconCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="regCredNum !=null and regCredNum !=''">
                <bind name="dregCredNum" value="'%'+regCredNum+'%'"/>
                AND d.REG_CRED_NUM LIKE #{dregCredNum,jdbcType=VARCHAR}
            </if>
            <if test="regCredModel !=null and regCredModel !=''">
                <bind name="dregCredModel" value="'%'+regCredModel+'%'"/>
                AND d.REG_CRED_MODEL LIKE #{dregCredModel,jdbcType=VARCHAR}
            </if>
            <if test="regCredSpec !=null and regCredSpec !=''">
                <bind name="dregCredSpec" value="'%'+regCredSpec+'%'"/>
                AND d.REG_CRED_SPEC LIKE #{dregCredSpec,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="batch !=null and batch !=''">
                AND d.BATCH = #{batch,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC
    </select>

    <select id="getConMaterialDetailById" resultType="map">
        select d.*,dc.name as conCompanyName,b.BATCH_NAME as batchName
        from con_material_detail d
        left join con_material_batch b on d.BATCH=b.CODE
        left join sup_drug_company dc on d.CON_COMPANY_ID = dc.ID
        where d.ID=#{id,jdbcType=VARCHAR}
    </select>

    <select id="getConMaterialCountBySource" resultType="Map">
        SELECT
            SOURCE AS 'source',
            count(*) AS 'count',
            d.ITEM_NAME AS 'sourceName'
        FROM
            SUP_DRUG_SOURCE s
        LEFT JOIN (
            SELECT
                ITEM_VALUE,ITEM_NAME FROM  SUP_DICT_ITEM
            WHERE DICT_ID =(SELECT ID FROM SUP_DICT WHERE CODE = 'SOURCE')
        ) d ON s.SOURCE = d.ITEM_VALUE
        RIGHT JOIN con_material_view v on v.CODE=s.DRUG_CODE
        <where>
            <if test="name !=null and name !=''">
                <bind name="dName" value="'%'+name+'%'"/>
                AND v.NAME LIKE #{dName,jdbcType=VARCHAR}
            </if>
            <if test="regCredName !=null and regCredName !=''">
                <bind name="dregCredName" value="'%'+regCredName+'%'"/>
                AND v.REG_CRED_NAME LIKE #{dregCredName,jdbcType=VARCHAR}
            </if>
            <if test="conCompanyName !=null and conCompanyName !=''">
                <bind name="dconCompanyName" value="'%'+conCompanyName+'%'"/>
                AND v.CON_COMPANY_NAME LIKE #{dconCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="regCredNum !=null and regCredNum !=''">
                <bind name="dregCredNum" value="'%'+regCredNum+'%'"/>
                AND v.REG_CRED_NUM LIKE #{dregCredNum,jdbcType=VARCHAR}
            </if>
            <if test="regCredModel !=null and regCredModel !=''">
                <bind name="dregCredModel" value="'%'+regCredModel+'%'"/>
                AND v.REG_CRED_MODEL LIKE #{dregCredModel,jdbcType=VARCHAR}
            </if>
            <if test="regCredSpec !=null and regCredSpec !=''">
                <bind name="dregCredSpec" value="'%'+regCredSpec+'%'"/>
                AND v.REG_CRED_SPEC LIKE #{dregCredSpec,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND v.status=#{status,jdbcType=VARCHAR} and   s.STATUS = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY s.SOURCE
    </select>
</mapper>
