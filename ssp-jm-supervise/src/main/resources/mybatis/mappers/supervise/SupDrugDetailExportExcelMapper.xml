<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugDetailExportExcelMapper">



    <select id="selectDrugListJcExportExcel" resultType="com.inspur.ssp.supervise.bean.entity.SupDrugDetailExportExcel">
        SELECT
        d.*,
        c. NAME AS 'catalogName',
        dc. NAME AS 'DRUG_COMPANY_NAME',
        b.BATCH_NAME
        FROM
        sup_drug_detail d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
        INNER JOIN sup_drug_batch_db db ON db.detail_id = d.id
        LEFT JOIN sup_drug_catalog c ON c.id = d.catalog_id
        LEFT JOIN sup_drug_company dc ON dc.id = d.drug_company_id
        LEFT JOIN sup_drug_batch b ON db.batch_code = b.CODE
        WHERE
        d.CODE IS NOT NULL AND s.DRUG_CODE IS NOT NULL AND d.COUNTRY='1'

        <if test="batch !=null and batch !=''">
            and db.BATCH_CODE = #{batch,jdbcType=VARCHAR}
        </if>

        <if test="catalogId !=null and catalogId !=''">
            AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName !=null and goodsName !=''">
            <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
            AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
        </if>
        <if test="catalogName !=null and catalogName !=''">
            <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
            AND c.name LIKE #{dcatalogName,jdbcType=VARCHAR}
        </if>
        <if test="standardCode !=null and standardCode !=''">
            <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
            AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
        </if>
        <if test="specs !=null and specs !=''">
            <bind name="dspecs" value="'%'+specs+'%'"/>
            AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
        </if>
        <if test="drugCompanyName !=null and drugCompanyName !=''">
            <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
            AND dc.NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="approvalNumber !=null and approvalNumber !=''">
            <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
            AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
        </if>
        <if test="dosageForm !=null and dosageForm !=''">
            <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
            AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
        </if>
        <if test="status !=null and status !=''">
            AND d.STATUS = #{status,jdbcType=CHAR}
        </if>
        <if test="packingSpecs !=null and packingSpecs !=''">
            <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
            AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
        </if>
        <if test='country=="2" and hospitalId !=null and hospitalId !=""'>
            AND h.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <if test='attribute !=null and attribute !=""'>
            AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
        </if>
        <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
            AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
        </if>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>

    <select id="selectDrugListExportExcel" parameterType="map" resultType="com.inspur.ssp.supervise.bean.entity.SupDrugDetailExportExcel">
        SELECT
        d.ID,
        d.CODE,
        d.COUNTRY_TYPE,
        d.CATALOG_ID,
        d.COUNTRY,
        d.DOSAGE_FORM,
        d.GOODS_NAME,
        d.SPECS,
        d.APPROVAL_NUMBER,
        d.STATUS,
        d.CATALOG_NAME,
        d.UNIT,
        d.PACKING_SPECS,
        d.DRUG_COMPANY_NAME,
        <if test='country=="2"'>
            h.HOSPITAL_ID,
            h.HOSPITAL_NAME,
        </if>
        d.STANDARD_CODE,
        d.BATCH,
        <if test="batch !=null and batch !=''">
            #{batch} BATCH_CODE,
        </if>
        d.ATTRIBUTE,
        d.QUALITY_LEVEL,
        d.MEDICAL_INSURANCE_CODE,
        d.PACKING,
        b.BATCH_NAME
        FROM DRUG_VIEW d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
        LEFT JOIN sup_drug_batch b ON d.BATCH = b.CODE
        <if test='country=="2"'>
            LEFT JOIN SUP_HOSPITAL_DRUG h  ON h.drug_id=d.id
        </if>
        <where>
            <if test="countryType !=null and countryType !=''">
                AND d.COUNTRY_TYPE = #{countryType,jdbcType=VARCHAR}
            </if>
            <if test="batch !=null and batch !=''">
                and d.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})
            </if>
            <if test="hospitalName !=null and hospitalName !=''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                AND  h.HOSPITAL_NAME LIKE #{dhospitalName,jdbcType=VARCHAR}
            </if>
            <if test="country !=null and country !=''">
                AND d.COUNTRY = #{country,jdbcType=VARCHAR}
            </if>
            <if test="catalogId !=null and catalogId !=''">
                AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND d.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR} and s.STATUS = #{status,jdbcType=CHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
            <if test='country=="2" and hospitalId !=null and hospitalId !=""'>
                AND h.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='attribute !=null and attribute !=""'>
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>


</mapper>
