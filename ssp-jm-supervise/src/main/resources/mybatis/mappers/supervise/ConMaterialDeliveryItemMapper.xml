<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialDeliveryItemMapper">

    <select id="getConMaterialDeliveryItemList" resultType="map">
        SELECT
        i.*,
        d.HOSPITAL_NAME,
        d.DELIVERY_NAME,
        e.NAME,
        e.COUNTRY,
        e.APPROVAL_NUMBER,
        y.NAME AS 'conCompanyName',
        s.STOCK_STATUS
        FROM
        con_material_delivery_item i
        LEFT JOIN con_material_delivery d ON i.DELIVERY_ID = d.id
        LEFT JOIN con_material_detail e ON e.id = i.DETAIL_ID
        LEFT JOIN con_material_stock_in_item s ON s.DELIVERY_ITEM_ID = i.ID
        LEFT JOIN sup_drug_company y ON e.CON_COMPANY_ID=y.ID
        <where>
            <if test="name != null and name != ''">
                <bind name="hName" value="'%'+name+'%'"/>
                and e.NAME like #{hName}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and i.ORDER_CODE = #{orderCode}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and i.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and d.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and d.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.DELIVERY_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="orderItemId != null and orderItemId != ''">
                and i.ORDER_ITEM_ID = #{orderItemId}
            </if>
        </where>
        order by i.DELIVERY_TIME desc,d.HOSPITAL_NAME desc
    </select>

    <select id="getConMaterialDeliveryItemDetail" resultType="map">
      select
        d.* ,v.*,s.STOCK_STATUS
      from con_material_delivery_item  d
      left join con_material_detail v on d.DETAIL_ID=v.id
      left join con_material_stock_in_item s on s.DELIVERY_ITEM_ID=d.ID
      where d.DELIVERY_CODE=#{deliveryCode}
      order by d.DELIVERY_TIME desc
    </select>



    <select id="getMaterialSourceAmountByHospital" resultType="com.inspur.ssp.supervise.bean.vo.MaterialSourceAmountVo">
        SELECT
        sri.REGION_NAME AS 'regionName',
        o.HOSPITAL_NAME AS 'hospitalName',
        sum(
        IFNULL( s.AMOUNT, 0 )) AS 'totalPrice',
        sum( IFNULL( s.NUM, 0 ) * d.FACTOR ) AS 'totalPackingAmount',
        sum(
        IF
        ( d.COUNTRY = '1', s.AMOUNT, 0 )) AS 'countryPrice',
        sum(
        IFNULL( d.FACTOR, 0 ) *
        IF
        ( d.COUNTRY = '1', IFNULL( s.NUM, 0 ), 0 )) ' countryPackingAmount',
        sum(
        IF
        ( d.COUNTRY = '0', s.AMOUNT, 0 )) AS 'noCountryPrice',
        sum(
        IFNULL( d.FACTOR, 0 ) *
        IF
        ( d.COUNTRY = '0', IFNULL( s.NUM, 0 ), 0 )) 'noCountryPackingAmount'
        FROM
        con_material_delivery_item s
        LEFT JOIN con_material_delivery o ON o.id = s.DELIVERY_ID
        LEFT JOIN sup_hospital h ON h.ID = o.HOSPITAL_ID
        LEFT JOIN con_material_detail d ON s.DETAIL_ID = d.id
        LEFT JOIN sup_region_info sri on sri.REGION_CODE = h.REGION_ID
        <where>

            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                 o.DELIVERY_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source !=null and source !='' ">
                AND o.SOURCE =#{source}
            </if>
            <if test="attribute !=null and attribute !=''">
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
            <if test="regionId != null and regionId != ''">
                and h.REGION_ID = #{regionId}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and h.id = #{hospitalId}
            </if>
            <if test="userHospitalId != null and userHospitalId != ''">
                and o.HOSPITAL_ID = #{userHospitalId}
            </if>
        </where>
        GROUP BY
        o.HOSPITAL_NAME
        ORDER BY h.SORT_ORDER ASC
    </select>
</mapper>
