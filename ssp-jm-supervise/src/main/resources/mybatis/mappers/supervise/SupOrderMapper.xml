<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupOrderMapper">

    <select id="selectOrderDrugList" resultType="com.inspur.ssp.supervise.bean.vo.SupOrderDrugVo" parameterType="map">
        SELECT
        d.COUNTRY,
        d.CATALOG_NAME,
        d.SPECS,
        d.DOSAGE_FORM,
        d.CATEGORY,
        d.PACKING_SPECS
        FROM
        drug_view d
        WHERE
        d.`STATUS` = '1'
        <if test="country !=null and country != ''">
            and d.COUNTRY = #{country}
        </if>
        <if test="catalogName !=null and catalogName != ''">
            <bind name="dCatalogName" value="'%'+catalogName+'%'"/>
            and d.CATALOG_NAME like #{dCatalogName}
        </if>
        <if test="specs !=null and specs != ''">
            <bind name="dSpecs" value="'%'+specs+'%'"/>
            and d.SPECS like #{dSpecs}
        </if>
        <if test="dosageForm !=null and dosageForm != ''">
            <bind name="dDosageForm" value="'%'+dosageForm+'%'"/>
            and d.DOSAGE_FORM like #{dDosageForm}
        </if>
        <if test="category !=null and category != ''">
            and d.CATEGORY = #{category}
        </if>
        GROUP BY
        d.COUNTRY,
        d.CATALOG_NAME,
        d.SPECS,
        d.DOSAGE_FORM,
        d.CATEGORY,
        d.PACKING_SPECS
        ORDER BY
        d.CATALOG_NAME
    </select>

    <select id="contrastPrice" resultType="com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo">
        SELECT
        d.*,
        ds.ID sourceId,
        ds.SOURCE,
        ds.BUSI_CODE busiCode,
        dp.ID priceId,
        dp.PRICE unitPrice
        FROM
        drug_view d
        LEFT JOIN sup_drug_source ds ON d.`CODE` = ds.DRUG_CODE
        LEFT JOIN drug_price_view dp ON dp.SOURCE_ID = ds.ID
        WHERE
        ds.`STATUS` = '1'
        AND dp.`STATUS` = '1'

        <if test="catalogId !=null and catalogId != ''">
            and d.CATALOG_ID = #{catalogId}
        </if>


        <if test="dosageFormGroup !=null and dosageFormGroup != ''">
            and d.DOSAGE_FORM_GROUP like #{dosageFormGroup}
        </if>

        order by dp.PRICE asc
    </select>

    <select id="getOrderNumByHospital"  resultType="com.inspur.ssp.supervise.bean.vo.SupOrderHospitalVo">
	SELECT
        O.ID AS "id",
		O.HOSPITAL_NAME AS "hospitalName",
        O.SOURCE AS "source",
		COUNT( * ) AS "totalCount",
		COUNT( CASE WHEN O.WARNING != '1' THEN 'warning' END ) "warningCount",
		COUNT( CASE WHEN O.WARNING = '1' THEN 'normal' END ) "normalCount"
	FROM
		SUP_ORDER O
        <where>
            O.STATUS='1'
        <if test="startTime !=null and startTime !='' ">
            <![CDATA[ and O.SUBMIT_TIME  >=  #{startTime}  ]]>
        </if>
        <if test="endTime !=null and endTime!='' "  >
            <![CDATA[ and O.SUBMIT_TIME <=  #{endTime}  ]]>
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
            and o.HOSPITAL_NAME like #{dhospitalName}
        </if>
        <if test="source !=null and source!='' "  >
            and O.SOURCE = #{source}
        </if>

            <if test="regionId !=null and regionId!='' "  >
                and O.region_id = #{regionId}
            </if>
        </where>
	GROUP BY
		O.HOSPITAL_NAME,O.SOURCE
    ORDER BY
        O.HOSPITAL_NAME DESC,totalCount DESC
  </select>



    <select id="getOrderNumBySource"  resultType="Map">
        SELECT
        O.SOURCE AS "source",
        COUNT( * ) AS "totalCount",
        COUNT( CASE WHEN O.WARNING != '1' THEN 'warning' END ) "warningCount",
        COUNT( CASE WHEN O.WARNING = '1' THEN 'normal' END ) "normalCount"
        FROM
        SUP_ORDER O
        <where>
            O.STATUS='1'
            <if test="startTime !=null and startTime !='' ">
                <![CDATA[ and O.SUBMIT_TIME  >=  #{startTime}  ]]>
            </if>
            <if test="endTime !=null and endTime!='' "  >
                <![CDATA[ and O.SUBMIT_TIME <=  #{endTime}  ]]>
            </if>
        </where>
        GROUP BY
        O.SOURCE
        ORDER BY
        totalCount DESC
    </select>


    <select id="getOrderCount"  resultType="Map">
        SELECT
        COUNT( * ) AS "totalCount",
        COUNT( CASE WHEN O.WARNING != '1' THEN 'warning' END ) "warningCount",
        COUNT( CASE WHEN O.WARNING = '1' THEN 'normal' END ) "normalCount"
        FROM
        SUP_ORDER O
        <where>
            O.STATUS='1'
            <if test="startTime !=null and startTime !='' ">
                <![CDATA[ and O.SUBMIT_TIME  >=  #{startTime}  ]]>
            </if>
            <if test="endTime !=null and endTime!='' "  >
                <![CDATA[ and O.SUBMIT_TIME <=  #{endTime}  ]]>
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
            <if test="source !=null and source!='' "  >
                and O.SOURCE = #{source}
            </if>
            <if test="regionId !=null and regionId!='' "  >
                and O.REGION_ID = #{regionId}
            </if>
        </where>
        ORDER BY
        totalCount DESC
    </select>

    <select id="getCityData"  resultType="Map">
        SELECT
            count( DISTINCT ( s.DETAIL_ID )) AS 'totalCount',
            count( DISTINCT IF ( s.SOURCE = '1', s.DETAIL_ID, null )) AS 'szTotalCount',
            count( DISTINCT IF ( s.SOURCE = '2', s.DETAIL_ID, null )) AS 'gdTotalCount',
            count( DISTINCT IF ( s.SOURCE = '3', s.DETAIL_ID, null )) AS 'gzTotalCount',
            count( DISTINCT IF ( s.SOURCE = '4', s.DETAIL_ID, null )) AS 'offlineTotalCount',

            sum( IFNULL ( s.ITEM_PRICE, 0 )) AS 'totalPrice',
            sum( IF ( s.SOURCE = '1', s.ITEM_PRICE, null )) AS 'szTotalPrice',
            sum( IF ( s.SOURCE = '2', s.ITEM_PRICE, null )) AS 'gdTotalPrice',
            sum( IF ( s.SOURCE = '3', s.ITEM_PRICE, null )) AS 'gzTotalPrice',
            sum( IF ( s.SOURCE = '4', s.ITEM_PRICE, null )) AS 'offlineTotalPrice',

            count(s.ID) AS 'totalItemCount',
            count(IF ( locate('1',o.WARNING) > 0 OR o.WARNING is null OR o.WARNING = '', s.ID, null )) AS 'zcItemCount',
            count(IF ( locate('2',o.WARNING) > 0, s.ID, null )) AS 'tjItemCount',
            count(IF ( locate('6',o.WARNING) > 0, s.ID, null )) AS 'psItemCount',
            count(IF ( locate('7',o.WARNING) > 0, s.ID, null )) AS 'rkItemCount',
            count(IF ( locate('8',o.WARNING) > 0, s.ID, null )) AS 'zfItemCount'
        FROM
            sup_order_item s
            LEFT JOIN sup_order o ON o.id = s.order_id
        WHERE
            s.ORDER_ITEM_STATUS NOT IN ( '5' )
            and o.STATUS='1' and s.STATUS='1'
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>

        <if test="regionId != null and regionId != ''">
            and s.REGION_ID = #{regionId}
        </if>
    </select>

    <select id="getOrderPriceData"  resultType="Map">
        SELECT
            date_format( o.SUBMIT_TIME, '%Y-%m' ) AS submitTime,
            sum( IFNULL ( s.ITEM_PRICE, 0 )) AS 'totalPrice',
            sum( IF ( s.SOURCE = '1', s.ITEM_PRICE, 0 )) AS 'szTotalPrice',
            sum( IF ( s.SOURCE = '2', s.ITEM_PRICE, 0 )) AS 'gdTotalPrice',
            sum( IF ( s.SOURCE = '3', s.ITEM_PRICE, 0 )) AS 'gzTotalPrice',
            sum( IF ( s.SOURCE = '4', s.ITEM_PRICE, 0 )) AS 'offlineTotalPrice'
        FROM
            sup_order_item s
            right JOIN sup_order o ON o.id = s.order_id
        WHERE
            s.ORDER_ITEM_STATUS NOT IN ( '5' )
            and o.STATUS='1' and s.STATUS='1'
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
        <if test="regionId != null and regionId != ''">
            and s.REGION_ID = #{regionId}
        </if>
        GROUP BY
                date_format( o.SUBMIT_TIME, '%Y-%m' )
        ORDER BY
                date_format( o.SUBMIT_TIME, '%Y-%m' ) ASC
    </select>


    <select id="getOrderInvoiceList" resultType="com.inspur.ssp.supervise.bean.vo.SupOrderInvoiceVo" >
        SELECT
            o.HOSPITAL_NAME,
            o.SOURCE,
            count(*) ORDER_COUNT,
            sum( o.TOTAL_PRICE ) TOTAL_PRICE,
            IFNULL((SELECT	sum( i.TAXES_AMOUNT ) 	FROM  sup_invoice_item i
                LEFT JOIN sup_invoice s on i.INVOICE_ID=s.ID
                LEFT JOIN sup_order_item d on i.order_item_id=d.id
                <where>
                    i.pay_status='1'  AND s.HOSPITAL_ID= o.HOSPITAL_ID and d.source=o.source
                    <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                        AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
                    </if>
                </where>
                GROUP BY d.source,s.HOSPITAL_ID
                 ),0)  HAVE_PAY
        FROM
            sup_order o
            LEFT JOIN SUP_HOSPITAL H ON o.HOSPITAL_ID = H.ID
        <where>
            and o.STATUS='1'
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and o.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="regionId != null and regionId != ''">
                and H.REGION_ID = #{regionId}
            </if>
        </where>
        GROUP BY
            o.HOSPITAL_ID,
            o.SOURCE
        ORDER BY  H.SORT_ORDER ASC
    </select>

    <select id="getOrderListToExcel" resultType="com.inspur.ssp.supervise.bean.dto.purchase.SupOrderExportExcel">
        SELECT
        d.ORDER_NUM,
        d.SOURCE,
        d.HOSPITAL_NAME,
        d.TOTAL_PRICE,
        d.ORDER_STATUS,
        d.STOCK_STATUS,
        d.PAY_STATUS,
        d.WARNING,
        d.SUBMIT_TIME
        FROM
        sup_order d

        <where>
            and o.STATUS='1'
            <if test="orderStatus != null and orderStatus != ''">
                and d.ORDER_STATUS = #{orderStatus}
            </if>
            <if test="orderNum != null and orderNum != ''">
                <bind name="horderCode" value="'%'+orderNum+'%'"/>
                and d.ORDER_NUM like #{horderCode}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                and d.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and d.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="warning != null and warning != ''">
                <if test="warning==-1">
                    AND d.WARNING!='1'
                </if>
                <if test="warning==1">
                    AND d.WARNING='1'
                </if>
            </if>
        </where>
        order by d.SUBMIT_TIME desc,d.HOSPITAL_NAME ASC
    </select>

    <select id="updateWarning">
        update sup_order set warning = #{warning} where id = #{id}
    </select>

    <select id="getOrderNotExistsDeliveryItem" resultType="com.inspur.ssp.supervise.bean.entity.SupOrder">
        SELECT o.ORDER_NUM FROM sup_order o
        LEFT JOIN sup_order_item oi ON oi.ORDER_ID=o.ID
        WHERE o.SOURCE=1
        AND oi.HOSPITAL_NAME IS NULL AND o.ORDER_NUM='D24040100252'
        GROUP BY o.ID,o.ORDER_NUM,o.SUBMIT_TIME,oi.SUBMIT_TIME,oi.HOSPITAL_NAME
        ORDER BY o.SUBMIT_TIME DESC;
    </select>

</mapper>
