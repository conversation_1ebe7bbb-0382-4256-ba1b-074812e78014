<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugPriceMapper">
        <select id="getDrugPrice" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo">
            SELECT s.DRUG_CODE as DRUG_CODE ,p.version as VERSION,s.SOURCE as SOURCE,s.ID as SOURCE_ID,s.BUSI_CODE as BUSI_CODE,p.PRICE as PRICE
            FROM  SUP_DRUG_SOURCE s
            LEFT JOIN DRUG_PRICE_VIEW p ON s.ID=p.SOURCE_ID
            <where>
                1=1 and p.PRICE!=''
                <if test="drugCode !=null and drugCode !=''">
                    and s.DRUG_CODE=#{drugCode,jdbcType=VARCHAR}
                </if>
                <if test="busiCode !=null and busiCode !=''">
                    and s.BUSI_CODE=#{busiCode,jdbcType=VARCHAR}
                </if>
            </where>
        </select>

    <select id="getDrugPriceList" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo">
        SELECT s.DRUG_CODE as DRUG_CODE ,p.version as VERSION,s.SOURCE as SOURCE,s.ID as SOURCE_ID,s.BUSI_CODE as BUSI_CODE,p.PRICE as PRICE
        FROM  SUP_DRUG_SOURCE s
        LEFT JOIN DRUG_PRICE_VIEW p ON s.ID=p.SOURCE_ID
    </select>
</mapper>
