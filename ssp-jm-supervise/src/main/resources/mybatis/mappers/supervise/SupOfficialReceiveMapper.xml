<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupOfficialReceiveMapper">
    <select id="getOfficialLReceiveList" resultType="com.inspur.ssp.supervise.bean.vo.SupOfficialReceiveVo">
            SELECT r.ID,r.OFFICIAL_ID,r.USER_ID,r.IS_SEE,r.SEE_TIME,r.CREATION_TIME,s.TITLE as TITLE,s.CONTENT as CONTENT,u.NAME as USER_NAME,d.NAME as DEPT_NAME
            FROM  SUP_OFFICIAL_RECEIVE r
            LEFT JOIN SUP_OFFICIAL s ON s.ID=r.OFFICIAL_ID
            LEFT JOIN PUB_USER  u ON u.ID=r.USER_ID
            LEFT JOIN PUB_DEPT  d ON d.CODE=u.DEPT_CODE
        <where>
            r.STATUS='1'
            <if test="isSee != null and isSee != ''">
                AND r.IS_SEE = #{isSee,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != ''">
                AND r.USER_ID= #{userId,jdbcType=VARCHAR}
            </if>
            <if test="officialId != null and officialId != ''">
                AND r.OFFICIAL_ID = #{officialId,jdbcType=VARCHAR}
            </if>
            <if test="title !=null and title !=''">
                <bind name="dtitle" value="'%'+title+'%'"/>
                AND s.TITLE LIKE #{dtitle,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY r.CREATION_TIME desc,r.SEE_TIME DESC
        </select>
</mapper>
