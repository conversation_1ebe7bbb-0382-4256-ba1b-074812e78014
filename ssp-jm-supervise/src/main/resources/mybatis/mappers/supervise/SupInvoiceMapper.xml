<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupInvoiceMapper">

    <select id="update412Invoice" resultType="map">
        SELECT * from sup_invoice i

        LEFT JOIN (SELECT ii.INVOICE_ID, ii.DOC_INFO,ii.PAY_TIME, sum(ii.TAXES_AMOUNT) sumTaxes from sup_invoice_item ii GROUP BY ii.INVOICE_ID ) t

        on i.id = t.INVOICE_ID

        WHERE i.TAXES_AMOUNT &lt; t.sumTaxes
        <if test="invoiceNo != null and invoiceNo != ''">
            and i.no = #{invoiceNo}
        </if>
        <if test="invoiceCode != null and invoiceCode != ''">
            and i.code = #{invoiceCode}
        </if>

    </select>

    <select id="getList" resultType="com.inspur.ssp.supervise.bean.entity.SupInvoice">
        SELECT * from sup_invoice i
        WHERE 1=1
        <if test="invoiceCode != null and invoiceCode != ''">
            and i.no = #{invoiceCode}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and i.HOSPITAL_ID = #{hospitalId}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
            and i.HOSPITAL_NAME like #{hHospitalName}
        </if>
        <if test="deliveryName != null and deliveryName != ''">
            <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
            and i.DELIVERY_NAME like #{hDeliveryName}
        </if>
        <if test="payStatus != null and payStatus != ''">
            and i.STATUS = #{payStatus}
        </if>
        <if test='country != null and country == "1"'>
            and exists (select ii.ID from sup_invoice_item ii LEFT JOIN DRUG_VIEW v on ii.DRUG_ID = v.ID where ii.INVOICE_ID = i.ID AND v.country = '1')
        </if>
        <if test='country != null and country == "0"'>
            and exists (select ii.ID from sup_invoice_item ii LEFT JOIN DRUG_VIEW v on ii.DRUG_ID = v.ID where ii.INVOICE_ID = i.ID AND v.country != '1')
        </if>
        <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
            AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
        </if>
        ORDER BY INVOICE_DATE DESC
    </select>
</mapper>
