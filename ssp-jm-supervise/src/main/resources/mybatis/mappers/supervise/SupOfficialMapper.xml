<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupOfficialMapper">
    <select id="getOfficialList" resultType="com.inspur.ssp.supervise.bean.entity.SupOfficial">
        SELECT
            DISTINCT s.*
        FROM
            SUP_OFFICIAL s
        LEFT JOIN SUP_OFFICIAL_RECEIVE r ON s.ID = r.OFFICIAL_ID
        <where>
            s.STATUS != '-1'
            <if test="status != null and status != ''">
                AND s.STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != ''">
                <bind name="dtitle" value="'%'+title+'%'"/>
                AND s.TITLE LIKE #{dtitle,jdbcType=VARCHAR}
            </if>
            <if test="sendUerId != null and sendUerId != ''">
                AND s.SEND_USER_ID = #{sendUerId,jdbcType=VARCHAR}
            </if>
            <if test="receiveUserId != null and receiveUserId != ''">
                AND r.USER_ID = #{receiveUserId,jdbcType=VARCHAR}
            </if>
            <if test="startSendTime !=null and startSendTime !='' and endSendTime !=null and endSendTime !=''">
                AND s.SEND_TIME between #{startSendTime} and #{endSendTime}
            </if>
        </where>
        ORDER BY s.LAST_MODIFITION_TIME DESC
    </select>
</mapper>
