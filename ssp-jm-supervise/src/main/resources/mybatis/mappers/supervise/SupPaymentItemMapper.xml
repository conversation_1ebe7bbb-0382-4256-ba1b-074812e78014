<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupPaymentItemMapper">
    <select id="getPaymentItemList" resultType="map">
        SELECT
            pi.*,
            si.INVOICE_CODE,
            si.INVOICE_NO,
            si.HOSPITAL_ID,
            si.HOSPITAL_NAME,
            si.ORDER_NUM,
            si.DELIVERY_ID,
            si.DELIVERY_NAME,
            ii.NUM,
            ii.UNIT_PRICE,
            ii.TAXES_AMOUNT,
            di.DELIVERY_CODE,
            c.NAME AS 'CATALOG_NAME',
            e.COUNTRY,
            oi.SOURCE,
            sii.STOCK_IN_TIME,
            o.SUBMIT_TIME orderCreationTime,
            ofn.NOTICE_NUM noticeNum,
            DATEDIFF( NOW(), sii.STOCK_IN_TIME ) stockDay
        FROM
            sup_payment_item pi
        LEFT JOIN sup_settlement_item si on pi.SETTLEMENT_ITEM_ID = si.ID
            LEFT JOIN sup_invoice_item ii on si.INVOICE_ITEM_ID = ii.id
            LEFT JOIN sup_delivery_item di on di.id = ii.DELIVERY_ITEM_ID
            INNER JOIN sup_order_item oi on di.ORDER_ITEM_ID = oi.ID
            INNER JOIN sup_order o on oi.ORDER_ID = o.ID
            INNER JOIN sup_stock_in_item sii ON di.id = sii.DELIVERY_ITEM_ID
            LEFT JOIN sup_drug_detail e ON e.id = ii.drug_id
            LEFT JOIN sup_drug_catalog c ON c.id = e.CATALOG_ID
            LEFT JOIN sup_invoice i ON ii.INVOICE_ID = i.id
            LEFT JOIN sup_official_hospital_notice ofn on ii.id = ofn.BUSI_ID
        <where>
            <if test="payStatus != null and payStatus != ''">
                <if test='payStatus =="1"'>
                    and  ii.PAY_STATUS = #{payStatus}
                </if>

                <if test='payStatus =="0"'>
                    and  ii.PAY_STATUS != '1'
                </if>

            </if>
            <if test="source != null and source != ''">
                AND    oi.SOURCE=#{source}
            </if>
            <if test="country != null and country != ''">
                and  e.COUNTRY = #{country}
            </if>
            <if test="deliveryItemId != null and deliveryItemId != ''">
                and ii.DELIVERY_ITEM_ID = #{deliveryItemId}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and ii.ORDER_CODE = #{orderCode}
            </if>
            <if test="deliveryCode != null and deliveryCode != ''">
                and ii.DELIVERY_CODE = #{deliveryCode}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and i.NO = #{invoiceCode}
            </if>
            <if test="itemNo != null and itemNo != ''">
                and ii.ITEM_NO = #{itemNo}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and i.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="hHospitalName" value="'%'+hospitalName+'%'"/>
                and i.HOSPITAL_NAME like #{hHospitalName}
            </if>
            <if test="deliveryName != null and deliveryName != ''">
                <bind name="hDeliveryName" value="'%'+deliveryName+'%'"/>
                and i.DELIVERY_NAME like #{hDeliveryName}
            </if>
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND i.INVOICE_DATE BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND  c.NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="stockInStartTime !=null and stockInStartTime !='' and stockInEndTime !=null and stockInEndTime !=''">
                AND sii.STOCK_IN_TIME BETWEEN #{stockInStartTime} and #{stockInEndTime}
            </if>
            <if test="orderStartTime !=null and orderStartTime !='' and orderEndTime !=null and orderEndTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{orderStartTime} and #{orderEndTime}
            </if>
            <if test="overFlag !=null and overFlag != ''">
                <if test='overFlag == "1"'>
                    AND ii.PAY_STATUS = '0'
                    AND di.PAY_SURPLUS &lt; 0
                    and oi.ORDER_ITEM_STATUS != '0' and oi.PAY_STATUS != '1' and oi.STOCK_STATUS != '0'
                </if>
                <if test='overFlag == "2"'>
                    AND ii.PAY_STATUS = '0'
                    and oi.ORDER_ITEM_STATUS != '0' and oi.PAY_STATUS != '1' and oi.STOCK_STATUS != '0'
                </if>
            </if>
            <if test="stockDay !=null and stockDay != ''">
                <if test='stockDay == "1"'>
                    AND  DATEDIFF( NOW(), sii.STOCK_IN_TIME ) &gt; 30
                </if>
                <if test='stockDay == "2"'>
                    AND  DATEDIFF( NOW(), sii.STOCK_IN_TIME ) &lt; 30
                </if>
            </if>
            <if test="itemStatus !=null and itemStatus != ''">
                AND pi.status = #{itemStatus}
            </if>
            <if test="paymentId !=null and paymentId != ''">
                AND pi.PAYMENT_ID = #{paymentId}
            </if>
            <if test="paymentIds !=null and paymentIds.size() > 0">
                AND pi.PAYMENT_ID IN
                <foreach collection="paymentIds" item="paymentId" index="index" open="(" close=")" separator=",">
                    #{paymentId}
                </foreach>
            </if>
        </where>
        order by stockDay desc, ofn.NOTICE_NUM desc
    </select>
</mapper>
