<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupContractInfoMapper">


    <select id="getSupContractInfoYbCodeNull" resultType="map">
        SELECT d.ID,MAX(c.contract_id) AS contractId  FROM sup_contract_info c
        LEFT JOIN sup_drug_detail d ON c.drug_detail_id=d.ID
        WHERE c.source='2' AND  d.MEDICAL_INSURANCE_CODE IS NULL OR d.MEDICAL_INSURANCE_CODE=''
        GROUP BY d.ID;
    </select>
</mapper>
