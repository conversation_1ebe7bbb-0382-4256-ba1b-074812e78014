<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugSourceMapper">

    <select id="getDrugCountBySource" resultType="Map">
        SELECT
        SOURCE AS 'source',
        count(*) AS 'count',
        d.ITEM_NAME AS 'sourceName'
        FROM
        SUP_DRUG_SOURCE s
        LEFT JOIN (
        SELECT
        ITEM_VALUE,ITEM_NAME FROM  SUP_DICT_ITEM
        WHERE DICT_ID =(SELECT ID FROM SUP_DICT WHERE CODE = 'SOURCE')
        ) d ON s.SOURCE = d.ITEM_VALUE
        LEFT JOIN drug_view v on v.CODE=s.DRUG_CODE
        <where>
            <if test="country!='' and country!=null">
                AND   v.COUNTRY=#{country}
            </if>

            <if test="batch !=null and batch !=''">
                AND v.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})

            </if>
            <if test="status !=null and status !=''">
                AND v.status=#{status,jdbcType=VARCHAR} and   s.STATUS = #{status,jdbcType=VARCHAR}
            </if>
            <if test="category !=null and category !=''">
                AND v.CATEGORY = #{category,jdbcType=VARCHAR}
            </if>
            <if test="country !=null and country !=''">
                AND v.COUNTRY = #{country,jdbcType=VARCHAR}
            </if>
            <if test="source !=null and source !=''">
                AND s.SOURCE = #{source,jdbcType=VARCHAR}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND v.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="catalogId !=null and catalogId !=''">
                AND v.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND v.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND v.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND v.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND v.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND v.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND v.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND v.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="attribute !=null and attribute !=''">
                AND v.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND v.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>

        </where>
        GROUP BY s.SOURCE
    </select>


    <select id="getDrugCountBySourceJc" resultType="Map">
        SELECT
            s.SOURCE AS 'source',
            count(*) AS 'count',
            t.ITEM_NAME AS 'sourceName'
        FROM
        sup_drug_detail d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
            LEFT JOIN (
                SELECT
                    ITEM_VALUE,ITEM_NAME FROM  SUP_DICT_ITEM
                WHERE DICT_ID =(SELECT ID FROM SUP_DICT WHERE CODE = 'SOURCE')
            ) t ON s.SOURCE = t.ITEM_VALUE
            INNER JOIN sup_drug_batch_db db ON db.detail_id = d.ID
        LEFT JOIN sup_drug_catalog c ON c.id = d.catalog_id
        LEFT JOIN sup_drug_company dc ON dc.id = d.drug_company_id
        <where>
        d.CODE IS NOT NULL AND s.DRUG_CODE IS NOT NULL
         <if test="country!='' and country!=null">
           AND   d.COUNTRY=#{country}
         </if>

        <if test="batch !=null and batch !=''">
            AND d.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})

        </if>
        <if test="status !=null and status !=''">
            AND d.status=#{status,jdbcType=VARCHAR}
        </if>
        <if test="category !=null and category !=''">
            AND c.CATEGORY = #{category,jdbcType=VARCHAR}
        </if>
        <if test="source !=null and source !=''">
            AND s.SOURCE = #{source,jdbcType=VARCHAR}
        </if>
        <if test="catalogName !=null and catalogName !=''">
            <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
            AND c.NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
        </if>
        <if test="catalogId !=null and catalogId !=''">
            AND c.ID = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName !=null and goodsName !=''">
            <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
            AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
        </if>
        <if test="standardCode !=null and standardCode !=''">
            <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
            AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
        </if>
        <if test="packingSpecs !=null and packingSpecs !=''">
            <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
            AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
        </if>
        <if test="specs !=null and specs !=''">
            <bind name="dspecs" value="'%'+specs+'%'"/>
            AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
        </if>
        <if test="drugCompanyName !=null and drugCompanyName !=''">
            <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
            AND dc.NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="approvalNumber !=null and approvalNumber !=''">
            <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
            AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
        </if>
        <if test="dosageForm !=null and dosageForm !=''">
            <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
            AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
        </if>
        <if test="attribute !=null and attribute !=''">
            AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
        </if>
        <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
            AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
        </if>

        </where>
        GROUP BY s.SOURCE
  </select>

</mapper>
