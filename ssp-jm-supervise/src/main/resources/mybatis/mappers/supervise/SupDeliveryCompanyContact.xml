<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDeliveryCompanyContactMapper">


    <select id="getCompanyContact" resultType="map">
        SELECT
        c.HOSPITAL_ID,
        c.HOSPITAL_NAME,
        c.COMPANY_NAME,
        c.ADDRES,
        c.CONTACTS,
        c.PHONE
        FROM
        sup_delivery_company_contact c
        <where>

            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and c.HOSPITAL_NAME like #{dhospitalName}
            </if>
        </where>

    </select>
</mapper>