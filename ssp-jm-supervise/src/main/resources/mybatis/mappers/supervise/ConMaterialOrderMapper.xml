<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.ConMaterialOrderMapper">

    <select id="getConMaterialSourceAmountByCity" resultType="map">
        SELECT
            sum(IFNULL( s.ITEM_PRICE, 0 )) AS 'totalPrice',
            sum(IFNULL( s.AMOUNT, 0 )) AS 'totalAmount',
            count(DISTINCT(s.DETAIL_ID)) AS 'totalCount'
        FROM
            con_material_order_item s
            LEFT JOIN con_material_order o ON o.id = s.order_id
        <where>
            s.ORDER_ITEM_STATUS NOT IN ('5')
            <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
                AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
            </if>
            <if test="source !=null and source !='' ">
                AND o.SOURCE =#{source}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                and o.HOSPITAL_NAME like #{dhospitalName}
            </if>
        </where>
    </select>

    <select id="getConMaterialOrderPriceData"  resultType="Map">
        SELECT
            date_format( o.SUBMIT_TIME, '%Y-%m' ) AS submitTime,
            sum( IFNULL ( s.ITEM_PRICE, 0 )) AS 'totalPrice'
        FROM
            con_material_order_item s
            right JOIN con_material_order o ON o.id = s.order_id
        WHERE
            s.ORDER_ITEM_STATUS NOT IN ( '5' )
        <if test="startTime !=null and startTime !='' and endTime !=null and endTime !=''">
            AND o.SUBMIT_TIME BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            and o.HOSPITAL_ID = #{hospitalId}
        </if>
        GROUP BY
            date_format( o.SUBMIT_TIME, '%Y-%m' )
        ORDER BY
            date_format( o.SUBMIT_TIME, '%Y-%m' ) ASC
    </select>
</mapper>
