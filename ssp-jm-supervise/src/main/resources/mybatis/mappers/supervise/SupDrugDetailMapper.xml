<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inspur.ssp.supervise.mapper.SupDrugDetailMapper">

    <update id="updateSourceStatusBatch" >
        update sup_drug_source s   set s.status='0'
        where s.source=#{source}
    </update>

    <update id="updateDrugStatusBatch" >
        update sup_drug_detail d  left join  sup_drug_source s on s.drug_code=d.code set d.status='0'
        where s.source=#{source}
    </update>

    <select id="selectDrugListJc" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo">
        SELECT
        d.*, c. NAME AS 'catalogName',
        dc. NAME AS 'DRUG_COMPANY_NAME'
        FROM
        sup_drug_detail d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
        INNER JOIN sup_drug_batch_db db ON db.detail_id = d.id
        LEFT JOIN sup_drug_catalog c ON c.id = d.catalog_id
        LEFT JOIN sup_drug_company dc ON dc.id = d.drug_company_id
        WHERE
        d.CODE IS NOT NULL AND s.DRUG_CODE IS NOT NULL AND d.COUNTRY='1'

            <if test="batch !=null and batch !=''">
                and db.BATCH_CODE = #{batch,jdbcType=VARCHAR}
            </if>

            <if test="catalogId !=null and catalogId !=''">
                AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND c.name LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND dc.NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
            <if test='country=="2" and hospitalId !=null and hospitalId !=""'>
                AND h.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='attribute !=null and attribute !=""'>
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>
    <select id="selectDrugList" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo">
        SELECT
        d.ID,
        d.CODE,
        d.COUNTRY_TYPE,
        d.CATALOG_ID,
        d.COUNTRY,
        d.DOSAGE_FORM,
        d.GOODS_NAME,
        d.SPECS,
        d.APPROVAL_NUMBER,
        d.STATUS,
        d.CATALOG_NAME,
        d.UNIT,
        d.PACKING_SPECS,
        d.DRUG_COMPANY_NAME,
        <if test='country=="2"'>
            h.HOSPITAL_ID,
            h.HOSPITAL_NAME,
        </if>
        d.STANDARD_CODE,
        d.BATCH,
        <if test="batch !=null and batch !=''">
            #{batch} BATCH_CODE,
        </if>
        d.ATTRIBUTE,
        d.QUALITY_LEVEL,
        d.MEDICAL_INSURANCE_CODE,
        d.PACKING
        FROM DRUG_VIEW d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
        <if test='country=="2"'>
            LEFT JOIN SUP_HOSPITAL_DRUG h  ON h.drug_id=d.id
        </if>
        <where>
            <if test="countryType !=null and countryType !=''">
                AND d.COUNTRY_TYPE = #{countryType,jdbcType=VARCHAR}
            </if>
            <if test="batch !=null and batch !=''">
                and d.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})
            </if>
            <if test="hospitalName !=null and hospitalName !=''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                AND  h.HOSPITAL_NAME LIKE #{dhospitalName,jdbcType=VARCHAR}
            </if>
            <if test="country !=null and country !=''">
                AND d.COUNTRY = #{country,jdbcType=VARCHAR}
            </if>
            <if test="catalogId !=null and catalogId !=''">
                AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND d.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR} and s.STATUS = #{status,jdbcType=CHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
            <if test='country=="2" and hospitalId !=null and hospitalId !=""'>
                AND h.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='attribute !=null and attribute !=""'>
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>

    <select id="selectDrugListExportExcel" parameterType="map" resultType="com.inspur.ssp.supervise.bean.entity.SupDrugDetailExportExcel">
        SELECT
        d.ID,
        d.CODE,
        d.COUNTRY_TYPE,
        d.CATALOG_ID,
        d.COUNTRY,
        d.DOSAGE_FORM,
        d.GOODS_NAME,
        d.SPECS,
        d.APPROVAL_NUMBER,
        d.STATUS,
        d.CATALOG_NAME,
        d.UNIT,
        d.PACKING_SPECS,
        d.DRUG_COMPANY_NAME,
        <if test='country=="2"'>
            h.HOSPITAL_ID,
            h.HOSPITAL_NAME,
        </if>
        d.STANDARD_CODE,
        d.BATCH,
        <if test="batch !=null and batch !=''">
            #{batch} BATCH_CODE,
        </if>
        d.ATTRIBUTE,
        d.QUALITY_LEVEL,
        d.MEDICAL_INSURANCE_CODE,
        d.PACKING,
        b.BATCH_NAME
        FROM DRUG_VIEW d
        LEFT JOIN SUP_DRUG_SOURCE s ON s.DRUG_CODE=d.CODE
        LEFT JOIN sup_drug_batch b ON d.BATCH = b.CODE
        <if test='country=="2"'>
            LEFT JOIN SUP_HOSPITAL_DRUG h  ON h.drug_id=d.id
        </if>
        <where>
            <if test="countryType !=null and countryType !=''">
                AND d.COUNTRY_TYPE = #{countryType,jdbcType=VARCHAR}
            </if>
            <if test="batch !=null and batch !=''">
                and d.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})
            </if>
            <if test="hospitalName !=null and hospitalName !=''">
                <bind name="dhospitalName" value="'%'+hospitalName+'%'"/>
                AND  h.HOSPITAL_NAME LIKE #{dhospitalName,jdbcType=VARCHAR}
            </if>
            <if test="country !=null and country !=''">
                AND d.COUNTRY = #{country,jdbcType=VARCHAR}
            </if>
            <if test="catalogId !=null and catalogId !=''">
                AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="catalogName !=null and catalogName !=''">
                <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
                AND d.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR} and s.STATUS = #{status,jdbcType=CHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
            <if test='country=="2" and hospitalId !=null and hospitalId !=""'>
                AND h.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='attribute !=null and attribute !=""'>
                AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
            </if>
            <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
                AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>

    <select id="selectDrugListWithSource" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo">
        SELECT
        d.ID,
        d.CODE,
        d.COUNTRY_TYPE,
        d.CATALOG_ID,
        d.COUNTRY,
        d.DOSAGE_FORM,
        d.GOODS_NAME,
        d.SPECS,
        d.APPROVAL_NUMBER,
        d.STATUS,
        d.CATALOG_NAME,
        d.UNIT,
        d.PACKING_SPECS,
        d.DRUG_COMPANY_NAME,
        d.STANDARD_CODE,
        c.SOURCE AS "SOURCE",
        c.PRICE AS "PRICE"
        FROM DRUG_VIEW d LEFT JOIN
        (select * from (select p.id,s.drug_code,p.price,s.source from drug_price_view  p left join sup_drug_source s on  p.source_id=s.id where s.status='1' and p.status='1') as table_1 where id = (
        select id from (select p.id,s.drug_code,p.price,s.source from drug_price_view  p left join sup_drug_source s on  p.source_id=s.id where s.status='1' and p.status='1' )as table_2 where table_2.drug_code=table_1.drug_code order by price asc , source asc limit 1 )) c  on c.drug_code=d.code
        <where>
            <if test="country !=null and country !=''">
                AND d.COUNTRY = #{country,jdbcType=VARCHAR}
            </if>
            <if test="catalogId !=null and catalogId !=''">
                AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
            </if>
            <if test="goodsName !=null and goodsName !=''">
                <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
                AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
            </if>
            <if test="standardCode !=null and standardCode !=''">
                <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
                AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
            </if>
            <if test="specs !=null and specs !=''">
                <bind name="dspecs" value="'%'+specs+'%'"/>
                AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
            </if>
            <if test="drugCompanyName !=null and drugCompanyName !=''">
                <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
                AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
            </if>
            <if test="approvalNumber !=null and approvalNumber !=''">
                <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
                AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
            </if>
            <if test="dosageForm !=null and dosageForm !=''">
                <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
                AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
            </if>
            <if test="status !=null and status !=''">
                AND d.STATUS = #{status,jdbcType=CHAR}
            </if>
            <if test="packingSpecs !=null and packingSpecs !=''">
                <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
                AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY d.LAST_MODIFITION_TIME DESC,d.STANDARD_CODE,d.SPECS ASC
    </select>

    <select id="getSupDrugListBySource" resultType="com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo">
        SELECT
        d.*,
        <if test="batch !=null and batch !=''">
            #{batch} BATCH_CODE,
        </if>
        ds.ID sourceId,
        ds.SOURCE,
        ds.BUSI_CODE busiCode,
        dp.ID priceId,
        dp.PRICE unitPrice,
        convert(dp.PRICE/(-(-d.PACKING_SPECS)),decimal(12,3)) specsPrice
        FROM
        sup_drug_source ds
        LEFT JOIN  drug_view d  ON d.CODE = ds.DRUG_CODE
        LEFT JOIN drug_price_view dp ON dp.SOURCE_ID = ds.ID
        WHERE
        d.CODE is not null
        <if test="batch !=null and batch !=''">
            and d.BATCH in (SELECT b.code FROM sup_drug_batch b where b.CODE = #{batch,jdbcType=VARCHAR} or b.RENEW_CODE = #{batch,jdbcType=VARCHAR})
        </if>
        <if test="status !=null and status !=''">
            AND d.status=#{status,jdbcType=VARCHAR} and   ds.STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="category !=null and category !=''">
            AND d.CATEGORY = #{category,jdbcType=VARCHAR}
        </if>
        <if test="country !=null and country !=''">
            AND d.COUNTRY = #{country,jdbcType=VARCHAR}
        </if>
        <if test="source !=null and source !=''">
            AND ds.SOURCE = #{source,jdbcType=VARCHAR}
        </if>
        <if test="catalogName !=null and catalogName !=''">
            <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
            AND d.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR}
        </if>
        <if test="catalogId !=null and catalogId !=''">
            AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName !=null and goodsName !=''">
            <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
            AND d.GOODS_NAME LIKE #{dgoodsName,jdbcType=VARCHAR}
        </if>
        <if test="standardCode !=null and standardCode !=''">
            <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
            AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
        </if>
        <if test="packingSpecs !=null and packingSpecs !=''">
            <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
            AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
        </if>
        <if test="specs !=null and specs !=''">
            <bind name="dspecs" value="'%'+specs+'%'"/>
            AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
        </if>
        <if test="drugCompanyName !=null and drugCompanyName !=''">
            <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
            AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="approvalNumber !=null and approvalNumber !=''">
            <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
            AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
        </if>
        <if test="dosageForm !=null and dosageForm !=''">
            <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
            AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
        </if>
        <if test="attribute !=null and attribute !=''">
            AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
        </if>
        <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
            AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
        </if>
        order by d.CATALOG_NAME desc
    </select>

    <select id="selectSupDrugList" resultType="com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo">
        SELECT
        d.*
        FROM
        sup_drug_select d
        where 1=1

        <if test="systemContrast !=null and systemContrast !=''">
            AND d.SYSTEM_CONTRAST = #{systemContrast,jdbcType=VARCHAR}
        </if>
        <if test="busiCode !=null and busiCode !=''">
            AND d.BUSI_CODE = #{busiCode,jdbcType=VARCHAR}
        </if>
        <if test="category !=null and category !=''">
            AND d.CATEGORY = #{category,jdbcType=VARCHAR}
        </if>
        <if test="country !=null and country !=''">
            AND d.COUNTRY = #{country,jdbcType=VARCHAR}
        </if>
        <if test="source !=null and source !=''">
            <if test='source =="123"'>
                AND d.SOURCE in ('1','2','3')
            </if>
            <if test='source !="123"'>
                AND d.SOURCE = #{source,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="catalogName !=null and catalogName !=''">
            <bind name="dcatalogName" value="'%'+catalogName+'%'"/>
            AND (d.CATALOG_NAME LIKE #{dcatalogName,jdbcType=VARCHAR} or d.CATALOG_NAME_GROUP LIKE #{dcatalogName,jdbcType=VARCHAR})
        </if>
        <if test="catalogId !=null and catalogId !=''">
            AND d.CATALOG_ID = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="goodsName !=null and goodsName !=''">
            <bind name="dgoodsName" value="'%'+goodsName+'%'"/>
            AND d.GOODS_NAME_GROUP LIKE #{dgoodsName,jdbcType=VARCHAR}
        </if>
        <if test="standardCode !=null and standardCode !=''">
            <bind name="dstandardCode" value="'%'+standardCode+'%'"/>
            AND d.STANDARD_CODE LIKE #{dstandardCode,jdbcType=VARCHAR}
        </if>
        <if test="packingSpecs !=null and packingSpecs !=''">
            <bind name="dpackingSpecs" value="'%'+packingSpecs+'%'"/>
            AND d.PACKING_SPECS LIKE #{dpackingSpecs,jdbcType=VARCHAR}
        </if>
        <if test="specs !=null and specs !=''">
            <bind name="dspecs" value="'%'+specs+'%'"/>
            AND d.SPECS LIKE #{dspecs,jdbcType=VARCHAR}
        </if>
        <if test="drugCompanyName !=null and drugCompanyName !=''">
            <bind name="ddrugCompanyName" value="'%'+drugCompanyName+'%'"/>
            AND d.DRUG_COMPANY_NAME LIKE #{ddrugCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="approvalNumber !=null and approvalNumber !=''">
            <bind name="dapprovalNumber" value="'%'+approvalNumber+'%'"/>
            AND d.APPROVAL_NUMBER LIKE #{dapprovalNumber,jdbcType=VARCHAR}
        </if>
        <if test="dosageForm !=null and dosageForm !=''">
            <bind name="ddosageForm" value="'%'+dosageForm+'%'"/>
            AND d.DOSAGE_FORM LIKE #{ddosageForm,jdbcType=VARCHAR}
        </if>
        <if test="attribute !=null and attribute !=''">
            AND d.ATTRIBUTE = #{attribute,jdbcType=VARCHAR}
        </if>
        <if test='medicalInsuranceCode !=null and medicalInsuranceCode !=""'>
            AND d.MEDICAL_INSURANCE_CODE = #{medicalInsuranceCode,jdbcType=VARCHAR}
        </if>
        ORDER BY d.CATALOG_NAME_GROUP DESC, d.QUALITY_LEVEL_GROUP, d.SPECS_GROUP DESC , d.DOSAGE_FORM_GROUP DESC , d.SPECS_PRICE ASC
    </select>



    <select id="getDrugDetailById" resultType="map">
        select c.NAME AS CATALOG_NAME,c.CATEGORY AS CATEGORY,d.COMPANY_ID AS COMPANY_ID,
               d.ID AS ID,d.CODE AS CODE,d.CATALOG_ID AS CATALOG_ID,d.COUNTRY AS COUNTRY,
               d.COUNTRY_TYPE AS COUNTRY_TYPE,d.PACKING_SPECS,d.ELECTION_SCOPE AS ELECTION_SCOPE,
               d.DOSAGE_FORM AS DOSAGE_FORM,d.GOODS_NAME AS GOODS_NAME,d.SPECS AS SPECS,
               d.SPECS_ATTR AS SPECS_ATTR,d.DRUG_COMPANY_ID AS DRUG_COMPANY_ID,
               dc.NAME AS DRUG_COMPANY_NAME,d.PACKING AS PACKING,d.UNIT AS UNIT,
               d.QUALITY_LEVEL AS QUALITY_LEVEL,d.APPROVAL_NUMBER AS APPROVAL_NUMBER,
               d.STANDARD_CODE AS STANDARD_CODE,d.MEDICAL_INSURANCE AS MEDICAL_INSURANCE,
               d.ATTRIBUTE AS ATTRIBUTE,d.ADJUVANT AS ADJUVANT,d.NET AS NET,d.COEFFICIENT AS COEFFICIENT,
               d.REMARK AS REMARK,d.VERSION AS VERSION,d.CREATION_TIME AS CREATION_TIME,d.CREATOR AS CREATOR,
               d.LAST_MODIFITOR AS LAST_MODIFITOR,d.LAST_MODIFITION_TIME AS LAST_MODIFITION_TIME,
               d.STATUS AS STATUS,d.BATCH AS BATCH,d.MEDICAL_INSURANCE_CODE AS MEDICAL_INSURANCE_CODE
        from sup_drug_detail d
                 left join sup_drug_catalog c on  d.CATALOG_ID = c.ID
                 left join sup_drug_company dc on d.DRUG_COMPANY_ID = dc.ID
        where d.ID=#{id,jdbcType=VARCHAR}
    </select>


    <select id="getSupDrugDetail" resultType="com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo">
        select d.*,c.name as catalog_name,dc.name as drug_company_name from sup_drug_detail d left join sup_drug_catalog c on d.catalog_id = c.id
        left join sup_drug_company dc on dc.id = d.DRUG_COMPANY_ID
        where 1 = 1
        <if test='guanlian !=null and guanlian !="" and guanlian == "0"'>
            and d.id not in (select detail_id from sup_drug_batch_db where 1 = 1
            <if test="batchCode !=null and batchCode !=''">
                and batch_code = #{batchCode}
            </if>
            )
        </if>

        <if test='guanlian !=null and guanlian !="" and guanlian == "1"'>
            and d.id in (select detail_id from sup_drug_batch_db where 1 = 1
                <if test="batchCode !=null and batchCode !=''">
                    and batch_code = #{batchCode}
                </if>
            )
        </if>

        <if test='name !=null and name !=""'>
            <bind name="name" value="'%'+name+'%'"/>
            and c.name like #{name}
        </if>

    </select>


    <select id="selectDrugCode" resultType="java.lang.String">
        SELECT
            d.SPECS
        FROM
            sup_drug_source ds
                LEFT JOIN  drug_view d  ON d.CODE = ds.DRUG_CODE
        WHERE
            ds.BUSI_CODE=#{busiCode,jdbcType=VARCHAR} AND d.CODE IS NOT NULL
    </select>


    <select id="selectDrugCodeList" resultType="com.inspur.ssp.supervise.bean.entity.SupDrugDetail">
        SELECT DISTINCT d.CODE
        FROM sup_drug_detail d
        WHERE d.CODE IN (
            SELECT s.DRUG_CODE
            FROM sup_drug_source s
            WHERE s.SOURCE = '1'
            GROUP BY s.DRUG_CODE
            HAVING COUNT(*) = 2
        )AND NOT EXISTS (SELECT 1 FROM sup_country_purchase p WHERE d.ID = p.DRUG_ID);
    </select>

    <select id="getByPurchaseDrugDetailId" resultType="com.inspur.ssp.supervise.bean.vo.PurchaseDrugDetailVo"
            parameterType="com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto">
        SELECT
        T1.*,
        sdi.DELIVERY_CODE,
        sd.DELIVERY_NAME,
        DATE_FORMAT( T1.Last_SubmitTime, '%Y-%m-%d' ) AS SUBMIT_TIME
        FROM
        (
        SELECT
        soi.`CODE`,
        soi.DETAIL_ID,
        soi.HOSPITAL_NAME,
        soi.UNIT_PRICE,
        soi.ORDER_NUM,
        soi.CATALOG_NAME,
        MAX( soi.SUBMIT_TIME ) AS Last_SubmitTime,
        soi.SOURCE,
        soi.ORDER_ITEM_STATUS
        FROM
        sup_order_item soi
        <where>
            <if test="detailId!=null and detailId!=''">
                soi.DETAIL_ID = #{detailId}
            </if>
            <if test="unitPrice!=null and unitPrice!=''">
                AND soi.UNIT_PRICE like concat('%', #{unitPrice},'%')
            </if>
            <if test="source!=null and source!=''">
                AND soi.source =#{source}
            </if>
            <if test="hospitalName!=null and hospitalName!=''">
                AND soi.HOSPITAL_NAME like concat('%', #{hospitalName},'%')
            </if>
            <if test="submitTime!=null and submitTime!=''">
                AND SUBMIT_TIME BETWEEN #{startSubmitTime} and #{endSubmitTime}
            </if>
        </where>
        GROUP BY
        soi.HOSPITAL_NAME,
        soi.UNIT_PRICE
        ) AS T1
        inner JOIN sup_delivery_item sdi ON sdi.ORDER_ITEM_NO = T1.`CODE`
        inner join sup_delivery sd on sd.id = sdi.DELIVERY_ID
        order by SUBMIT_TIME desc
    </select>




</mapper>
