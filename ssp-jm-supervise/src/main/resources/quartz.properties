# ¹Ì¶¨Ç°×ºorg.quartz
# Ö÷Òª·ÖÎªscheduler¡¢threadPool¡¢jobStore¡¢pluginµÈ²¿·Ö
#ÊµÀýÃû
org.quartz.scheduler.instanceName=Scheduler
#ÊµÀýid£¨Î¨Ò»£¬ÓÐÈ±Ê¡Öµ£©
org.quartz.scheduler.instanceId=SchedulerId
org.quartz.scheduler.rmi.export=false
org.quartz.scheduler.rmi.proxy=false
org.quartz.scheduler.wrapJobExecutionInUserTransaction=false
# ÊµÀý»¯ThreadPoolÊ±£¬Ê¹ÓÃµÄÏß³ÌÀàÎªSimpleThreadPool
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
# ²¢·¢¸öÊý
org.quartz.threadPool.threadCount=10
# ÓÅÏÈ¼¶
org.quartz.threadPool.threadPriority=5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
org.quartz.jobStore.misfireThreshold=5000
# Ä¬ÈÏ´æ´¢ÔÚÄÚ´æÖÐ
org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

