package com.inspur;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
/**
 * Created by zengwh on 2018/1/24.
 */
@SpringBootApplication(scanBasePackages = "org.jangod,com.inspur")
@MapperScan(value = "com.inspur")
@ServletComponentScan("org.jangod,com.inspur")
@EnableTransactionManagement
@EnableScheduling
@EnableCaching
public class StartupApplication implements ApplicationListener<WebServerInitializedEvent> {
    Logger logger = LoggerFactory.getLogger(StartupApplication.class);
    public static void main(String[] args) {
        SpringApplication.run(StartupApplication.class, args);
    }

    @Override
    public void onApplicationEvent(WebServerInitializedEvent webServerInitializedEvent) {
        logger.info("启动成功，监听在端口"+webServerInitializedEvent.getWebServer().getPort());
    }
}
