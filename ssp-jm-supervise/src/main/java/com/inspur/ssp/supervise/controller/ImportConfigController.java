package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.ImportConfig;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import com.inspur.ssp.supervise.service.IImportConfigService;
import com.inspur.ssp.supervise.utils.excel.ImportConfigUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@RestController
@RequestMapping("/supervise/importConfig")
public class ImportConfigController extends AbstractController {

    @ApiOperation(value = "根据业务类型获取加载组件路径")
    @RequestMapping("/componentPath")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "busiType", value = "业务类型", required = true, dataType = "String"),
    })
    public Result componentPath() {
        String busiType = this.getParameter("busiType");
        if(StringUtil.isBlank(busiType)){
            return ResultUtil.error("业务类型不能为空");
        }
        ImportConfig importConfig = ImportConfigUtil.getImportConfig(busiType);
        if (importConfig == null) {
            return ResultUtil.error("导入配置id不存在");
        }
        String componentName = importConfig.getComponentName();
        /*if(StringUtil.isBlank(componentName)){
            return ResultUtil.error("导入配置组件路径不能为空");
        }*/
        return ResultUtil.successToObject(componentName);
    }

}

