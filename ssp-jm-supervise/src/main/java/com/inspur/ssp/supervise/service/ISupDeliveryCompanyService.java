package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDeliveryCompany;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
public interface ISupDeliveryCompanyService extends IService<SupDeliveryCompany> {
     SupDeliveryCompany checkDeliveryCompany(String companyName,String companyNameCode);
     String createCode() ;
}
