package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupDeduction;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.service.ISupDeductionItemService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@RestController
@RequestMapping("/supervise/supDeductionItem")
public class SupDeductionItemController extends AbstractController {
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Autowired
    private ISupDeductionItemService supDeductionItemServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @ApiOperation(value = "获取扣款单明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<SupDeduction> queryWrapper = new QueryWrapper<>();
            String startTime=(String) params.get("creationTime[0]");
            String endTime=(String) params.get("creationTime[1]");
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
                params.put("startTime",startTime);
                params.put("endTime",endTime);

            }else if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    params.put("hospitalId",hospitalUser.getHospitalId());
                }
            }
            PageHelper.startPage(page, limit);
            List<Map<String,Object>> deductionItems = supDeductionItemServiceImpl.queryList(params);
            PageList<Map<String,Object>> selectPage = new PageList<>(deductionItems);
            return ResultUtil.successToList(selectPage);

    }
}

