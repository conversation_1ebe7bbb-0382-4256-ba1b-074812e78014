package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupInvoice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupStockIn;
import org.jangod.iweb.util.Tools;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 发票表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
public interface ISupInvoiceService extends IService<SupInvoice> {

   String createInvoiceCode();

   List<SupInvoice> getList(Map<String,Object> params);


}
