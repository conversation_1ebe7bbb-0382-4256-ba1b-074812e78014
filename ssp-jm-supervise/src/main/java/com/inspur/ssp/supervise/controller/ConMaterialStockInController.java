package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.inspur.ssp.supervise.bean.entity.ConMaterialStockIn;
import com.inspur.ssp.supervise.bean.entity.SupStockIn;
import com.inspur.ssp.supervise.service.IConMaterialStockInItemService;
import com.inspur.ssp.supervise.service.IConMaterialStockInService;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@RestController
@RequestMapping("/supervise/conMaterialStockIn")
public class ConMaterialStockInController extends AbstractController {

    @Autowired
    private IConMaterialStockInService conMaterialStockInServiceImpl;

    @Autowired
    private IConMaterialStockInItemService conMaterialStockInItemServiceImpl;

    @ApiOperation(value = "入库单详情")
    @RequestMapping("/show/{stockInCode}")
    public Result show(@PathVariable("stockInCode") String stockInCode) {
        try {
            Map<String, Object> map = new HashMap<>();
            LambdaQueryWrapper<ConMaterialStockIn> queryWrapper = new LambdaQueryWrapper<ConMaterialStockIn>().eq(ConMaterialStockIn::getCode, stockInCode);
            ConMaterialStockIn conMaterialStockIn = conMaterialStockInServiceImpl.getOne(queryWrapper);
            if(Objects.isNull(conMaterialStockIn)){
                return ResultUtil.error("无此入库单，请联系管理员");
            }else{
                map.put("stockInCode",stockInCode);
                List<Map<String, Object>> stockInItemDetail = conMaterialStockInItemServiceImpl.getConMaterialStockInItemList(map);
                map.put("data",conMaterialStockIn);
                map.put("stockInItem",stockInItemDetail);
            }

            return ResultUtil.successToObject(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }
}

