package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.math.NumberUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-07-29
 */
@RestController
@RequestMapping("/supervise/supInsuranceLog")
public class SupInsuranceLogController extends AbstractController {

    @Autowired
    private ISupInsuranceLogService supInsuranceLogServiceImpl;
    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;
    @Autowired
    private ISupDeductionService supDeductionServiceImpl;
    @Autowired
    private IProcessLogService processLogServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Autowired
    private ISupOrderService supOrderServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;

    @ApiOperation(value = "获取列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "hospitalName", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "orderNum", value = "订单编号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "invoiceItemNo", value = "发票编号", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String startTime=(String) params.get("payDate[0]");
        String endTime=(String) params.get("payDate[1]");
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime",startTime);
            params.put("endTime",endTime);

        }
        String roleValue = user.getRoleValue();
        if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
            if(Objects.isNull(hospitalUser)){
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            }else{
                params.put("hospitalId",hospitalUser.getHospitalId());
            }
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> invoiceItemList = supInsuranceLogServiceImpl.getInsuranceList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(invoiceItemList);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("保存医保基金支付记录")
    @PostMapping("/save")
    public Result save(@RequestBody SupInsuranceLog supInsuranceLog) {
        IUser user = this.getCurrentUser();
        SupInvoiceItem supInvoiceItem = new SupInvoiceItem();
        if (user == null) {
            ResultUtil.error("获取用户失败，请重新登录");
        } else {
            supInvoiceItem.setLastModifier(user.getId());
            supInvoiceItem.setLastModifiedTime(new Date());
        }
        if (StringUtil.isEmpty(supInsuranceLog.getBankCard())) {
            return ResultUtil.error("配送单位银行卡号不能为空");
        }
        if (Objects.isNull(supInsuranceLog.getAmount()) || supInsuranceLog.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            return ResultUtil.error("支付价格为大于0的金额。");
        }
        supInvoiceItem.setId(supInsuranceLog.getInvoiceItemId());
        supInvoiceItem.setPayTime(new Date());
        supInvoiceItem.setDocInfo("[]");
        supInvoiceItem.setPayPrice(supInsuranceLog.getAmount());
        supInvoiceItem.setRemark(supInsuranceLog.getRemark());
        supInvoiceItem.setInsurancePayFlag("1");

        Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);
        if (result.getState() == 0) {
            return result;
        }
        supInsuranceLog.setStatus("1");
        supInsuranceLog.setId(Tools.genId() + "");
        supInsuranceLog.setCreator(user.getId());
        supInsuranceLog.setCreationTime(new Date());
        supInsuranceLog.setLastModifier(user.getId());
        supInsuranceLog.setLastModifiedTime(new Date());
        supInsuranceLogServiceImpl.save(supInsuranceLog);
        return ResultUtil.success("支付成功");

    }


    @ApiOperation("保存医保基金支付记录多条")
    @PostMapping("/saveInsuranceLog")
    public Result saveInsuranceLog(@RequestBody JSONArray confirmDataList) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            ResultUtil.error("获取用户失败，请重新登录");
        }

        List<SupInsuranceLog> supInsuranceLogs = new ArrayList<>();
        String deductionId="";
        String deductionCode="";
        String hospitalName="";
        List<String> codes=new ArrayList<String>();
        for (Object o : confirmDataList) {
            JSONObject jo=(JSONObject) JSONObject.toJSON(o);
            deductionCode= (String) jo.get("deductionCode");
            deductionId = (String) jo.get("deductionId");
            hospitalName=(String) jo.get("hospitalName");
            String  invoiceItemId = (String) jo.get("invoiceItemId");
            String bankCard = (String) jo.get("bankCard");
            String bankName = (String) jo.get("bankName");
            String itemNo = (String) jo.get("itemNo");
            BigDecimal payPrice = NumberUtils.createBigDecimal(String.valueOf(jo.get("taxesAmount")));
            String id = (String) jo.get("deductionItemId");//扣款明细id
            String code = (String) jo.get("itemCode");//扣款明细code
            String orderItemId = (String) jo.get("orderItemId");//订单明细id

            if (StringUtil.isEmpty(bankCard)) {
                return ResultUtil.error("配送单位银行卡号不能为空");
            }
            if (Objects.isNull(payPrice) || payPrice.compareTo(BigDecimal.ZERO) == 0) {
                return ResultUtil.error("支付价格为大于0的金额。");
            }

            SupInvoiceItem supInvoiceItem = new SupInvoiceItem();
            supInvoiceItem.setLastModifier(user.getId());
            supInvoiceItem.setLastModifiedTime(new Date());
            supInvoiceItem.setId(invoiceItemId);
            supInvoiceItem.setPayTime(new Date());
            supInvoiceItem.setDocInfo("[]");
            supInvoiceItem.setPayPrice(payPrice);
            supInvoiceItem.setInsurancePayFlag("1");
            supInvoiceItem.setMany(false);
            Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);
            if (result.getState() == 0) {
                return result;
            }
            SupOrderItem supOrderItem= supOrderItemServiceImpl.getById(orderItemId);
            //更改订单明细预警超时未支付为已支付
            supOrderServiceImpl.updateOrderWarning(supOrderItem.getOrderId());

            SupInsuranceLog supInsuranceLog = new SupInsuranceLog();
            supInsuranceLog.setStatus("1");
            supInsuranceLog.setDeductionItemCode(code);
            supInsuranceLog.setDeductionCode(deductionCode);
            supInsuranceLog.setId(Tools.genId() + "");
            supInsuranceLog.setCreator(user.getId());
            supInsuranceLog.setCreationTime(new Date());
            supInsuranceLog.setLastModifier(user.getId());
            supInsuranceLog.setLastModifiedTime(new Date());
            supInsuranceLog.setBankName(bankName);
            supInsuranceLog.setBankCard(bankCard);
            supInsuranceLog.setInvoiceItemId(invoiceItemId);
            supInsuranceLog.setInvoiceItemNo(itemNo);
            supInsuranceLog.setAmount(payPrice);
            supInsuranceLog.setDeductionId(deductionId);
            supInsuranceLog.setDeductionItemId(id);
            String number = supInsuranceLog.getId() + "" + Tools.formatDate(new Date(), "yyyyMMdd") + "" + (int)(Math.random() * (9000) + 1000);

                QueryWrapper<SupInsuranceLog> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(SupInsuranceLog::getTransactionNumber, number);
                int count = supInsuranceLogServiceImpl.count(queryWrapper);
                while (count > 0) {
                     number = supInsuranceLog.getId() + "" + Tools.formatDate(new Date(), "yyyyMMdd") + "" + (int)(Math.random() * (9000) + 1000);
                    count = supInsuranceLogServiceImpl.count(queryWrapper);
                }
            supInsuranceLog.setTransactionNumber(number);
            supInsuranceLogs.add(supInsuranceLog);
            codes.add(number);

        }
        supInsuranceLogServiceImpl.saveBatch(supInsuranceLogs,supInsuranceLogs.size());
        //修改状态
        SupDeduction supDeduction = new SupDeduction();
        supDeduction.setId(deductionId);
        supDeduction.setCode(deductionCode);
        supDeduction.setHospitalName(hospitalName);
        supDeduction.setStatus(Constant.DEDUCTION_STATUS_PAY);//生成支付指令
        supDeduction.setLastModifitionTime(new Date());
        supDeduction.setLastModifitor(user.getId());
        supDeductionServiceImpl.updateById(supDeduction);


        //更新日志
        processLogServiceImpl.saveProcessLog(user, supDeduction.getStatus(),supDeduction.getId(),"1");

        Map<String, Object> map = new HashMap<>();
        map.put("supDeduction",supDeduction);
        map.put("transactionNumbers",codes);

        return ResultUtil.successToObject("支付成功",map);

    }
}

