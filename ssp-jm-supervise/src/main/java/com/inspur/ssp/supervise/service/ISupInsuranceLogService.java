package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupInsuranceLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2020-07-29
 */
public interface ISupInsuranceLogService extends IService<SupInsuranceLog> {

    List<Map<String,Object>> getInsuranceList(Map<String,Object> params);

}
