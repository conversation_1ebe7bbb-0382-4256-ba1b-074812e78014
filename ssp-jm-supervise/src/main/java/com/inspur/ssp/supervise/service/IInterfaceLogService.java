package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.InterfaceLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
public interface IInterfaceLogService extends IService<InterfaceLog> {

    boolean updateImportFlag(Map param);
    boolean updateSuccessImportFlag(Map param);

    Long selectByTaskTime(String taskTime);

}
