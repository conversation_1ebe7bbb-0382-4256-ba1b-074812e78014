package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupInvoiceItem;
import com.inspur.ssp.supervise.bean.entity.SupStockInItem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
public interface ISupStockInItemService extends IService<SupStockInItem> {
    List<Map<String,Object>> getStockInItemDetail(String stockInCode);

    Result saveStockVoucher(SupStockInItem supStockInItem) ;

    List<Map<String,Object>> getStockInItemList(Map<String,Object> params);


    List<SupStockInItem> getNoStockAndOnPay(Map<String,Object> params);

    void flushCountryStockInItem(Map<String, Object> upParams);
}
