package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugSource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
public interface ISupDrugSourceService extends IService<SupDrugSource> {

    List<Map<String, Object>>  getDrugCountBySource(Map<String,Object> params);
}
