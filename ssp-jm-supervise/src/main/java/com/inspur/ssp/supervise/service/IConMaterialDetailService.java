package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.ConMaterialDetail;
import com.inspur.ssp.supervise.bean.vo.ConMaterialDetailVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 药品清单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
public interface IConMaterialDetailService extends IService<ConMaterialDetail> {

    String createCode();

    /**
     * 获取耗材列表
     *
     * @return
     */
    List<ConMaterialDetailVo> selectConMaterialList(Map<String, Object> params);

    Map<String, Object> getConMaterialDetailById(String conMaterialDetailId);

    List<Map<String, Object>> getConMaterialCountBySource(Map<String, Object> params);

    /**
     * 根据busiCode获取产品
     * @param busiCode
     * @return
     */
    ConMaterialDetail selectByBusiCode(String busiCode);
}
