package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupDeliveryCompany;
import com.inspur.ssp.supervise.bean.entity.SupDrugCatalog;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import com.inspur.ssp.supervise.service.ISupDrugCatalogService;
import com.inspur.ssp.supervise.service.ISupDrugDetailService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 药品目录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@RestController
@RequestMapping("/supervise/supDrugCatalog")
public class SupDrugCatalogController extends AbstractController {
    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;
    @Autowired
    private ISupDrugCatalogService supDrugCatalogServiceImpl;
    @ApiOperation(value = "获取药品目录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "药品目录名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "category", value = "药品目录类别", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupDrugCatalog> queryWrapper = new LambdaQueryWrapper<SupDrugCatalog>()
                .orderByDesc(SupDrugCatalog::getLastModifitionTime);
        //查询字段
        String category = (String) params.get("category");  //查询类别
        String name = (String) params.get("name");   //查询名称
        if (StringUtil.isNotBlank(category)){
            queryWrapper.like(SupDrugCatalog::getCategory,category);
        }
        if (StringUtil.isNotBlank(name)){
            queryWrapper.like(SupDrugCatalog::getName, name);
        }
        PageHelper.startPage(page, limit);
        List<SupDrugCatalog> list = supDrugCatalogServiceImpl.list(queryWrapper);
        PageList<SupDrugCatalog> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("保存,修改药品目录")
    @RequestMapping("/edit")
    public Result edit(@ApiParam(value = "SupDrugCatalog药品目录对象", required = true) SupDrugCatalog supDrugCatalog) {
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            ResultUtil.error("获取用户失败，请重新登录");
        }else{
            supDrugCatalog.setCreator(currentUser.getId());
        }
        Map<String, Object> drugCatalog = supDrugCatalogServiceImpl.checkDrugCatalog(supDrugCatalog);
        String exist = (String) drugCatalog.get("exist");
        if (exist.equals("1")) {
            return ResultUtil.error("药品目录类别已存在");
        }else{
            return ResultUtil.success("操作成功");
        }
    }



    @ApiOperation(value = "药品目录详细信息")
    @RequestMapping("/info/{supDrugCatalogId}")
    public Result info(@ApiParam("药品目录id") @PathVariable("supDrugCatalogId") String supDrugCatalogId) {
        SupDrugCatalog supDrugCatalog = supDrugCatalogServiceImpl.getById(supDrugCatalogId);
        if (supDrugCatalog == null) {
            return ResultUtil.error("药品目录ID不存在");
        }
        return ResultUtil.successToObject(supDrugCatalog);
    }

    @ApiOperation(value = "删除药品目录")
    @RequestMapping("/delete/{supDrugCatalogId}")
    public Result deleteById(@ApiParam("药品目录ID") @PathVariable("supDrugCatalogId") String supDrugCatalogId) {
        //查询该目录下是否存在药品
        QueryWrapper<SupDrugDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("CATALOG_ID",supDrugCatalogId);
        int count = supDrugDetailServiceImpl.count(queryWrapper);
        if(count>0){
            return ResultUtil.error("该目录下存在药品，无法删除");
        }
        supDrugCatalogServiceImpl.removeById(supDrugCatalogId);
        return ResultUtil.success("删除成功");
    }

    @ApiOperation(value = "更新药品目录状态")
    @RequestMapping("/updateStatus")
    public  Result updateStatus(@ApiParam(value = "SupDrugCatalog药品目录对象",required = true) SupDrugCatalog supDrugCatalog){
        UpdateWrapper<SupDrugCatalog> updateWrapper = new UpdateWrapper<SupDrugCatalog>();
        updateWrapper.eq("ID", supDrugCatalog.getId());
        boolean update = supDrugCatalogServiceImpl.update(supDrugCatalog, updateWrapper);
        if(update){
            return ResultUtil.success("操作成功");
        }else{
            return ResultUtil.error("操作失败");
        }
    }

    @ApiOperation(value = "获取药品目录列表，不带分页")
    @RequestMapping("/getDrugCatalogList")
    public Result getDrugCatalogList(HttpServletRequest request) {

        QueryWrapper<SupDrugCatalog> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("ID","NAME","CATEGORY");
        if (StringUtil.isNotBlank(request.getParameter("name"))){
            queryWrapper.like("NAME",request.getParameter("name"));
        }

        String page = request.getParameter("page");
        String limit = request.getParameter("limit");

        if (StringUtils.isNotBlank(page) && StringUtils.isNotBlank(limit)) {
            PageHelper.startPage(Integer.parseInt(page),Integer.parseInt(limit));
        }

        List<SupDrugCatalog> list = supDrugCatalogServiceImpl.list(queryWrapper);
        PageList<SupDrugCatalog> supDrugCatalogPageList = new PageList<>(list);
        return ResultUtil.successToList(supDrugCatalogPageList);
    }

}

