package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseExcel;
import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseExportDto;
import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseSpeedExportDto;
import com.inspur.ssp.supervise.bean.dto.purchase.SupPurchaseInvoiceExcel;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.excel.CustomCellWriteHandler;
import com.inspur.ssp.supervise.utils.excel.ExcelUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.excel.metadata.Sheet;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.annotation.AepSecurity;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@RestController
@RequestMapping("/supervise/supCountryPurchase")
@Slf4j
public class SupCountryPurchaseController extends AbstractController {

    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Autowired
    private ISupCountryPurchaseService supCountryPurchaseServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupFileService supFileServiceImpl;
    @Autowired
    ICdBatchLogService cdBatchLogServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ISupDrugBatchService supDrugBatchServiceImpl;

    @ApiOperation(value = "获取约定采购量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "药品目录名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "year", value = "年份", required = false, dataType = "String"),
            @ApiImplicitParam(name = "batch", value = "批次", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String roleValue = "";
        String allType = (String)params.get("allType");
        String regionId = (String)params.get("regionId");
        IUser user = (IUser) this.getCurrentUser();
        if (StringUtil.isBlank(allType) || !allType.equals("all")) {

            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            } else {
                roleValue = user.getRoleValue();
                if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) &&!roleValue.contains(areaRegionAdmin)) {
                    SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                    if (Objects.isNull(hospitalUser)) {
                        return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                    } else {
                        params.put("hospitalId", hospitalUser.getHospitalId());
                    }
                }
            }
        }
        if (StringUtil.isNotBlank(regionId)) {
            params.put("regionId",regionId);
        }

        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }

        PageHelper.startPage(page, limit);  //进行分页
        List<Map<String, Object>> list = supCountryPurchaseServiceImpl.selectCountryPurchaseList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(list);
        return ResultUtil.successToList("查询成功", selectPage);
    }

    @ApiOperation(value = "获取约定采购量列表")
    @ApiImplicitParams({
            //日期
    })
    @RequestMapping("/getPurchaseList")
    public Result getPurchaseList() {
        Map<String, Object> params = this.getRequestParams();
        List<Map<String, Object>> list = supCountryPurchaseServiceImpl.selectCountryPurchaseList(params);
        return ResultUtil.successToList(list);
    }

    @ApiOperation(value = "获取约定采购量列表各医院")
    @RequestMapping("/getListByHospital")
    public Result getListByHospital() {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        String regionId = (String) params.get("regionId");
        String roleValue = "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        if (StringUtil.isNotBlank(regionId)) {
            params.put("regionId",regionId);
        }
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        List<Map<String, Object>> list = supCountryPurchaseServiceImpl.selectListByHospital(params);
        return ResultUtil.successToList(list);
    }

    @ApiOperation("保存约定采购量")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "约定采购量对象", required = true) SupCountryPurchase supCountryPurchase) {
        IUser currentUser = this.getCurrentUser();
        if (currentUser == null) {
            return ResultUtil.error("获取用户失败，请重新登录");
        }
        supCountryPurchase.setStatus("1");
        supCountryPurchase.setId(String.valueOf(Tools.genId()));
        supCountryPurchase.setCreationTime(new Date());
        supCountryPurchase.setLastModifitionTime(new Date());
        supCountryPurchase.setCreator(currentUser.getId());
        supCountryPurchase.setLastModifitor(currentUser.getId());
        supCountryPurchaseServiceImpl.save(supCountryPurchase);
        return ResultUtil.success("保存成功");
    }

    @ApiOperation("修改约定采购量")
    @RequestMapping("/update")
    public Result update(@ApiParam(value = "约定采购量对象", required = true) SupCountryPurchase supCountryPurchase) {
        IUser currentUser = this.getCurrentUser();
        if (currentUser == null) {
            return ResultUtil.error("获取用户失败，请重新登录");
        }
        supCountryPurchase.setStatus("1");
        supCountryPurchase.setLastModifitionTime(new Date());
        supCountryPurchase.setLastModifitor(currentUser.getId());
        supCountryPurchaseServiceImpl.updateById(supCountryPurchase);
        return ResultUtil.success("保存成功");
    }

    @ApiOperation(value = "约定采购量详细信息")
    @RequestMapping("/info/{id}")
    public Result info(@ApiParam("医院id") @PathVariable("id") String id) {
        SupCountryPurchase supCountryPurchase = supCountryPurchaseServiceImpl.getById(id);
        if (supCountryPurchase == null) {
            return ResultUtil.error("医院约定采购量不存在");
        }
        return ResultUtil.successToObject(supCountryPurchase);
    }

    @ApiOperation(value = "删除医院")
    @RequestMapping("/delete/{id}")
    public Result deleteById(@ApiParam("医院ID") @PathVariable("id") String id) {
        supCountryPurchaseServiceImpl.removeById(id);
        return ResultUtil.success("删除成功");
    }

    @ApiOperation("上传任务量明细excel")
    @RequestMapping("/importPurchase")
    public Result importPurchase(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_PURCHASE, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupCountryPurchaseExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }

    @ApiOperation("上传任务量进度excel")
    @RequestMapping("/importPurchaseSpeed")
    public Result importPurchaseSpeed(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }

            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_PURCHASE_SPEED, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupCountryPurchaseSpeedExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }

    @ApiOperation(value = "获取任务单号列表")
    @RequestMapping("/taskNumAll")
    public Result taskNumAll() {
        QueryWrapper<SupCountryPurchase> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("task_num");
        queryWrapper.groupBy("task_num");
        List<Map<String, Object>> taskNumList = supCountryPurchaseServiceImpl.listMaps(queryWrapper);
        return ResultUtil.successToObject(taskNumList);
    }

    /**
     * 完成情况
     *
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping("/exportPurchase")
    public void exportPurchase(HttpServletResponse response, HttpServletRequest request) throws IOException {
        Map<String, Object> params = this.getRequestParams();
        String userId = (String) params.get("userId");
        String roleValue = (String) params.get("roleValue");
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(roleValue)) {
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", userId));
                if (Objects.nonNull(hospitalUser)) {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        List<SupCountryPurchaseExcel> supCountryPurchaseExcels = supCountryPurchaseServiceImpl.selectListToExcel(params);

        for (SupCountryPurchaseExcel supCountryPurchaseExcel : supCountryPurchaseExcels) {
            String taskStartTime = supCountryPurchaseExcel.getTaskStartTime();
            String taskEndTime = supCountryPurchaseExcel.getTaskEndTime();
            supCountryPurchaseExcel.setTaskTIme(taskStartTime + "至" + taskEndTime);
            String taskStatus = supCountryPurchaseExcel.getTaskStatus();
            if ("0".equals(taskStatus)) {
                supCountryPurchaseExcel.setTaskStatus("正常");
            }
            if ("-1".equals(taskStatus)) {
                supCountryPurchaseExcel.setTaskStatus("紧急");
            }
            String totalDeliveryPurchase = supCountryPurchaseExcel.getTotalDeliveryPurchase();
            String totalStockPurchase = supCountryPurchaseExcel.getTotalStockPurchase();
            String purchase = supCountryPurchaseExcel.getPurchase();
            String totalPrice = supCountryPurchaseExcel.getTotalPrice();
            if (StringUtil.isBlank(totalDeliveryPurchase)) {
                totalDeliveryPurchase = "0";
            }
            if (StringUtil.isBlank(totalStockPurchase)) {
                totalStockPurchase = "0";
                supCountryPurchaseExcel.setTotalPurchase("0");
            }
            if (StringUtil.isBlank(totalPrice)) {
                supCountryPurchaseExcel.setTotalPrice("0");
            }
            BigDecimal totalDeliveryPurchaseNew = NumberUtils.createBigDecimal(totalDeliveryPurchase).setScale(4);
            BigDecimal purchaseDeliveryNew = NumberUtils.createBigDecimal(purchase).setScale(4);
            BigDecimal deliveryDivide = totalDeliveryPurchaseNew.divide(purchaseDeliveryNew, 2, BigDecimal.ROUND_DOWN);
            BigDecimal deliveryCompletion = deliveryDivide.multiply(new BigDecimal(100));
            supCountryPurchaseExcel.setDeliveryRate(deliveryCompletion + "%");


            BigDecimal totalPurchaseNew = NumberUtils.createBigDecimal(totalStockPurchase).setScale(4);
            BigDecimal purchaseNew = NumberUtils.createBigDecimal(purchase).setScale(4);
            BigDecimal divide = totalPurchaseNew.divide(purchaseNew, 2, BigDecimal.ROUND_DOWN);
            BigDecimal completion = divide.multiply(new BigDecimal(100));
            supCountryPurchaseExcel.setCompletionRate(completion + "%");
        }

        String fileName = URLEncoder.encode("集采完成情况数据", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), SupCountryPurchaseExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(SupCountryPurchaseExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet("集采完成情况数据")
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(supCountryPurchaseExcels);
    }

    /**
     * 回款情况
     *
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping("/purchaseInvoiceExport")
    public void purchaseInvoiceExport(HttpServletResponse response, HttpServletRequest request) throws IOException {
        Map<String, Object> params = this.getRequestParams();
        String userId = (String) params.get("userId");
        String roleValue = (String) params.get("roleValue");
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(roleValue)) {
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", userId));
                if (Objects.nonNull(hospitalUser)) {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        List<SupPurchaseInvoiceExcel> supCountryPurchaseExcels = supCountryPurchaseServiceImpl.selectPurchaseInvoiceToExcel(params);

        for (SupPurchaseInvoiceExcel supCountryPurchaseExcel : supCountryPurchaseExcels) {
            String taskStartTime = supCountryPurchaseExcel.getTaskStartTime();
            String taskEndTime = supCountryPurchaseExcel.getTaskEndTime();
            supCountryPurchaseExcel.setTaskTIme(taskStartTime + "至" + taskEndTime);


            String totalPurchase = supCountryPurchaseExcel.getTotalPurchase();//累计采购量
            String totalPrice = supCountryPurchaseExcel.getTotalPrice();//累计采购金额
            String havePay = supCountryPurchaseExcel.getTotalPayPrice();//已回款金额
            if (StringUtil.isBlank(totalPurchase)) {
                supCountryPurchaseExcel.setTotalPurchase("0");
            }
            if (StringUtil.isBlank(totalPrice)) {
                supCountryPurchaseExcel.setTotalPrice("0");
            }


            BigDecimal havePayNew = NumberUtils.createBigDecimal(havePay).setScale(4);
            BigDecimal totalPriceNew = NumberUtils.createBigDecimal(totalPrice).setScale(4);
            if (totalPriceNew.equals(BigDecimal.ZERO.setScale(4))) {
                supCountryPurchaseExcel.setHavePayRate("/");
            } else if (havePayNew.equals(BigDecimal.ZERO.setScale(4))) {
                supCountryPurchaseExcel.setHavePayRate("0.00%");
            } else {
                BigDecimal divide = havePayNew.divide(totalPriceNew, 2, BigDecimal.ROUND_DOWN);
                BigDecimal completion = divide.multiply(new BigDecimal(100));
                supCountryPurchaseExcel.setHavePayRate(completion + "%");
            }
        }

        String fileName = URLEncoder.encode("国家集采回款情况数据", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), SupPurchaseInvoiceExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(SupPurchaseInvoiceExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet("国家集采回款情况数据")
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(supCountryPurchaseExcels);
    }

    @ApiOperation(value = "批次汇总")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalName", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "batch", value = "批次", required = false, dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始日期", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束日期", required = false, dataType = "String"),
    })
    @GetMapping("/batchSummary")
    public Result batchSummary() {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        String roleValue = "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }

        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        Map<String, Object> data = supCountryPurchaseServiceImpl.batchSummary(params);
        return ResultUtil.successToObject(data);
    }


    /**
     * 批次汇总导出
     *
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping("/batchSummaryExport")
    public void batchSummaryExport(HttpServletResponse response, HttpServletRequest request) throws IOException, ParseException {
        Map<String, Object> params = this.getRequestParams();
        String userId = (String) params.get("userId");
        String roleValue = (String) params.get("roleValue");
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(roleValue)) {
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", userId));
                if (Objects.nonNull(hospitalUser)) {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        Map<String, Object> dataMap = supCountryPurchaseServiceImpl.batchSummary(params);

        JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(dataMap.get("list")));
        JSONArray batchList = JSONArray.parseArray(JSONArray.toJSONString(dataMap.get("batchList")));
        List<List<String>> headList = new ArrayList<>();


        String startTime = MapUtils.getString(params, "startTime");
        String endTime = MapUtils.getString(params, "endTime");

        String title = "江门市国家集采情况";
        if (StringUtil.isNotEmpty(startTime) && StringUtil.isNotEmpty(endTime)) {
            title = startTime + "--" + endTime + title;
        }
        List<String> head = new ArrayList<>();
        head.add(title);
        head.add("单位\\批次");
        headList.add(head);

        List<SupDrugBatch> batches = supDrugBatchServiceImpl.list();
        Map<String, SupDrugBatch> bMap = batches.stream().collect(Collectors.toMap(SupDrugBatch::getCode,d -> d));

        for (int i = 0; i < batchList.size(); i++) {
            String batch = batchList.getString(i);
            String taskStartTime = new SimpleDateFormat("yyyy-MM-dd").format(bMap.get(batch).getTaskStartTime());
            String taskEndTime = new SimpleDateFormat("yyyy-MM-dd").format(bMap.get(batch).getTaskEndTime());
            List<String> batchHead1 = new ArrayList<>();
            batchHead1.add(title);
            batchHead1.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead1.add("任务量（片/支/粒）");
            headList.add(batchHead1);

            List<String> batchHead2 = new ArrayList<>();
            batchHead2.add(title);
            batchHead2.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead2.add("累计采购量（片/支/粒）");
            headList.add(batchHead2);

            List<String> batchHead3 = new ArrayList<>();
            batchHead3.add(title);
            batchHead3.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead3.add("完成率（入库）");
            headList.add(batchHead3);


            List<String> batchHead4 = new ArrayList<>();
            batchHead4.add(title);
            batchHead4.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead4.add("累计金额（入库）");
            headList.add(batchHead4);
        }


        String fileName = URLEncoder.encode("国家集采回款情况数据（入库）", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        List<List<String>> dataList = new ArrayList<>();
        for (int j = 0; j < jsonArray.size(); j++) {
            JSONObject dataObj = jsonArray.getJSONObject(j);
            List<String> data = new ArrayList<>();
            data.add(dataObj.getString("hospitalName"));
            for (int i = 0; i < batchList.size(); i++) {
                String batch = batchList.getString(i);
                data.add(dataObj.getString(batch + "_purchase"));
                data.add(dataObj.getString(batch + "_totalPurchaseCount"));
                data.add(dataObj.getString(batch + "_totalPurchase"));
                data.add(dataObj.getString(batch + "_countryPrice"));
            }
            dataList.add(data);
        }


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .registerWriteHandler(new CustomCellWriteHandler())
                .sheet("国家集采批次统计数据")
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                .head(headList)
                .doWrite(dataList);
    }


    /**
     * 批次汇总导出
     *
     * @param response
     * @param request
     * @throws IOException
     */
    @RequestMapping("/batchSummaryDeliveryExport")
    public void batchSummaryDeliveryExport(HttpServletResponse response, HttpServletRequest request) throws IOException, ParseException {
        Map<String, Object> params = this.getRequestParams();
        String userId = (String) params.get("userId");
        String roleValue = (String) params.get("roleValue");
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(roleValue)) {
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", userId));
                if (Objects.nonNull(hospitalUser)) {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        Map<String, Object> dataMap = supCountryPurchaseServiceImpl.batchSummary(params);

        JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(dataMap.get("list")));
        JSONArray batchList = JSONArray.parseArray(JSONArray.toJSONString(dataMap.get("batchList")));
        List<List<String>> headList = new ArrayList<>();


        String startTime = MapUtils.getString(params, "startTime");
        String endTime = MapUtils.getString(params, "endTime");

        String title = "江门市国家集采情况";
        if (StringUtil.isNotEmpty(startTime) && StringUtil.isNotEmpty(endTime)) {
            title = startTime + "--" + endTime + title;
        }
        List<String> head = new ArrayList<>();
        head.add(title);
        head.add("单位\\批次");
        headList.add(head);

        List<SupDrugBatch> batches = supDrugBatchServiceImpl.list();
        Map<String, SupDrugBatch> bMap = batches.stream().collect(Collectors.toMap(SupDrugBatch::getCode,d -> d));

        for (int i = 0; i < batchList.size(); i++) {
            String batch = batchList.getString(i);
            String taskStartTime = new SimpleDateFormat("yyyy-MM-dd").format(bMap.get(batch).getTaskStartTime());
            String taskEndTime = new SimpleDateFormat("yyyy-MM-dd").format(bMap.get(batch).getTaskEndTime());
            List<String> batchHead1 = new ArrayList<>();
            batchHead1.add(title);
            batchHead1.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead1.add("任务量（片/支/粒）");
            headList.add(batchHead1);

            List<String> batchHead2 = new ArrayList<>();
            batchHead2.add(title);
            batchHead2.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead2.add("累计采购量（片/支/粒）");
            headList.add(batchHead2);

            List<String> batchHead3 = new ArrayList<>();
            batchHead3.add(title);
            batchHead3.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
            batchHead3.add("完成率（配送）");
            headList.add(batchHead3);


//            List<String> batchHead4 = new ArrayList<>();
//            batchHead4.add(title);
//            batchHead4.add(bMap.get(batch).getBatchName()+"("+taskStartTime+"至"+taskEndTime+")");
//            batchHead4.add("累计金额（入库）");
//            headList.add(batchHead4);
        }


        String fileName = URLEncoder.encode("国家集采回款情况数据（配送）", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        List<List<String>> dataList = new ArrayList<>();
        for (int j = 0; j < jsonArray.size(); j++) {
            JSONObject dataObj = jsonArray.getJSONObject(j);
            List<String> data = new ArrayList<>();
            data.add(dataObj.getString("hospitalName"));
            for (int i = 0; i < batchList.size(); i++) {
                String batch = batchList.getString(i);
                data.add(dataObj.getString(batch + "_purchase"));
                data.add(dataObj.getString(batch + "_totalDeliveryPurchaseCount"));
                data.add(dataObj.getString(batch + "_totalDeliveryPurchase"));
//                data.add(dataObj.getString(batch + "_countryPrice"));
            }
            dataList.add(data);
        }


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .registerWriteHandler(new CustomCellWriteHandler())
                .sheet("国家集采批次统计数据")
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                .head(headList)
                .doWrite(dataList);
    }

    @ApiOperation(value = "医院批次完成度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalName", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "batch", value = "批次", required = false, dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始日期", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束日期", required = false, dataType = "String"),
    })
    @PostMapping("/hospitalBatchSummary")
    public Result hospitalBatchSummary() {
        Map<String, Object> params = this.getRequestParams();

        List<Map<String, Object>> data = supCountryPurchaseServiceImpl.hospitalBatchSummary(params);
        return ResultUtil.successToObject(data);
    }

}

