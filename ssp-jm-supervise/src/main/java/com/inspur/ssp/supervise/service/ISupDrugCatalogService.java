package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugCatalog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 药品目录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface ISupDrugCatalogService extends IService<SupDrugCatalog> {


    /**
     * isDeal为true检验目录是否存在 存在则返回,不存在则新增后返回
     * @param supDrugCatalog
     * @param
     */
    Map<String, Object> checkDrugCatalog(SupDrugCatalog supDrugCatalog);
}
