package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto;
import com.inspur.ssp.supervise.bean.dto.purchase.ConMaterialOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.entity.ConMaterialOrderItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.MaterialSourceAmountVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
public interface IConMaterialOrderItemService extends IService<ConMaterialOrderItem> {
    List<Map<String, Object>> getOrderItemList(Map<String,Object> params);

    Map<String, Object> getByPurchaseMaterialId(PurchaseDrugDetailDto purchaseDrugDetailDto);


    List<ConMaterialOrderItemExportExcel> getOrderItemListToExcel(Map<String, Object> params);

    /**
     * 耗材采购和配送的统计
     * @param params
     * @return
     */
    List<MaterialSourceAmountVo> getMaterialSourceAmountByHospital(Map<String, Object> params);

    /**
     * 耗材采购明细列表
     * @param params
     * @return
     */
    List<Map<String, Object>> getMaterialPurchaseList(Map<String, Object> params);

}
