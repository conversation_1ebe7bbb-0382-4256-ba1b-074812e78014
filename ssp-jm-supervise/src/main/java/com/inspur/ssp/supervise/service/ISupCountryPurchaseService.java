package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseExcel;
import com.inspur.ssp.supervise.bean.dto.purchase.SupPurchaseInvoiceExcel;
import com.inspur.ssp.supervise.bean.entity.SupCountryPurchase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
public interface ISupCountryPurchaseService extends IService<SupCountryPurchase> {

    List<Map<String,Object>> selectCountryPurchaseList(Map<String,Object> params);

    List<SupCountryPurchaseExcel> selectListToExcel(Map<String,Object> params);

    List<SupPurchaseInvoiceExcel> selectPurchaseInvoiceToExcel(Map<String,Object> params);

    List<Map<String,Object>> selectListByHospital(Map<String,Object> params);

    void updateCountryPurchase(SupOrderItem supOrderItem);

    List<SupCountryPurchase> getList(Map<String,Object> params);

    Map<String, Object> batchSummary(Map<String, Object> params);

    List<Map<String, Object>> hospitalBatchSummary(Map<String, Object> params);

}
