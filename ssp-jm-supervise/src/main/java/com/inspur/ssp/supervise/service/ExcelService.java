package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.CdBatchLog;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-4-28
 */
public interface ExcelService<T> {

    /**
     * 校验数据
     *
     * @param data
     * @return 返回数据所有错误集合，无错误返回null
     */
    List<String> checkData(T data, Map<String,Object> params);

    /**
     * 保存数据
     *
     * @param list
     * @param batchLog
     * @throws Exception
     */
    void saveData(List<T> list, CdBatchLog batchLog, Map<String,Object> params) throws Exception;

}
