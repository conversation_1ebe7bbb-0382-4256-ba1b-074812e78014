package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupContractInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
public interface ISupContractInfoService extends IService<SupContractInfo> {

    List<Map<String,Object>> getSupContractInfoYbCodeNull();

    /**
     * 根据药品id获取合同
     * @param drugId
     * @return
     */
    SupContractInfo getSupContractInfoByDrugId(String drugId);

    SupContractInfo getByContractDetailId(String contractDetailId);

    /**
     * 根据合同编号和当前时间查询
     * @param contractNumber
     * @return
     */
    SupContractInfo getByContractNumberAndTime(String contractNumber);

    /**
     * 根据合同编号查询
     * @param contractNumber
     * @return
     */
    List<SupContractInfo> getByContractNumber(String contractNumber);
}
