package com.inspur.ssp.supervise.controller;


import com.inspur.ssp.supervise.bean.entity.SupDrugCatalog;
import com.inspur.ssp.supervise.service.ISupDrugAmountTempService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@RestController
@RequestMapping("/supervise/supDrugAmountTemp")
public class SupDrugAmountTempController extends AbstractController {
    @Autowired
    private ISupDrugAmountTempService supDrugAmountTempServiceImpl;

    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;

    @ApiOperation("按月份统计药品采购数量")
    @RequestMapping("/getDrugAmountOnMonth")
    public Result getDrugAmountOnMonth() {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的第一天
        String roleValue = "";
        IUser user = (IUser) this.getCurrentUser();
        roleValue = user.getRoleValue();
        Calendar cal_1=Calendar.getInstance();//获取当前日期
        cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        cal_1.add(Calendar.YEAR, -1);
        String   startDate = format.format(cal_1.getTime());
        Map<String, Object> requestParams = this.getRequestParams();
        requestParams.put("startDate",startDate);
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            requestParams.put("regionId",user.getOrgCode());
        }
        List<Map<String, Object>> drugAmountOnMonth = supDrugAmountTempServiceImpl.getDrugAmountOnMonth(requestParams);
        return ResultUtil.successToList(drugAmountOnMonth);
    }


    @ApiOperation("按季度统计药品采购数量")
    @RequestMapping("/getDrugAmountOnQuarter")
    public Result getDrugAmountOnQuarter() {

        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        //获取前月的第一天
        Calendar cal_1=Calendar.getInstance();//获取当前日期
        cal_1.add(Calendar.YEAR, -1);
        Integer month = cal_1.get(Calendar.MONTH);
        Integer quarter =month/3==0? month/3: month/3+1;
        String startDate=format.format(cal_1.getTime())+quarter;
        Map<String, Object> requestParams = this.getRequestParams();
        requestParams.put("startDate",startDate);
        List<Map<String, Object>> drugAmountOnMonth = supDrugAmountTempServiceImpl.getDrugAmountOnQuarter(requestParams);
        for (Map<String, Object> map : drugAmountOnMonth) {
            String submitYear =String.valueOf(map.get("submitYear"));
            String submitQuarter = String.valueOf(map.get("submitQuarter"));
            map.put("submitTime",submitYear+"-"+submitQuarter);
        }
        return ResultUtil.successToList(drugAmountOnMonth);
    }
}

