package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderItemDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderPayDto;
import com.inspur.ssp.supervise.bean.entity.SupHospital;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import com.inspur.ssp.supervise.bean.entity.SupOrderPay;
import com.inspur.ssp.supervise.bean.entity.SupOrderStock;
import com.inspur.ssp.supervise.service.ISupOrderPayService;
import com.inspur.ssp.supervise.service.ISupOrderService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-05-12
 */
@RestController
@RequestMapping("/supervise/supOrderPay")
public class SupOrderPayController extends AbstractController {
    @Autowired
    private ISupOrderService supOrderServiceImpl;
    @Autowired
    private ISupOrderPayService supOrderPayServiceImpl;


    @ApiOperation(value = "获取所有列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单Id", required = false, dataType = "String"),
    })
    @RequestMapping("/all")
    public Result listAll() {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupOrderPay> queryWrapper = new LambdaQueryWrapper<SupOrderPay>()
                .orderByDesc(SupOrderPay::getPayTime);
        //查询字段
        String orderId = (String) params.get("orderId");
        if (StringUtil.isNotBlank(orderId)){
            queryWrapper.like(SupOrderPay::getOrderId,orderId);
        }
        List<SupOrderPay> list = supOrderPayServiceImpl.list(queryWrapper);
        return ResultUtil.successToObject(list);
    }


    @ApiOperation("保存订单支付凭证")
    @PostMapping("/save")
    public Result save(@RequestBody SupOrderPayDto orderPayDto) {

        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (Objects.isNull(orderPayDto.getPayPrice()) || orderPayDto.getPayPrice().compareTo(BigDecimal.ZERO) == 0) {
            return ResultUtil.error("支付价格为大于0的金额。");
        }
        if (StringUtil.isEmpty(orderPayDto.getDocInfo())) {
            return ResultUtil.error("请上传支付凭证。");
        }
        if(Objects.isNull(orderPayDto.getPayTime())){
            return ResultUtil.error("请选择支付时间。");
        }
        if(StringUtil.isEmpty(orderPayDto.getOrderId())){
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        SupOrder supOrder = supOrderServiceImpl.getById(orderPayDto.getOrderId());
        if(Objects.isNull(supOrder)){
            return ResultUtil.error("订单异常，请联系管理员。");
        }

        SupOrderPay orderPay = BeanUtil.copyProperties(orderPayDto,SupOrderPay.class);
        if(StringUtil.isNotEmpty(orderPay.getId())){
            orderPay.setLastModifitionTime(new Date());
            orderPay.setLastModifitor(user.getId());
            orderPay.setStatus("1");
            supOrderPayServiceImpl.updateById(orderPay);
        }else{
            orderPay.setId(Tools.genId() + "");
            orderPay.setCreationTime(new Date());
            orderPay.setCreator(user.getId());
            orderPay.setLastModifitionTime(new Date());
            orderPay.setLastModifitor(user.getId());
            orderPay.setStatus("1");
            supOrderPayServiceImpl.save(orderPay);

            //更新支付状态，部分支付
            String payStatus = supOrder.getPayStatus();
            if(!payStatus.equals("2")){
                supOrder.setPayStatus("2");
                supOrder.setLastModifitionTime(new Date());
                supOrder.setLastModifitor(user.getId());
            }
            supOrderServiceImpl.updateById(supOrder);
        }
        return ResultUtil.successToObject(orderPayDto);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单Id", required = true, dataType = "String"),
    })
    @RequestMapping("/delete/{orderPayId}")
    public Result deleteById(@ApiParam("医院ID") @PathVariable("orderPayId") String orderPayId) {
        IUser user =  this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        supOrderPayServiceImpl.removeById(orderPayId);
        String orderId=this.getParameter("orderId");
        if(StringUtil.isBlank(orderId)){
            return ResultUtil.error("订单Id不能为空，请联系管理员。");
        }
        SupOrder supOrder = supOrderServiceImpl.getById(orderId);
        if(Objects.isNull(supOrder)){
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        int count = supOrderPayServiceImpl.count(new QueryWrapper<SupOrderPay>().eq("ORDER_ID", orderId));
        //更新支付状态
        if(count==0&&!supOrder.getPayStatus().equals("0")){
            supOrder.setPayStatus("0");
            supOrder.setLastModifitionTime(new Date());
            supOrder.setLastModifitor(user.getId());
        }
        supOrderServiceImpl.updateById(supOrder);
        return ResultUtil.success("删除成功");
    }
}

