package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.WarningConfig;
import com.inspur.ssp.supervise.service.IWarningConfigService;
import com.inspur.ssp.supervise.task.OrderWarningTask;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-01
 */
@RestController
@RequestMapping("/supervise/warningConfig")
public class WarningConfigController extends AbstractController {
    @Autowired
    private IWarningConfigService warningConfigServiceImpl;

@Autowired
private OrderWarningTask orderWarningTaskImpl;

    @ApiOperation(value = "获取预警配置列表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "编码", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<WarningConfig> queryWrapper = new LambdaQueryWrapper<WarningConfig>().
               orderByDesc(WarningConfig::getLastModifiedTime);
        //查询字段
        String code = (String) params.get("code");  //查询编码
        String name = (String) params.get("name");   //查询名称
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(WarningConfig::getCode,code);
        }
        if (StringUtil.isNotBlank(name)){
            queryWrapper.like(WarningConfig::getName, name);
        }
        PageHelper.startPage(page, limit);
        List<WarningConfig> list =warningConfigServiceImpl.list(queryWrapper);
        PageList<WarningConfig> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation("保存预警配置")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "WarningConfig预警配置对象", required = true) WarningConfig warningConfig) {
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
    /*    WarningConfig config =warningConfigServiceImpl.getOne(new QueryWrapper<WarningConfig>().lambda().eq(WarningConfig::getCode, warningConfig.getCode()));
        if (config != null && StringUtil.isNotBlank(config.getId())) {
            return ResultUtil.error("预警配置编码已存在");
        }*/
        WarningConfig warningConfigName =warningConfigServiceImpl.getOne(new QueryWrapper<WarningConfig>().lambda().eq(WarningConfig::getName, warningConfig.getName()));
        if (warningConfigName != null && StringUtil.isNotBlank(warningConfigName.getId())) {
            return ResultUtil.error("预警配置名称已存在");
        }
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            warningConfig.setCreator(user.getId());
        }
        warningConfig.setStatus("1");
        warningConfig.setId(String.valueOf(Tools.genId()));
        warningConfig.setCreationTime(new Date());
        warningConfig.setLastModifiedTime(new Date());
       warningConfigServiceImpl.save(warningConfig);
        return ResultUtil.success("保存成功");

    }
    @ApiOperation(value = "修改预警配置")
    @RequestMapping("/update")
    public Result update(@ApiParam(value = "WarningConfig预警配置对象", required = true) WarningConfig warningConfig) {
   /*     WarningConfig config =warningConfigServiceImpl.getOne(new QueryWrapper<WarningConfig>().lambda().eq(WarningConfig::getCode, warningConfig.getCode()).ne(WarningConfig::getId,warningConfig.getId()));
        if (config != null && StringUtil.isNotBlank(config.getId())) {
            return ResultUtil.error("预警配置编码已存在");
        }*/

        WarningConfig warningConfigName =warningConfigServiceImpl.getOne(new QueryWrapper<WarningConfig>().lambda().eq(WarningConfig::getName, warningConfig.getName()).ne(WarningConfig::getId,warningConfig.getId()));
        if (warningConfigName != null && StringUtil.isNotBlank(warningConfigName.getId())) {
            return ResultUtil.error("预警配置名称已存在");
        }
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            warningConfig.setLastModifier(user.getId());
        }
        warningConfig.setLastModifiedTime(new Date());
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
       warningConfigServiceImpl.updateById(warningConfig);
        return ResultUtil.success("修改成功");
    }

    @ApiOperation(value = "预警配置详细信息")
    @RequestMapping("/info/{WarningConfigId}")
    public Result info(@ApiParam("预警配置id") @PathVariable("WarningConfigId") String warningConfigId) {
        WarningConfig WarningConfig =warningConfigServiceImpl.getById(warningConfigId);
        if (WarningConfig == null) {
            return ResultUtil.error("预警配置ID不存在");
        }
        return ResultUtil.successToObject(WarningConfig);
    }


    @ApiOperation("预警任务")
    @RequestMapping("/warningTask")
    public Result warningTask(){
        try {
            Map<String, Object> params = this.getRequestParams();
            String warningCode = (String)params.get("warningCode");
            String isCountry = (String)params.get("isCountry");
            if(StringUtil.isBlank(warningCode)){
                return ResultUtil.error("预警类型不能为空");
            }
            if(StringUtil.isBlank(isCountry)){
                return ResultUtil.error("是否国集不能为空");
            }
            if(warningCode.equals("deliveryWarning")){
                orderWarningTaskImpl.overDeliveryWarning(params);
            }
            if(warningCode.equals("stockWarning")){
                orderWarningTaskImpl.overStockInWarning(params);
            }
            if(warningCode.equals("payWarning")){
                orderWarningTaskImpl.handlerOrderWarning(params);
            }
            return ResultUtil.success("后台刷新中");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error(e.getMessage());
        }
    }
}

