package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupPayment;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@RestController
@RequestMapping("/supervise/supTransferLog")
public class SupTransferLogController extends AbstractController {
    @ApiOperation(value = "支付单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String num,
                       @RequestParam(required = false) String curNode, @RequestParam(required = false) String isTransfer,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }

        return ResultUtil.successToList(null);
    }
}

