package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupDelivery;
import com.inspur.ssp.supervise.bean.entity.SupStockIn;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.util.Tools;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
public interface ISupStockInService extends IService<SupStockIn> {


     String createStockInCode();

}
