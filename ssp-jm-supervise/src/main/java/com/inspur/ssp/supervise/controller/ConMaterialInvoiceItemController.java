package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.ConMaterialInvoiceItem;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.entity.SupInvoiceItem;
import com.inspur.ssp.supervise.service.IConMaterialInvoiceItemService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@RestController
@RequestMapping("/supervise/conMaterialInvoiceItem")
public class ConMaterialInvoiceItemController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private IConMaterialInvoiceItemService conMaterialInvoiceItemServiceImpl;

    @ApiOperation(value = "获取发票明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
    })
    @RequestMapping("/getInvoiceItem")
    public Result getInvoiceItem(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
        String deliveryItemId = (String) params.get("deliveryItemId");
        String orderItemId = (String) params.get("orderItemId");
        LambdaQueryWrapper<ConMaterialInvoiceItem> queryWrapper = new LambdaQueryWrapper<>();
        if (!"#".equals(deliveryItemId) && StringUtil.isNotBlank(deliveryItemId)) {
            queryWrapper.eq(ConMaterialInvoiceItem::getDeliveryItemId, deliveryItemId);
        }
        if (!"#".equals(orderItemId) && StringUtil.isNotBlank(orderItemId)) {
            queryWrapper.eq(ConMaterialInvoiceItem::getOrderItemId, orderItemId);
        }
        PageHelper.startPage(page, limit);
        List<ConMaterialInvoiceItem> list = conMaterialInvoiceItemServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @ApiOperation(value = "获取发票明细列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "deliveryCode", value = "发票单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
    })
    @RequestMapping("/getConMaterialInvoiceItemList")
    public Result getInvoiceItemListNew(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("invoiceTime[0]");
        String endTime = (String) params.get("invoiceTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            String roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId", hospitalId);

                }
            }
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> invoiceItemList = conMaterialInvoiceItemServiceImpl.getConMaterialInvoiceItemList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(invoiceItemList);
        return ResultUtil.successToList(selectPage);

    }

}

