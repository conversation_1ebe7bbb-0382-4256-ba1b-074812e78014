package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.SupDeductionDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.entity.SupDeduction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import org.jangod.iweb.core.bean.IUser;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ISupDeductionService extends IService<SupDeduction> {


    void save(IUser user, SupDeductionDto supDeductionDto);

}
