package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugBatch;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-15
 */
public interface ISupDrugBatchService extends IService<SupDrugBatch> {
    /**
     * 根据批次编号获取相应的续约批次
     * @param code
     * @return
     */
    List<SupDrugBatch> getRenewCodeList(Integer code);

    /**
     * 根据批次编号查询获取初始批次
     * @param code
     * @return
     */
    SupDrugBatch getCodeBatch(Integer code);

    /**
     * 获取初始以及续约合同
     * @param code
     * @return
     */
    List<SupDrugBatch> getRenewList(Integer code);
}
