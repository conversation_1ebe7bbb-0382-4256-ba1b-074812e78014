package com.inspur.ssp.supervise.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.inspur.ssp.supervise.bean.entity.SupSettlement;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.IUser;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结算业务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ISupSettlementService extends IService<SupSettlement> {

    List<Map<String, Object>> settlementTodo(Map<String, Object> params);

    void save(IUser user, JSONArray list);

    void nextData(IUser user, JSONObject obj);

    void acceptFail(IUser user, JSONObject obj);
}
