package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupReconcileItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
public interface ISupReconcileItemService extends IService<SupReconcileItem> {

    List<Map<String, Object>> getReconcileItemList(HashMap<String, Object> params);
}
