package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.SupOrderStockDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-05-12
 */
@RestController
@RequestMapping("/supervise/supOrderStock")
public class SupOrderStockController extends AbstractController {
    @Autowired
    private ISupOrderStockService supOrderStockServiceImpl;
    @Autowired
    private ISupOrderService supOrderServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;
    @Autowired
    private ISupDrugStockItemService supDrugStockItemServiceImpl;

    @Autowired
    private ISupDrugStockService supDrugStockServiceImpl;

    @ApiOperation("保存订单入库凭证")
    @PostMapping("/save")
    public Result save(@RequestBody SupOrderStockDto orderStockDto) {

        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        BigDecimal itemStock = orderStockDto.getItemStock();
        if (Objects.isNull(itemStock) || itemStock.compareTo(BigDecimal.ZERO) == -1) {
            return ResultUtil.error("入库数量为大于0的数。");
        }
        if (StringUtil.isEmpty(orderStockDto.getDocInfo())) {
            return ResultUtil.error("请上传入库凭证。");
        }
        if (Objects.isNull(orderStockDto.getStockTime())) {
            return ResultUtil.error("请选择入库时间。");
        }
        if (StringUtil.isBlank(orderStockDto.getOrderId()) || StringUtil.isBlank(orderStockDto.getOrderItemId())) {
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        SupOrderItem supOrderItem = supOrderItemServiceImpl.getById(orderStockDto.getOrderItemId());
        if (Objects.isNull(supOrderItem)) {
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        String sourceId = orderStockDto.getSourceId();
        if (StringUtil.isBlank(sourceId)) {
            return ResultUtil.error("来源Id不能为空");
        }
          /* BigDecimal amount = supOrderItem.getAmount();
        if(itemStock.compareTo(amount)==1){
            return ResultUtil.error("入库数量不得超过本次采购数量。");
        }*/
        Result result = supOrderStockServiceImpl.saveOrderStock(orderStockDto, user.getId());
        return result;
    }

    @ApiOperation(value = "获取订单项入库凭证列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "orderItemId", value = "订单项Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
    })
    @RequestMapping("/all")
    public Result listAll(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupOrderStock> queryWrapper = new LambdaQueryWrapper<SupOrderStock>()
                .orderByDesc(SupOrderStock::getStockTime);
        //查询字段
        String orderItemId = (String) params.get("orderItemId");
        if (StringUtil.isNotBlank(orderItemId)) {
            queryWrapper.eq(SupOrderStock::getOrderItemId, orderItemId);
        } else {
            return ResultUtil.error("订单异常，订单项Id不能为空，请联系管理员。");
        }
        String orderId = (String) params.get("orderId");
        if (StringUtil.isBlank(orderId)) {
            return ResultUtil.error("订单异常，订单ID不能为空，请联系管理员。");
        }
        SupOrder supOrder = supOrderServiceImpl.getById(orderId);
        if (Objects.isNull(supOrder)) {
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        String stockStatus = supOrder.getStockStatus();
        PageHelper.startPage(page, limit);
        List<SupOrderStock> list = supOrderStockServiceImpl.list(queryWrapper);
        PageList<SupOrderStock> selectPage = new PageList<>(list);
        return ResultUtil.successToList(stockStatus, selectPage);
    }

    @ApiOperation(value = "删除")
    @RequestMapping("/delete/{orderStockId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sourceId", value = "来源Id", required = true, dataType = "String"),
    })
    public Result deleteById(@ApiParam("入库ID") @PathVariable("orderStockId") String orderStockId) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        String orderId = this.getParameter("orderId");
        if (StringUtil.isBlank(orderId)) {
            return ResultUtil.error("订单Id不能为空，请联系管理员。");
        }
        String sourceId = this.getParameter("sourceId");
        if (StringUtil.isBlank(sourceId)) {
            return ResultUtil.error("采购平台Id不能为空，请联系管理员。");
        }
        SupOrder supOrder = supOrderServiceImpl.getById(orderId);
        if (Objects.isNull(supOrder)) {
            return ResultUtil.error("订单异常，请联系管理员。");
        }
        int count = supOrderStockServiceImpl.count(new QueryWrapper<SupOrderStock>().eq("ORDER_ID", orderId));
        if (count == 0 && !supOrder.getStockStatus().equals("0")) {
            supOrder.setStockStatus("0");
            supOrder.setLastModifitionTime(new Date());
            supOrder.setLastModifitor(user.getId());
        }
        supOrderServiceImpl.updateById(supOrder);

        //库存表维护
        SupDrugStock supDrugStock = supDrugStockServiceImpl.getOne(new QueryWrapper<SupDrugStock>().eq("SOURCE_ID", sourceId));
        SupOrderStock supOrderStock = supOrderStockServiceImpl.getById(orderStockId);

        SupDrugStockItem supDrugStockItem = new SupDrugStockItem();
        BigDecimal stockNum = supDrugStock.getStockNum();
        supDrugStockItem.setId(Tools.genId() + "");
        supDrugStockItem.setStatus("1");
        supDrugStockItem.setWarehousing(supOrderStock.getItemStock().negate());
        supDrugStockItem.setDrugStockId(supDrugStock.getId());
        supDrugStockItem.setOrderStockId(orderStockId);
        supDrugStockItem.setBeforeStockNum(stockNum);
        supDrugStockItem.setAfterStockNum(stockNum.subtract(supOrderStock.getItemStock()));
        supDrugStockItem.setCreationTime(new Date());
        supDrugStockItem.setCreator(user.getId());
        supDrugStockItem.setLastModifitionTime(new Date());
        supDrugStockItem.setLastModifitor(user.getId());
        supDrugStockItemServiceImpl.save(supDrugStockItem);

        supDrugStock.setStockNum(stockNum.subtract(supOrderStock.getItemStock()));
        supDrugStock.setLastModifitionTime(new Date());
        supDrugStock.setLastModifitor(user.getId());
        supDrugStockServiceImpl.updateById(supDrugStock);


        supOrderStockServiceImpl.removeById(orderStockId);
        return ResultUtil.success("删除成功");
    }

}

