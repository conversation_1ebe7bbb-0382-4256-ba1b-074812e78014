package com.inspur.ssp.supervise.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.ConMaterialBatch;
import com.inspur.ssp.supervise.service.IConMaterialBatchService;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024 05 08 10 59
 **/
@RestController
@RequestMapping("/supervise/conMaterialBatch")
public class ConMaterialBatchController extends AbstractController {

    @Autowired
    private IConMaterialBatchService conMaterialBatchServiceImpl;

    @RequestMapping("/getBatchList")
    public Result getBatchList() {
        QueryWrapper<ConMaterialBatch> queryWrapper = new QueryWrapper<ConMaterialBatch>();
        queryWrapper.eq("status","1");
        queryWrapper.orderByDesc("sort_order");
        List<ConMaterialBatch> list = conMaterialBatchServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

}
