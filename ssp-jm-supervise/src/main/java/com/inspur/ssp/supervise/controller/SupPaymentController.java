package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.bean.entity.SupPayment;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@RestController
@RequestMapping("/supervise/supPayment")
public class SupPaymentController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${hospitalAdmin}")
    private String hospitalAdmin;
    @Value("${deliveryAdmin}")
    private String deliveryAdmin;

    @Autowired
    public ISupPaymentService supPaymentServiceImpl;

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private ISupPaymentItemService supPaymentItemServiceImpl;

    @Autowired
    private ISupSettlementCourseService supSettlementCourseServiceImpl;

    @Autowired
    private ISupSettlementItemService supSettlementItemServiceImpl;

    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;

    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;

    @Autowired
    private ISupOrderService supOrderServiceImpl;

    @Autowired
    private ISupTransferLogService supTransferLogServiceImpl;

    @ApiOperation(value = "支付单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String num,
                       @RequestParam(required = false) String curNode, @RequestParam(required = false) String transferStatus,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        QueryWrapper<SupPayment> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtil.isNotEmpty(num), SupPayment::getNum, num)
                .eq(StringUtil.isNotEmpty(curNode), SupPayment::getCurNode, curNode)
                .eq(StringUtil.isNotEmpty(transferStatus), SupPayment::getTransferStatus, transferStatus)
                .between(StringUtil.isNotEmpty(startDate) && StringUtil.isNotEmpty(endDate), SupPayment::getCreationTime, startDate, endDate);
        String roleValue = user.getRoleValue();
        if (!(roleValue.contains(adminRole) || roleValue.contains(medicalRole))) {
            if (roleValue.contains(hospitalAdmin)) {//医院
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    String hospitalId = hospitalUser.getHospitalId();
                    queryWrapper.lambda().eq(SupPayment::getHospitalId, hospitalId);

                }
            }
            if (roleValue.contains(deliveryAdmin)) {
                queryWrapper.lambda().like(SupPayment::getDeliveryName, user.getName());
            }
        }
        queryWrapper.lambda().orderByDesc(SupPayment::getCreationTime);
        PageHelper.startPage(page, limit);
        List<SupPayment> list = supPaymentServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @ApiOperation("保存支付确认单")
    @PostMapping("/save")
    public Result save(@RequestBody JSONArray list) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.error("结算单不能为空。");
        }
        supPaymentServiceImpl.save(user, list);
        return ResultUtil.success();
    }

    @ApiOperation(value = "支付单查看")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String id, String type) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (StringUtil.isEmpty(id)) {
            return ResultUtil.error("结算单id不能为空。");
        }
        SupPayment supPayment = supPaymentServiceImpl.getById(id);

        JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(supPayment));
        obj.put("transferPrice", supPayment.getTotalPrice().subtract(supPayment.getBackPrice()));

        HashMap<String, Object> params = new HashMap<>();
        params.put("paymentId", id);

        String roleValue = user.getRoleValue();
        if (roleValue.contains(hospitalAdmin)) {
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
            if (Objects.isNull(hospitalUser)) {
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            } else {
                String hospitalId = hospitalUser.getHospitalId();
                params.put("hospitalId", hospitalId);

            }
        }
        if (roleValue.contains(deliveryAdmin)) {
            params.put("deliveryName", user.getName());
//            params.put("itemStatus", "1");
        }

        List<Map<String, Object>> items = supPaymentItemServiceImpl.getPaymentItemList(params);

        List<String> reconcileIds = items.stream().map(item -> (String) item.get("reconcileId")).collect(Collectors.toList());
        List<String> settlementIds = items.stream().map(item -> (String) item.get("settlementId")).collect(Collectors.toList());

        List<SupSettlementCourse> courseList = supSettlementCourseServiceImpl.list(new QueryWrapper<SupSettlementCourse>().lambda()
                .eq(SupSettlementCourse::getActive, "0")
                .and(i -> i.eq(SupSettlementCourse::getBizId, id)
                        .or(j -> j.in(SupSettlementCourse::getBizId, reconcileIds))
                        .or(j -> j.in(SupSettlementCourse::getBizId, settlementIds))
                ));
        LinkedHashMap<String, List<SupSettlementCourse>> collect = courseList.stream().collect(
                Collectors.groupingBy((c) -> c.getCurNodeId() + "," + c.getCurNodeName(), LinkedHashMap::new, Collectors.toList()));

        HashMap<String, Object> result = new HashMap<>();
        if (!supPayment.getTransferStatus().equals("0")) {
            List<SupTransferLog> transfers = supTransferLogServiceImpl.list(new QueryWrapper<SupTransferLog>().lambda().eq(SupTransferLog::getPanymentId, supPayment.getId()));
            result.put("transfers", transfers);
        }
        result.put("data", obj);
        result.put("items", items);
        result.put("courses", collect);
        return ResultUtil.successToObject(result);
    }

    @ApiOperation("保存支付记录")
    @PostMapping("/transfer")
    public Result transfer(@RequestBody JSONObject transferData) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        JSONObject data = transferData.getJSONObject("data");
        String bankCard = transferData.getString("bankCard");
        String bankName = transferData.getString("bankName");


        if (StringUtil.isEmpty(bankCard)) {
            return ResultUtil.error("配送单位银行卡号不能为空");
        }
        BigDecimal transferPrice = data.getBigDecimal("transferPrice");
        if (Objects.isNull(transferPrice) || transferPrice.compareTo(BigDecimal.ZERO) == 0) {
            return ResultUtil.error("支付价格为大于0的金额。");
        }

        String paymentId = data.getString("id");
        SupPayment supPayment = this.supPaymentServiceImpl.getById(paymentId);

        JSONArray items = transferData.getJSONArray("items");
        for (int i = 0; i < items.size(); i++) {
            JSONObject itemObj = items.getJSONObject(i);
            String status = itemObj.getString("status");
            if (status.equals("2")) {
                String settlementItemId = itemObj.getString("settlementItemId");
                SupSettlementItem settlementItem = this.supSettlementItemServiceImpl.getById(settlementItemId);

                SupInvoiceItem supInvoiceItem = new SupInvoiceItem();
                supInvoiceItem.setId(settlementItem.getInvoiceItemId());
                supInvoiceItem.setPayTime(new Date());
                supInvoiceItem.setDocInfo("[]");
                supInvoiceItem.setPayPrice(settlementItem.getInvoicePrice());
                supInvoiceItem.setInsurancePayFlag("1");
                supInvoiceItem.setMany(false);
                supInvoiceItem.setLastModifier(user.getId());
                supInvoiceItem.setLastModifiedTime(new Date());
                Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);
                if (result.getState() == 0) {
                    return result;
                }

                SupOrderItem supOrderItem = supOrderItemServiceImpl.getById(settlementItem.getOrderItemId());
                //更改订单明细预警超时未支付为已支付
                supOrderServiceImpl.updateOrderWarning(supOrderItem.getOrderId());
            }
        }

        SupTransferLog supTransferLog = new SupTransferLog();
        supTransferLog.setId(Tools.genId() + "");
        supTransferLog.setPanymentId(supPayment.getId());
        String num = Tools.formatDate(new Date(), "yyyyMMddHHmmss") + "" + (int) (Math.random() * (9999) + 1000);
        supTransferLog.setNum(num);
        supTransferLog.setAmount(transferPrice);
        supTransferLog.setBankName(bankName);
        supTransferLog.setBankCard(bankCard);
        supTransferLog.setStatus("1");
        supTransferLog.setPayTime(new Date());
        supTransferLog.setCreator(user.getId());
        supTransferLog.setCreationTime(new Date());
        supTransferLog.setLastModifier(user.getId());
        supTransferLog.setLastModifiedTime(new Date());
        this.supTransferLogServiceImpl.save(supTransferLog);

        supPayment.setTransferStatus("1");
        this.supPaymentServiceImpl.updateById(supPayment);
        return ResultUtil.success("支付成功");
    }
}

