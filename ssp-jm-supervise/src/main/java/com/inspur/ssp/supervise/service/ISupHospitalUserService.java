package com.inspur.ssp.supervise.service;

import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.PageList;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
public interface ISupHospitalUserService extends IService<SupHospitalUser> {
    PageList<List<PubUser>> queryHospitalUser(Map<String,Object> params);

    List<PubUser> getUsersByHospitalId(String hospitaolId);
}
