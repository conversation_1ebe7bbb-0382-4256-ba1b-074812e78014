package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupDelivery;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import org.jangod.iweb.util.Tools;

/**
 * <p>
 * 配送主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
public interface ISupDeliveryService extends IService<SupDelivery> {

    String createDeliveryCode();

}
