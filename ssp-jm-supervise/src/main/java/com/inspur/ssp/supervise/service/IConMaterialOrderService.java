package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ConMaterialOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
public interface IConMaterialOrderService extends IService<ConMaterialOrder> {
    void updateOrderStatus(String orderId);

    Map<String, Object> getConMaterialSourceAmountByCity(Map<String, Object> params);

    List<Map<String, Object>> getConMaterialOrderPriceData(Map<String, Object> params);

}
