package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.InterfaceFailLog;
import com.inspur.ssp.supervise.bean.entity.InterfaceLog;
import com.inspur.ssp.supervise.service.IInterfaceFailLogService;
import com.inspur.ssp.supervise.service.IInterfaceLogService;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
@RestController
@RequestMapping("/supervise/interfaceFailLog")
public class InterfaceFailLogController extends AbstractController {
@Autowired
private IInterfaceFailLogService interfaceFailLogServiceImpl;
    @PostMapping("/query")
    public Result query(@RequestParam int page, @RequestParam int limit) {
        try {
            if (this.getCurrentUser() == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<InterfaceFailLog> query = new QueryWrapper<>();
            String code = (String) params.get("code");
            String startLogTime = (String) params.get("createTime[0]");
            String endLogTime = (String) params.get("createTime[1]");
            String importFlag = (String) params.get("importFlag");
            String codeType = (String) params.get("codeType");
            if(StringUtil.isNotBlank(code)){
                query.eq("CODE",code);
            }


            if(StringUtil.isNotBlank(codeType)){
                query.eq("CODE_TYPE",codeType);
            }

            if(StringUtil.isNotBlank(importFlag)){
                query.eq("IMPORT_FLAG","2");
            }

            query.eq("IMPORT_FLAG","2");
            if (StringUtil.isNotBlank(startLogTime) && StringUtil.isNotBlank(endLogTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(startLogTime);
                Date endTime = formatter.parse(endLogTime);
                query.between("CREATE_TIME", beginTime, endTime);
            }
            query.orderByDesc("CREATE_TIME");
            PageHelper.startPage(page, limit);
            List<InterfaceFailLog> list = interfaceFailLogServiceImpl.list(query);
            PageList<InterfaceFailLog> selectPage = new PageList<>(list);
            return ResultUtil.successToList("查询成功", selectPage);
        } catch (Exception e) {
            return ResultUtil.errorToList(e.getMessage());
        }
    }
}

