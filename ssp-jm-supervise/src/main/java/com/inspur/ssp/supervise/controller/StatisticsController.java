package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.excel.MaterialSourceAmountExcel;
import com.inspur.ssp.supervise.bean.dto.purchase.SupOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.entity.InterfaceLog;
import com.inspur.ssp.supervise.bean.entity.SupHospital;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import com.inspur.ssp.supervise.bean.vo.MaterialSourceAmountVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderStatisticsVo;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.BusinessUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 统计处理层
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@RestController
@Api(tags = "查询统计")
@RequestMapping("/supervise/statistics")
public class StatisticsController extends AbstractController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Autowired
    private ISupOrderService supOrderServiceImpl;
    @Autowired
    private ISupDrugSourceService supDrugSourceServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;
    @Autowired
    private ISupDrugAmountTempService supDrugAmountTempServiceImpl;

    @Resource
    private IConMaterialOrderItemService conMaterialOrderItemServiceImpl;



    @Autowired
    private BusinessUtil businessUtil;

    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;

    @ApiOperation("统计订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isCountry", value = "是否为国家集中采集订单", required = false, dataType = "String"),
    })
    @RequestMapping("/getOrderStatistics")
    public Result getOrderStatistics() {
        try {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<SupOrder> query = new QueryWrapper<>();

            SupOrderStatisticsVo supOrderStatisticsVo = new SupOrderStatisticsVo();
            //根据时间区间查询
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");
            params.put("startTime", beginOrderTime);
            params.put("endTime", endOrderTime);

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                query.between("SUBMIT_TIME", beginTime, endTime);
            }

            IUser user = (IUser) this.getCurrentUser();
            if (user != null) {

                String roleValue = user.getRoleValue();
                String orgCode = user.getOrgCode();
                if (StringUtil.isNotBlank(orgCode) && roleValue.contains(areaRegionAdmin)){
                   params.put("regioniId",orgCode);
                }
            }
            Map<String, Object> orderCount = supOrderServiceImpl.getOrderCount(params);
            supOrderStatisticsVo.setOrderCount(orderCount);
          /*  List<Map<String, Object>> orderNumBySource = supOrderServiceImpl.getOrderNumBySource(params);
            supOrderStatisticsVo.setOrderNumBySource(orderNumBySource);*/
            //各医院订单情况
            List<Map<String, Object>> orderNumByHospital = supOrderServiceImpl.getOrderNumByHospital(params);
            supOrderStatisticsVo.setOrderBarData(orderNumByHospital);

            return ResultUtil.successToObject("查询成功", supOrderStatisticsVo);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultUtil.error(e.getMessage());
        }
    }

    @ApiOperation("市级数据统计")
    @RequestMapping("/getCityStatistics")
    public Result getCityStatistics() {
        String roleValue = "";
        IUser user = (IUser) this.getCurrentUser();
        roleValue = user.getRoleValue();
        try {
            Map<String, Object> params = this.getRequestParams();


            //根据时间区间查询
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);
            }

            if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
                params.put("regionId",user.getOrgCode());
            }
            List<Map<String, Object>> drugAmountOnMonth = supDrugAmountTempServiceImpl.getDrugAmountOnMonth(params);
            Map<String, Object> cityData = supOrderServiceImpl.getCityData(params);
            List<Map<String, Object>> orderPriceData = supOrderServiceImpl.getOrderPriceData(params);

            Map<String, Object> resObj = new HashMap<>();
            resObj.put("cityData", cityData);
            resObj.put("orderPriceData", orderPriceData);
            resObj.put("orderAmountData", drugAmountOnMonth);

            return ResultUtil.successToObject("查询成功", resObj);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultUtil.error(e.getMessage());
        }
    }

    /**
     * 获取指定日期所在月份开始的时间
     * 时间格式yyyyMMdd
     *
     * @param date 指定日期
     * @return
     */
    public Date getMonthBegin(Date date) {
        SimpleDateFormat aDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间
        return c.getTime();
    }

    /**
     * 获取近一年的日期
     */
    public String getYearMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去一年
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        Date y = c.getTime();
        return format.format(y);

    }

    /**
     * 获取指定日期所在月份结束的时间
     * 时间格式yyyyMMdd
     *
     * @param date 指定日期
     * @return
     */
    public Date getMonthEnd(Date date) {
        SimpleDateFormat aDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间
        return c.getTime();
    }

    @ApiOperation("根据平台分组获取药品数量")
    @PostMapping("/getDrugCountBySource")
    public Result getDrugCountBySource() {
        Map<String, Object> params = this.getRequestParams();
        if (Objects.nonNull(params.get("country")) && ((String) params.get("country")).equals("2")) {//线下的
            IUser currentUser = this.getCurrentUser();
            if (currentUser == null) {
                return ResultUtil.error("用户登录超时，请重新登录");
            } else {
                //医院Id
                String roleValue = currentUser.getRoleValue();

                if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin) ) {
                    SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", currentUser.getId()));
                    if (Objects.isNull(hospitalUser)) {
                        return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                    } else {
                        params.put("hospitalId", hospitalUser.getHospitalId());
                    }
                }
            }
        }

        List<Map<String, Object>> drugCountBySource = supDrugSourceServiceImpl.getDrugCountBySource(params);
        return ResultUtil.successToList("查询成功", drugCountBySource);
    }


    @ApiOperation("根据平台分组获取采购金额")
    @PostMapping("/getPurchaseAmountBySource")
    public Result getPurchaseAmountBySource() {

        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }
        List<Map<String, Object>> purchaseAmount = supOrderItemServiceImpl.getPurchaseAmountBySource(params);
        return ResultUtil.successToList("查询成功", purchaseAmount);
    }

    @ApiOperation("根据医院分组获取采购金额")
    @PostMapping("/getAmountByHospital")
    public Result getAmountByHospital() {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }
        List<Map<String, Object>> purchaseAmount = supOrderItemServiceImpl.getAmountByHospital(params);
        return ResultUtil.successToObject("查询成功", purchaseAmount);
    }


    @ApiOperation("根据医院和平台分组获取采购金额")
    @PostMapping("/getSourceAmountByHospital")
    public Result getSourceAmountByHospital() {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }

        IUser user =  this.getCurrentUser();
        if (StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        List<Map<String, Object>> purchaseAmount = supOrderItemServiceImpl.getSourceAmountByHospital(params);
        return ResultUtil.successToList("查询成功", purchaseAmount);
    }

    @ApiOperation("耗材采购和配送的统计")
    @PostMapping("/getMaterialSourceAmountByHospital")
    public Result getMaterialSourceAmountByHospital() {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        String currentPage = (String) params.get("currentPage");
        String pageSize = (String) params.get("pageSize");


        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }
        IUser user =  this.getCurrentUser();
        String roleValue = user.getRoleValue();
        if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)){
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
            if(Objects.isNull(hospitalUser)){
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            }else{
                params.put("userHospitalId",hospitalUser.getHospitalId());
            }
        }

        if (StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        PageHelper.startPage(Integer.parseInt(currentPage), Integer.parseInt(pageSize));

        List<MaterialSourceAmountVo> purchaseAmount = conMaterialOrderItemServiceImpl.getMaterialSourceAmountByHospital(params);




        PageList<MaterialSourceAmountVo> selectPage = new PageList<>(purchaseAmount);
        return ResultUtil.successToList(selectPage);

    }


    @ApiOperation("导出耗材采购和配送")
    @PostMapping("/exportMaterialSourceAmountByHospital")
    public void exportMaterialSourceAmountByHospital(HttpServletResponse response, HttpServletRequest request) throws IOException {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        String currentPage = (String) params.get("currentPage");
        String pageSize = (String) params.get("pageSize");
        String type = (String) params.get("type");


        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }
        IUser user =  this.getCurrentUser();
        String roleValue = user.getRoleValue();
        if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)){
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
            if(Objects.isNull(hospitalUser)){
                throw new RuntimeException("用户未绑定医院，请联系管理员绑定");
            }else{
                params.put("userHospitalId",hospitalUser.getHospitalId());
            }
        }

        if (StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
//        PageHelper.startPage(Integer.parseInt(currentPage), Integer.parseInt(pageSize));

        List<MaterialSourceAmountVo> purchaseAmount = conMaterialOrderItemServiceImpl.getMaterialSourceAmountByHospital(params);

        //MaterialSourceAmountExcel

        List<MaterialSourceAmountExcel> materialSourceAmountExcels = new ArrayList<MaterialSourceAmountExcel>();

        if(!CollectionUtils.isEmpty(purchaseAmount)){
            for (MaterialSourceAmountVo materialSourceAmountVo : purchaseAmount) {

                MaterialSourceAmountExcel materialSourceAmountExcel = new MaterialSourceAmountExcel();
                //属性复制
                BeanUtils.copyProperties(materialSourceAmountVo, materialSourceAmountExcel);

                materialSourceAmountExcels.add(materialSourceAmountExcel);
            }
        }

        String time = startTime + " - " + endTime;
        String fileName = "";
        if("0".equals(type)){ //采购
            fileName = "耗材采购统计列表";
        }else{
            //配送
            fileName = "耗材配送统计列表";
        }

        response.setHeader("Content-disposition", "attachment;filename=" + fileName + time  + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), MaterialSourceAmountExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(MaterialSourceAmountExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet(fileName + time)
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(materialSourceAmountExcels);
    }


    @ApiOperation("根据平台分组获取采购金额")
    @PostMapping("/getSourceAmountByCity")
    public Result getSourceAmountByCity() {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        String regionId = (String) params.get("regionId");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }

        IUser user =  this.getCurrentUser();
        if (StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }

        if (StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",regionId);
        }
        List<Map<String, Object>> purchaseAmount = supOrderItemServiceImpl.getSourceAmountByCity(params);
        return ResultUtil.successToList("查询成功", purchaseAmount);
    }

    @ApiOperation("药品采购数量统计")
    @PostMapping("/getDrugAmountByMonth")
    public Result getDrugAmountByMonth(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String nowMonth = (String) params.get("nowMonth");
        if (StringUtil.isNotBlank(nowMonth)) {
            String[] split = nowMonth.split("-");
            if (split.length == 2) {
                params.put("year", split[0]);
                params.put("month", split[1]);
                params.put("lastYear", String.valueOf(Integer.parseInt(split[0]) - 1));
                int lastMonth = Integer.parseInt(split[1]) - 1;
                if (lastMonth == 0) {
                    lastMonth = 12;
                    params.put("acrossYear", "1");
                }
                params.put("lastMonth", String.valueOf(lastMonth));
            }
        } else {
            return ResultUtil.errorToObject("年月不能为空");
        }
        PageHelper.startPage(page, limit);

        IUser user = (IUser) this.getCurrentUser();
        if (user != null) {

            String roleValue = user.getRoleValue();
            String orgCode = user.getOrgCode();
            if (StringUtil.isNotBlank(orgCode) && roleValue.contains(areaRegionAdmin)){
                params.put("regionId",orgCode);
            }
        }
        List<Map<String, Object>> amountData = supOrderItemServiceImpl.getDrugAmountByMonth(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(amountData);
        return ResultUtil.successToList("查询成功", selectPage);
    }

    @ApiOperation("药品采购数量统计")
    @PostMapping("/getDrugAmountByQuarter")
    public Result getDrugAmountByQuarter(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String year = (String) params.get("year");
        if (StringUtil.isBlank(year)) {
            return ResultUtil.errorToObject("年份不能为空");
        } else {
            params.put("lastYear", String.valueOf(Integer.parseInt(year) - 1));
        }

        String quarter = (String) params.get("quarter");
        if (StringUtil.isNotBlank(quarter)) {
            int lastQuarter = Integer.parseInt(quarter) - 1;
            if (lastQuarter == 0) {
                lastQuarter = 4;
                params.put("acrossYear", "1");
            }
            params.put("lastQuarter", String.valueOf(lastQuarter));
        } else {
            return ResultUtil.errorToObject("季度不能为空");
        }

        PageHelper.startPage(page, limit);
        List<Map<String, Object>> amountData = supOrderItemServiceImpl.getDrugAmountByQuarter(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(amountData);
        return ResultUtil.successToList("查询成功", selectPage);
    }


    @ApiOperation("药品采购数量统计")
    @PostMapping("/getDrugAmountByYear")
    public Result getDrugAmountByYear(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String year = (String) params.get("year");
        if (StringUtil.isBlank(year)) {
            return ResultUtil.errorToObject("年份不能为空");
        } else {
            params.put("lastYear", String.valueOf(Integer.parseInt(year) - 1));
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> amountData = supOrderItemServiceImpl.getDrugAmountByYear(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(amountData);
        return ResultUtil.successToList("查询成功", selectPage);
    }


}
