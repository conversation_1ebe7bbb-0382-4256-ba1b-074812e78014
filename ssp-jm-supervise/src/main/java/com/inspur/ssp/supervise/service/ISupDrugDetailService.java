package com.inspur.ssp.supervise.service;

import com.alibaba.fastjson.JSONObject;
import com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.PurchaseDrugDetailVo;
import com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;

import java.util.*;

/**
 * <p>
 * 药品清单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface ISupDrugDetailService extends IService<SupDrugDetail> {


    /**
     * 批量修改药品状态
     */
    void  updateDrugStatusBatch(String source);
    void  updateSourceStatusBatch(String source);
    /**
     * 获取药品列表
     *
     * @return
     */
    List<SupDrugDetailVo> selectDrugList(Map<String, Object> params);

    /**
     * 获取药品列表
     *
     * @return
     */
    List<SupDrugDetailVo> selectDrugListWithSource(Map<String, Object> params);


    /**
     * 升级版本
     *
     * @param
     * @return
     */
    Result updateVersion(JSONObject data);


    Map<String, Object> getDrugDetailById(String id);


    Result editDrugDetail(JSONObject  params);

    String createCode();

    List<SupOrderDrugSelectVo> selectSupDrugList(Map<String, Object> params);

    List<SupOrderDrugSelectVo> getSupDrugListBySource(Map<String, Object> params);

    String selectDrugCode(String busiCode);

    PageList<SupDrugDetailVo> getSupDrugDetail(JSONObject param);

    List<SupDrugDetail> selectDrugCodeList();

    /**
     * 根据busiCode获取药品
     * @param busiCode
     * @return
     */
    SupDrugDetail getByBusiCode(String busiCode,Integer source);

    /**
     * 根据药品id获取哪些医院采购过药品以及相应的价格
     * @param purchaseDrugDetailDto
     * @return
     */
    Map<String, Object> getByPurchaseDrugDetailId(PurchaseDrugDetailDto purchaseDrugDetailDto);
}
