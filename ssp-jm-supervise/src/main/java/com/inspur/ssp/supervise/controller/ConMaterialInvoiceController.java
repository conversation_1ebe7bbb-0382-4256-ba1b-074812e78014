package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.inspur.ssp.supervise.bean.entity.ConMaterialInvoice;
import com.inspur.ssp.supervise.bean.entity.SupInvoice;
import com.inspur.ssp.supervise.service.IConMaterialInvoiceItemService;
import com.inspur.ssp.supervise.service.IConMaterialInvoiceService;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 发票表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@RestController
@RequestMapping("/supervise/conMaterialInvoice")
public class ConMaterialInvoiceController extends AbstractController {

    @Autowired
    private IConMaterialInvoiceService conMaterialInvoiceServiceImpl;

    @Autowired
    private IConMaterialInvoiceItemService conMaterialInvoiceItemServiceImpl;

    @ApiOperation(value = "发票单详情")
    @RequestMapping("/show/{invoiceCode}")
    public Result show(@PathVariable("invoiceCode") String invoiceCode) {
        try {
            Map<Object, Object> map = new HashMap<>();
            LambdaQueryWrapper<ConMaterialInvoice> queryWrapper = new LambdaQueryWrapper<ConMaterialInvoice>().eq(ConMaterialInvoice::getNo, invoiceCode);
            ConMaterialInvoice conMaterialInvoice = conMaterialInvoiceServiceImpl.getOne(queryWrapper,false);
            if(Objects.isNull(conMaterialInvoice)){
                return ResultUtil.error("无此发票单，请联系管理员");
            }else{
                Map<String, Object> params = new HashMap<>();
                params.put("code", invoiceCode);
                List<Map<String, Object>> invoiceItemDetail = conMaterialInvoiceItemServiceImpl.getConMaterialInvoiceItemList(params);
                map.put("data",conMaterialInvoice);
                map.put("invoiceItem",invoiceItemDetail);
            }

            return ResultUtil.successToObject(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }


}

