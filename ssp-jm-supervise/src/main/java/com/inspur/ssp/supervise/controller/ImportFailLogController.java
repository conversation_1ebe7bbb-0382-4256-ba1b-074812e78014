package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.ImportFailLog;
import com.inspur.ssp.supervise.service.IImportFailLogService;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 导入失败表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@RestController
@RequestMapping("/supervise/importFailLog")
public class ImportFailLogController extends AbstractController {

    @Autowired
    private IImportFailLogService importFailLogServiceImpl;
    @PostMapping("/query")
    public ResultList query() {

        Map<String, Object> params = this.getRequestParams();
        int page = 1;
        int limit = 10;
        if (params.containsKey("page")) {
            page = Integer.parseInt(params.get("page") + "");
        }
        if (params.containsKey("limit")) {
            limit = Integer.parseInt(params.get("limit") + "");
        }
        QueryWrapper<ImportFailLog> queryWrapper = new QueryWrapper<>();
        String batchId = (String) params.get("batchId");
        if (StringUtil.isNotEmpty(batchId)) {
            queryWrapper.eq("batch_id", batchId);
        }
        String status = (String) params.get("status");
        if (StringUtil.isNotEmpty(status)) {
            queryWrapper.eq("status", status);
        }
        PageHelper.startPage(page, limit);  //进行分页
        List<ImportFailLog> list = importFailLogServiceImpl.list(queryWrapper);
        PageList<ImportFailLog> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }


    @RequestMapping("/updateStatus/{id}")
    public Result handler(@PathVariable("id") String failId, String status) {
        ImportFailLog failLog = importFailLogServiceImpl.getById(failId);
        failLog.setStatus(status);
        importFailLogServiceImpl.updateById(failLog);
        return ResultUtil.success();
    }

    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String id) {
        ImportFailLog importFailLog = importFailLogServiceImpl.getImportFailLogById(id);
        return ResultUtil.successToObject(importFailLog);
    }
}

