package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDeductionItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
public interface ISupDeductionItemService extends IService<SupDeductionItem> {

   List<Map<String,Object>> queryList (Map<String,Object> params);

}
