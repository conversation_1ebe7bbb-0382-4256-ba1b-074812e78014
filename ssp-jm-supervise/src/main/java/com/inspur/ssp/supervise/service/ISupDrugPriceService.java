package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugPrice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
public interface ISupDrugPriceService extends IService<SupDrugPrice> {
    List<SupDrugPriceVo> getDrugPrice(String drugCode);


    List<SupDrugPriceVo> getDrugPriceList();

    Result setDrugPrice(Map<String, Object> params);
}
