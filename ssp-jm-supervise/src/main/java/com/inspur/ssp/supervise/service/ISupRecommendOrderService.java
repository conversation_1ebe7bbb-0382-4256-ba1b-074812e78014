package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.dto.SupRecommendOrderDto;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import com.inspur.ssp.supervise.bean.entity.SupRecommendOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.IUser;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
public interface ISupRecommendOrderService extends IService<SupRecommendOrder> {
    void save(IUser user, SupRecommendOrderDto orderDataDto);
}
