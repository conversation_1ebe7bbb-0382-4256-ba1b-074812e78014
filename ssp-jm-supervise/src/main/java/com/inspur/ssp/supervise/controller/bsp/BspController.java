package com.inspur.ssp.supervise.controller.bsp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.bsp.service.IPubUserService;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@RestController
@RequestMapping("/supervise/bsp")
public class BspController extends AbstractController {

    @Autowired
    private IPubUserService pubUserServiceImpl;

    @RequestMapping("/query")
    public Result query() {
        Map<String, String> params = this.getParameterMap();
        List<PubUser> list = pubUserServiceImpl.list(new QueryWrapper<PubUser>().lambda().eq(StringUtil.isNotEmpty(params.get("userName")), PubUser::getName, params.get("userName")));
        return ResultUtil.successToList(list);
    }
}
