package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupDrugPrice;
import com.inspur.ssp.supervise.bean.vo.SupDictVo;
import com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo;
import com.inspur.ssp.supervise.bean.vo.SupDrugSourcePriceVo;
import com.inspur.ssp.supervise.service.ISupDictItemService;
import com.inspur.ssp.supervise.service.ISupDrugPriceService;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import javax.swing.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
@RestController
@RequestMapping("/supervise/supDrugPrice")
public class SupDrugPriceController extends AbstractController {
    @Autowired
    private ISupDrugPriceService supDrugPriceServiceImpl;
    @Autowired
    private ISupDictItemService supDictItemServiceImpl;

    @ApiOperation(value = "获取采购平台价格")
    @RequestMapping("/getDrugPrice/{drugCode}")
    public Result getDrugPrice(@ApiParam("药品id") @PathVariable("drugCode") String drugCode) {
        try {
            List<Map<String, Object>> sourcePriceData = new ArrayList<>();
            List<SupDictVo> dictItem = supDictItemServiceImpl.getDictItem("SOURCE");
            List<SupDrugPriceVo> drugPrice = supDrugPriceServiceImpl.getDrugPrice(drugCode);
            Integer version = 0;
            String sourceName="";
            for (SupDrugPriceVo supDrugPriceVo : drugPrice) {
                BigDecimal price = supDrugPriceVo.getPrice();
                if (price == null) {
                    supDrugPriceVo.setPrice(new BigDecimal(0));
                }
                supDrugPriceVo.setNewPrice(price);
                //查询每个sourceId版本价格
                String sourceId = supDrugPriceVo.getSourceId();
                List<SupDrugPrice> list = supDrugPriceServiceImpl.list(new QueryWrapper<SupDrugPrice>().eq("SOURCE_ID", sourceId).orderByAsc("VERSION"));
                List<BigDecimal> priceArray = new ArrayList<>();
                for (SupDrugPrice supDrugPrice : list) {
                    if (supDrugPrice.getVersion() > version) {
                        version = supDrugPrice.getVersion();
                    }
                    priceArray.add(supDrugPrice.getPrice());
                }

                Optional<SupDictVo> supDictVoData = dictItem.stream().filter(item -> item.getValue().equals(supDrugPriceVo.getSource())).findFirst();
                if (supDictVoData.isPresent()) {
                    // 存在
                    SupDictVo supDictVo =  supDictVoData.get();
                    sourceName = supDictVo.getLabel();
                }
                Map<String, Object> map = new HashMap<>();
                map.put( "priceArray",priceArray);
                map.put( "sourceName",sourceName);
                sourcePriceData.add(map);
            }
            List<String> xAxisData = new ArrayList<>();
            for (int i=1;i<=version;i++){
                xAxisData.add(" V "+i+".0");
            }

            SupDrugSourcePriceVo supDrugSourcePriceVo = new SupDrugSourcePriceVo();
            supDrugSourcePriceVo.setXAxisData(xAxisData);
            supDrugSourcePriceVo.setDrugPriceVos(drugPrice);
            supDrugSourcePriceVo.setSourcePriceData(sourcePriceData);
            return ResultUtil.successToObject(supDrugSourcePriceVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常"+e.getMessage());
        }
    }


    @ApiOperation(value = "设置采购平台价格")
    @RequestMapping("/setDrugPrice")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "drugCode", value = "药品编码", required = true, dataType = "String"),
    })
    public Result setDrugPrice(@ApiParam(value = "采购平台价格集合对象") @RequestBody JSONObject data) {
        try {
            JSONArray supDrugPriceVos = data.getJSONArray("supDrugPriceVos");
            String drugCode = data.getString("drugCode");
            if (StringUtil.isBlank(drugCode)) {
                return ResultUtil.error("药品编码不能为空");
            }
            //金额验证
            for (int i = 0; i < supDrugPriceVos.size(); i++) {
                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                JSONObject job = supDrugPriceVos.getJSONObject(i);
                String newPrice = job.getString("newPrice");
                String source = job.getString("source");
                String busiCode = job.getString("busiCode");
                if (StringUtil.isBlank(source)) {
                    return ResultUtil.error("采购平台不能为空");
                }
                if (StringUtil.isBlank(busiCode)) {
                    return ResultUtil.error("药品业务编码不能为空");
                }
                if (StringUtil.isBlank(newPrice)) {
                    return ResultUtil.error("新价格不能为空");
                }
                boolean number = this.isNumber(newPrice);
                if (!number) {
                    return ResultUtil.error("新价格需大于0，且小数位最多4位");
                }
            }
            List<SupDrugPriceVo> drugPriceList = JSONObject.parseArray(supDrugPriceVos.toJSONString(), SupDrugPriceVo.class);
            List<String> sourceList = drugPriceList.stream().
                    collect(Collectors.groupingBy(supDrugPriceVo -> supDrugPriceVo.getSource(), Collectors.counting()))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(entry -> entry.getKey())
                    .collect(Collectors.toList());
            if (sourceList.size() > 0) {
                return ResultUtil.error("同一平台只能存在一条记录");
            }
            Map<String, Object> params = new HashMap<String, Object>();
            IUser currentUser = this.getCurrentUser();
            if (currentUser != null) {
                params.put("userId", currentUser.getId());
            }
            params.put("drugCode", drugCode);
            params.put("drugPriceList", drugPriceList);
            Result result = supDrugPriceServiceImpl.setDrugPrice(params);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e);
        }
    }

    //金额验证
    public static boolean isNumber(String str){
        Pattern pattern=Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){1,4})?$"); // 判断小数点后4位的数字的正则表达式
        Matcher match=pattern.matcher(str);
        return match.matches();
    }

}

