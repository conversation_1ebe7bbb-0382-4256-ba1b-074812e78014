package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupOfficial;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.Result;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公文内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-20
 */
public interface ISupOfficialService extends IService<SupOfficial> {

    /**
     * 发送公文
     *
     * @param
     * @param data
     * @return
     * @throws IOException
     */
    Result officialNotice(Map<String, Object> data);

    /**
     * 发送短信
     *
     * @param
     * @param data
     * @return
     * @throws IOException
     */
    Result sendSms(Map<String, Object> data) throws IOException;

    /**
     * 定时任务批量发送短信（避免轰炸）
     *
     * @param
     * @param data
     * @throws IOException
     */
    void sendSmsToJob(Map<String, Object> data) throws Exception;

    List<SupOfficial> getOfficialList(Map<String, Object> params);
}
