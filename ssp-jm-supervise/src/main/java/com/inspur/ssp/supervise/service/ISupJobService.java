package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupJob;
import com.baomidou.mybatisplus.extension.service.IService;
import org.quartz.*;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-08
 */
public interface ISupJobService extends IService<SupJob> {

     void initJob() throws Exception;

     void deleteJob(String id) throws Exception;

     void insertJob(SupJob supJob) throws Exception;


    /**
     * 根据方法名称从定时任务表中获取执行的时间
     * @param taskName
     * @return
     */
    SupJob selectByTaskName(String taskName);
}
