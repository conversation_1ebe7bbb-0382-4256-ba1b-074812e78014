package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.CdBatchLog;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.IUser;

/**
 * <p>
 * 批次记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface ICdBatchLogService extends IService<CdBatchLog> {
    /**
     * 批次表记录
     * @param docId 文件路径
     * @param name  文件名称
     * @param type  业务类型
     * @param user  用户
     * @param operationType 操作类型  add/edit
     * @return
     * @throws Exception
     */
     CdBatchLog save(String docId,String name,String type,IUser user,String operationType)throws Exception;
}
