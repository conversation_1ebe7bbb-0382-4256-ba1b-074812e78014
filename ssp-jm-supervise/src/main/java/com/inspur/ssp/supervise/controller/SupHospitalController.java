package com.inspur.ssp.supervise.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupHospital;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo;
import com.inspur.ssp.supervise.service.ISupHospitalService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.jangod.iweb.core.action.AbstractController;

import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@RestController
@RequestMapping("/supervise/supHospital")
public class SupHospitalController extends AbstractController {

    @Autowired
    private ISupHospitalService supHospitalServiceImpl;
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;

    @ApiOperation(value = "获取医院列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "医院编码", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupHospital> queryWrapper = new LambdaQueryWrapper<SupHospital>()
                .orderByAsc(SupHospital::getSortOrder).orderByDesc(SupHospital::getLastModifitionTime);
        //查询字段
        String code = (String) params.get("code");  //查询编码
        String name = (String) params.get("name");   //查询名称
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupHospital::getCode,code);
        }
        if (StringUtil.isNotBlank(name)){
            queryWrapper.like(SupHospital::getName, name);
        }
        PageHelper.startPage(page, limit);
        List<SupHospital> list = supHospitalServiceImpl.list(queryWrapper);
        PageList<SupHospital> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }
    @ApiOperation(value = "获取医院列表/根据用户权限")
    @RequestMapping("/getHospitalList")
    public Result getHospitalList() {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        QueryWrapper<SupHospital> queryWrapper = new QueryWrapper<>();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            String roleValue = user.getRoleValue();
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    queryWrapper.eq("id",hospitalId);
                }
            }
        }
        String hospitalName=(String)params.get("hospitalName");
        if(StringUtil.isNotBlank((String)params.get("hospitalName"))){
            queryWrapper.like("NAME",hospitalName);
        }
        queryWrapper.orderByAsc("SORT_ORDER");
        List<Map<String, Object>> maps = supHospitalServiceImpl.listMaps(queryWrapper);
        for (Map<String, Object> data : maps) {
            data.put("hasChildren", true);
            data.put("hospitalName",data.get("name"));
        }
        return ResultUtil.successToList(maps);
    }

    @ApiOperation(value = "获取医院所有列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "医院名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "医院编码", required = false, dataType = "String"),
    })
    @RequestMapping("/all")
    public Result listAll() {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupHospital> queryWrapper = new LambdaQueryWrapper<SupHospital>();
        //查询字段
        String code = (String) params.get("code");  //查询编码
        String name = (String) params.get("name");   //查询名称
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupHospital::getCode,code);
        }
        if (StringUtil.isNotBlank(name)){
            queryWrapper.like(SupHospital::getName, name);
        }
        IUser user = (IUser) this.getCurrentUser();
        if (user != null) {

            String roleValue = user.getRoleValue();
            String orgCode = user.getOrgCode();
            if (StringUtil.isNotBlank(orgCode) && roleValue.contains(areaRegionAdmin)){
                queryWrapper.like(SupHospital::getRegionId, orgCode);
            }
        }
        queryWrapper.orderByAsc(SupHospital::getSortOrder);
        List<SupHospital> list = supHospitalServiceImpl.list(queryWrapper);
        return ResultUtil.successToObject(list);
    }

    @ApiOperation("保存医院")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "SupHospital医院对象", required = true) SupHospital supHospital) {
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        IUser currentUser = this.getCurrentUser();
        SupHospital hospital = supHospitalServiceImpl.getOne(new QueryWrapper<SupHospital>().lambda().eq(SupHospital::getCode, supHospital.getCode()));
        if (hospital != null && StringUtil.isNotBlank(hospital.getId())) {
            return ResultUtil.error("医院编码已存在");
        }
        SupHospital hospitalName = supHospitalServiceImpl.getOne(new QueryWrapper<SupHospital>().lambda().eq(SupHospital::getName, supHospital.getName()));
        if (hospitalName != null && StringUtil.isNotBlank(hospitalName.getId())) {
            return ResultUtil.error("医院名称已存在");
        }

        supHospital.setStatus("1");
        supHospital.setId(String.valueOf(Tools.genId()));
        supHospital.setCreationTime(new Date());
        supHospital.setLastModifitionTime(new Date());
        supHospital.setCreator(currentUser.getId());
        supHospital.setLastModifitor(currentUser.getId());
        supHospitalServiceImpl.save(supHospital);
        return ResultUtil.success("保存成功");

    }
    @ApiOperation(value = "修改医院")
    @RequestMapping("/update")
    public Result update(@ApiParam(value = "SupHospital医院对象", required = true) SupHospital supHospital) {
        SupHospital hospital = supHospitalServiceImpl.getOne(new QueryWrapper<SupHospital>().lambda().eq(SupHospital::getCode, supHospital.getCode()).ne(SupHospital::getId,supHospital.getId()));
        if (hospital != null && StringUtil.isNotBlank(hospital.getId())) {
            return ResultUtil.error("医院编码已存在");
        }

        SupHospital hospitalName = supHospitalServiceImpl.getOne(new QueryWrapper<SupHospital>().lambda().eq(SupHospital::getName, supHospital.getName()).ne(SupHospital::getId,supHospital.getId()));
        if (hospitalName != null && StringUtil.isNotBlank(hospitalName.getId())) {
            return ResultUtil.error("医院名称已存在");
        }

        supHospital.setLastModifitionTime(new Date());
        IUser currentUser = this.getCurrentUser();
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        supHospital.setLastModifitor(currentUser.getId());
        supHospitalServiceImpl.updateById(supHospital);
        return ResultUtil.success("修改成功");
    }

    @ApiOperation(value = "医院详细信息")
    @RequestMapping("/info/{supHospitalId}")
    public Result info(@ApiParam("医院id") @PathVariable("supHospitalId") String supHospitalId) {
        SupHospital supHospital = supHospitalServiceImpl.getById(supHospitalId);
        if (supHospital == null) {
            return ResultUtil.error("医院ID不存在");
        }
        return ResultUtil.successToObject(supHospital);
    }

    @ApiOperation(value = "删除医院")
    @RequestMapping("/delete/{supHospitalId}")
    public Result deleteById(@ApiParam("医院ID") @PathVariable("supHospitalId") String supHospitalId) {
        supHospitalServiceImpl.removeById(supHospitalId);
        return ResultUtil.success("删除成功");
    }
    @ApiOperation(value = "更新医院状态")
    @RequestMapping("/updateStatus")
    public  Result updateStatus(@ApiParam(value = "supHospital医院对象",required = true) SupHospital supHospital){
        UpdateWrapper<SupHospital> updateWrapper = new UpdateWrapper<SupHospital>();
        updateWrapper.eq("ID", supHospital.getId());
        boolean update = supHospitalServiceImpl.update(supHospital, updateWrapper);
        if(update){
            return ResultUtil.success("操作成功");
        }else{
            return ResultUtil.error("操作失败");
        }
    }

}
