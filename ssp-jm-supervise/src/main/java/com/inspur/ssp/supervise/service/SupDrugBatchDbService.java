package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.CdBatchLog;
import com.inspur.ssp.supervise.bean.entity.SupDrugBatchDb;
import org.jangod.iweb.core.bean.IUser;

/**
 * <p>
 * 批次记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface SupDrugBatchDbService extends IService<SupDrugBatchDb> {
    void saveDetailSupBatch (SupDrugBatchDb supDrugBatchDb);

}
