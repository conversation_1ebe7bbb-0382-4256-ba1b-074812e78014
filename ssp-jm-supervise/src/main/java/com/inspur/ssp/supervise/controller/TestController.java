package com.inspur.ssp.supervise.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gdsyj.webservice.cbo.RespBidKindGroupData;
import com.gdsyj.webservice.common.CommandidEnum;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.service.impl.*;
import com.inspur.ssp.supervise.task.*;
import com.inspur.ssp.supervise.utils.ebus.HttpClientUtil;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.core.bean.ResultObject;
import org.jangod.iweb.db.util.S;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/api/test")
public class TestController extends AbstractController {

    @Autowired
    private SupDrugBatchDbServiceImpl supDrugBatchDbService;

    @Autowired
    private ProcurecatalogServiceImpl procurecatalogServiceImpl;

    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;
    @Autowired
    private ISupDrugSourceService supDrugSourceServiceImpl;
    @Autowired
    @Qualifier("supDrugPriceServiceImpl")
    private ISupDrugPriceService iSupDrugPriceService;
    @Autowired
    private SupDrugCatalogServiceImpl iSupDrugCatalogServiceImpl;
    @Autowired
    private SupDrugBatchServiceImpl iSupDrugBatchService;

    @Autowired
    private GZInterfaceTask gzInterfaceTask;

    @Autowired
    private GDMaterialInterfaceTask gdMaterialInterfaceTask;

    @Autowired
    private ShenZhenInterfaceTask shenZhenInterfaceTask;

    @Autowired
    private GDInterfaceTask gdInterfaceTask;

    @Autowired
    private InterfaceLogServiceImpl iInterfaceLogService;

    @Autowired
    private CountryPurchaseTask countryPurchaseTask;

    @Autowired
    private DrugTempTask drugTempTask;

    @Resource
    private ConMaterialDeliveryCompanyService conMaterialDeliveryCompanyService;

    @RequestMapping("/initDrugAmountTemp")
    public ResultObject initDrugAmountTemp(HttpServletRequest request){
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        drugTempTask.initDrugAmountTempByTime(startTime,endTime);
        return ResultUtil.successToObject("cg!");
    }



    @RequestMapping("/query")
    public Result query() {
        Map<String, String> params = this.getParameterMap();

        List<Procurecatalog> list = (List<Procurecatalog>) procurecatalogServiceImpl.getList(1,1000,new HashMap());
        return ResultUtil.successToList(list);
    }

    @RequestMapping("/updatePurchaseSchedule")
    public ResultObject updatePurchaseSchedule(@RequestParam String hospatialId) {
        //countryPurchaseTask.updatePurchaseSchedule1("");
        return null;
    }

    @RequestMapping("/szTask")
    public ResultObject szTask(HttpServletRequest request) throws ParseException {

        String type = request.getParameter("type");
        type = StringUtils.isBlank(type)? "0" : type;

        Date startTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("startTime"))) {
            startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("startTime"));
        }

        Date endTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("endTime"))) {
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("endTime"));
        }

        if (type.equals("0")) {
            shenZhenInterfaceTask.addInterfaceLog(shenZhenInterfaceTask.drugInterface,startTime,endTime);
        } else if (type.equals("1")) {
            shenZhenInterfaceTask.insertDrugData();
        } else if (type.equals("2")) {
            shenZhenInterfaceTask.addInterfaceLog(shenZhenInterfaceTask.hospitalOrderInterface,startTime,endTime);
        } else if (type.equals("3")) {
            shenZhenInterfaceTask.insertOrderData();
        } else if (type.equals("4")) {
            shenZhenInterfaceTask.addInterfaceLog(shenZhenInterfaceTask.deliveryInterface,startTime,endTime);
        } else if (type.equals("5")) {
            shenZhenInterfaceTask.insertDeliveryData();
        } else if (type.equals("6")) {
            shenZhenInterfaceTask.addInterfaceLog(shenZhenInterfaceTask.invoiceInterface,startTime,endTime);
        } else if (type.equals("7")) {
            shenZhenInterfaceTask.insertInvoiceData();
        } else if (type.equals("8")) {
            shenZhenInterfaceTask.addInterfaceLog(shenZhenInterfaceTask.stockInInterface,startTime,endTime);
        }  else if (type.equals("9")) {
            shenZhenInterfaceTask.insertStockInData();
        }
        return ResultUtil.successToObject("同步成功!");
    }

    @RequestMapping("/gzTask")
    public ResultObject gzTask(HttpServletRequest request) throws ParseException {

        String type = request.getParameter("type");
        type = StringUtils.isBlank(type)? "0" : type;

        Date startTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("startTime"))) {
            startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("startTime"));
        }

        Date endTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("endTime"))) {
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("endTime"));
        }

        if (type.equals("0")) {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.drug,startTime,endTime);
        } else if (type.equals("1")) {
            gzInterfaceTask.insertDrugData();
        } else if (type.equals("2")) {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.hospitalOrder,startTime,endTime);
        } else if (type.equals("3")) {
            gzInterfaceTask.insertOrderData();
        } else if (type.equals("4")) {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.hospitalOrderItem,startTime,endTime);
        } else if (type.equals("5")) {
            gzInterfaceTask.insertOrderItemData();
        } else if (type.equals("6")) {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.delivery,startTime,endTime);
        } else if (type.equals("7")) {
            gzInterfaceTask.insertDeliveryAndInvoiceData();
        } else if (type.equals("8")) {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.stockIn,startTime,endTime);
        }  else if (type.equals("9")) {
            gzInterfaceTask.insertStockInData();
        } else {
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.drug,startTime,endTime);
            gzInterfaceTask.insertDrugData();
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.hospitalOrder,startTime,endTime);
            gzInterfaceTask.insertOrderData();
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.hospitalOrderItem,startTime,endTime);
            gzInterfaceTask.insertOrderItemData();
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.hospitalOrderItem,startTime,endTime);
            gzInterfaceTask.insertOrderItemData();
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.delivery,startTime,endTime);
            gzInterfaceTask.insertDeliveryAndInvoiceData();
            gzInterfaceTask.addInterfaceLog(gzInterfaceTask.stockIn,startTime,endTime);
            gzInterfaceTask.insertStockInData();

        }
        return ResultUtil.successToObject("同步成功!");
    }

    @RequestMapping("/syncGdData")
    public ResultObject syncGdData (HttpServletRequest request) throws ParseException {
        String type = request.getParameter("type");
        type = StringUtils.isBlank(type)? "0" : type;

        String syncTime = request.getParameter("syncTime");
        if (StringUtils.isEmpty(syncTime)) {
            syncTime = "2020-01-01 00:00:00";
        }

        String syncEndTime = request.getParameter("syncEndTime");
        if (StringUtils.isEmpty(syncEndTime)) {
            syncEndTime = "2023-01-01 00:00:00";
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date oldDate = format.parse(syncTime);
        Date date = format.parse(syncEndTime);
//        Date endDate = format.parse(syncEndTime);
        while (oldDate.getTime() < date.getTime()) {
            Date nextData = getNextDate(oldDate);
            if (type.equals("0")) {
                gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40005.getCode(),oldDate,nextData,"1","200");
                gdInterfaceTask.insertData(CommandidEnum.COMMANID_40005.getCode(),null,null);
            } else if (type.equals("1")) {
                gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40008.getCode(),oldDate,nextData,"1","200");
                gdInterfaceTask.insertData(CommandidEnum.COMMANID_40008.getCode(),null,null);
            } else if (type.equals("2")) {
                gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40009.getCode(),oldDate,nextData,"1","200");
//                gdInterfaceTask.insertData(CommandidEnum.COMMANID_40009.getCode(),null,null);
            } else if (type.equals("3")) {
                gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_400010.getCode(),oldDate,nextData,"1","200");
                gdInterfaceTask.insertData(CommandidEnum.COMMANID_400010.getCode(),null,null);
            }
            oldDate = nextData;
        }

        return ResultUtil.successToObject("同步成功!");
    }

    private Date getNextDate(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE,1);
        return calendar.getTime();
    }

    @RequestMapping("/gdTask")
    public ResultObject gdTask(HttpServletRequest request) throws ParseException {

        String type = request.getParameter("type");
        type = StringUtils.isBlank(type)? "0" : type;

        Date startTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("startTime"))) {
            startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("startTime"));
        }

        Date endTime = null;
        if (StringUtils.isNotEmpty(request.getParameter("endTime"))) {
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(request.getParameter("endTime"));
        }


        String startpage = request.getParameter("startPage");
        String endpage = request.getParameter("endPage");

        String pageStr = request.getParameter("page");
        String limitStr = request.getParameter("limit");

        Integer page = 1;
        if (StringUtils.isNotEmpty(pageStr)) {
            page = Integer.parseInt(pageStr);
        }

        Integer limit = 10;
        if (StringUtils.isNotEmpty(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }


        if (type.equals("0")) {
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40005.getCode(),startTime,endTime,startpage,endpage);
        } else if (type.equals("1")) {
            gdInterfaceTask.insertData(CommandidEnum.COMMANID_40005.getCode(),page,limit);
        } else if (type.equals("2")) {
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40008.getCode(),startTime,endTime,startpage,endpage);
        } else if (type.equals("3")) {
            gdInterfaceTask.insertData(CommandidEnum.COMMANID_40008.getCode(),page,limit);
        } else if (type.equals("4")) {
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40009.getCode(),startTime,endTime,startpage,endpage);
        } else if (type.equals("5")) {
            gdInterfaceTask.insertData(CommandidEnum.COMMANID_40009.getCode(),page,limit);
        } else if (type.equals("6")) {
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_400010.getCode(),startTime,endTime,startpage,endpage);
        }  else if (type.equals("7")) {
            gdInterfaceTask.insertData(CommandidEnum.COMMANID_400010.getCode(),page,limit);
        } else {
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40005.getCode(),startTime,endTime,startpage,endpage);
            gdInterfaceTask.insertDrugData();
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40008.getCode(),startTime,endTime,startpage,endpage);
            gdInterfaceTask.insertOrderData();
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40009.getCode(),startTime,endTime,startpage,endpage);
            gdInterfaceTask.insertDeliveryAndStockInData();
            gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_400010.getCode(),startTime,endTime,startpage,endpage);
            gdInterfaceTask.insertInvoiceData();
        }
        return ResultUtil.successToObject("同步成功!");
    }

    //初始化国标初始化目录

    @RequestMapping("/initCountryMenu")
    public ResultObject initCountryMenu(@RequestBody JSONObject param) {
        HttpClientUtil util = new HttpClientUtil("A0001","A0001","eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJyb2xlSWRcIjpcIjEsMlwiLFwicm9sZUNvZGVcIjpcIlNVUEVSX0FETUlOLERBVEFfQURNSU5cIixcInVzZXJOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJJZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwicGxhdGZvcm1hZG1pblwiLFwiZGVwdENvZGVcIjpcIjEwMFwifSIsIngtYWVwLXVpZCI6IntcInJvbGVJZFwiOlwiMSwyXCIsXCJyb2xlQ29kZVwiOlwiU1VQRVJfQURNSU4sREFUQV9BRE1JTlwiLFwidXNlck5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwidXNlcklkXCI6XCIxXCIsXCJhY2NvdW50XCI6XCJwbGF0Zm9ybWFkbWluXCIsXCJkZXB0Q29kZVwiOlwiMTAwXCJ9IiwiZXhwIjoxNjY3NTg5ODgzLCJpYXQiOjE2Njc1NDY2ODMsImp0aSI6IjNjMjBlMWRjOTY4YjQ0YmFiMjQ2OGUwZjdjYjYwODI5In0.P0ZljaCEpJRHHCB7DCKLnCoGqzJSeL46EZMYqZX6ON0");
        JSONObject interparam = new JSONObject();
        int page = 0;
        int limit = 10;
        int total = 0;

        while (page <= total) {
            page += 1;
            interparam.put("page",page);
            interparam.put("limit",limit);
            interparam.put("country","1");
            String str=util.getResult("http://157.122.102.146:9000/supapi/supervise/supDrugDetail/list",interparam,true,"form");
            JSONObject result = JSONObject.parseObject(str);
            if (result.getString("state").equals("1")) {
                JSONArray list = result.getJSONArray("rows");
                for (int i = 0;i < list.size();i++) {
                    JSONObject object = list.getJSONObject(i);
                    JSONArray priceArray = object.getJSONArray("priceArray");
                    SupDrugDetail supDrugDetail = JSONObject.toJavaObject(object, SupDrugDetail.class);
                    if (supDrugDetailServiceImpl.getById(supDrugDetail.getId()) == null) {
                        supDrugDetailServiceImpl.save(supDrugDetail);
//                    for (int j = 0;j < priceArray.size();j++) {
//                        JSONObject price = priceArray.getJSONObject(j);
//                        SupDrugPrice drugPrice = JSONObject.toJavaObject(price,SupDrugPrice.class);
//                    }
                    }
                    SupDrugSource supDrugSource = new SupDrugSource();
                    String sourceId = Tools.genId()+"";
                    supDrugSource.setId(sourceId);
                    supDrugSource.setSource(object.getString("source"));
                    supDrugSource.setDrugCode(supDrugDetail.getCode());
                    if (priceArray.size() > 0) {
                        supDrugSource.setBusiCode(priceArray.getJSONObject(0).getString("busiCode"));
                        supDrugSource.setCreationTime(new Date());
                        supDrugSource.setStatus("1");
                        supDrugSourceServiceImpl.save(supDrugSource);

                        for (int j = 0;j < priceArray.size();j++) {
                            JSONObject price = priceArray.getJSONObject(j);
                            SupDrugPrice supDrugPrice = new SupDrugPrice();
                            supDrugPrice.setId(Tools.genId()+"");
                            supDrugPrice.setSourceId(sourceId);
                            supDrugPrice.setPrice(price.getBigDecimal("price"));
                            supDrugPrice.setVersion(1);
                            supDrugPrice.setStatus("1");
                            supDrugPrice.setCreationTime(new Date());
                            iSupDrugPriceService.save(supDrugPrice);
                        }
                    }

                    if (iSupDrugCatalogServiceImpl.getById(object.getString("catalogId")) == null) {
                        SupDrugCatalog supDrugCatalog = new SupDrugCatalog();
                        supDrugCatalog.setId(object.getString("catalogId"));
                        supDrugCatalog.setName(object.getString("catalogName"));
                        supDrugCatalog.setCategory(object.getString("category"));
                        supDrugCatalog.setCreationTime(new Date());
                        supDrugCatalog.setStatus("1");
                        iSupDrugCatalogServiceImpl.save(supDrugCatalog);
                    }

                }

                total = result.getInteger("total");
            }
        }

        return ResultUtil.successToObject("");
    }

    @RequestMapping("/getDrugBatch")
    public ResultObject getDrugBatch (@RequestBody JSONObject param) throws ParseException {
//        String url = "http://157.122.102.146:9000/supapi/supervise/supDrugBatch/list";
//        HttpClientUtil util = new HttpClientUtil("A0001","A0001","eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJyb2xlSWRcIjpcIjEsMlwiLFwicm9sZUNvZGVcIjpcIlNVUEVSX0FETUlOLERBVEFfQURNSU5cIixcInVzZXJOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJJZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwicGxhdGZvcm1hZG1pblwiLFwiZGVwdENvZGVcIjpcIjEwMFwifSIsIngtYWVwLXVpZCI6IntcInJvbGVJZFwiOlwiMSwyXCIsXCJyb2xlQ29kZVwiOlwiU1VQRVJfQURNSU4sREFUQV9BRE1JTlwiLFwidXNlck5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwidXNlcklkXCI6XCIxXCIsXCJhY2NvdW50XCI6XCJwbGF0Zm9ybWFkbWluXCIsXCJkZXB0Q29kZVwiOlwiMTAwXCJ9IiwiZXhwIjoxNjY3NTg5ODgzLCJpYXQiOjE2Njc1NDY2ODMsImp0aSI6IjNjMjBlMWRjOTY4YjQ0YmFiMjQ2OGUwZjdjYjYwODI5In0.P0ZljaCEpJRHHCB7DCKLnCoGqzJSeL46EZMYqZX6ON0");
//        JSONObject interparam = new JSONObject();
        int page = 0;
//        int limit = 10;
        int total = 100;

        while (page <= total) {
            page += 1;
//            interparam.put("page", page);
//            interparam.put("limit", limit);
//            String str=util.getResult(url,interparam,true,"form");
//            JSONObject result = JSONObject.parseObject(str);
//            if (result.getString("state").equals("1")) {
//                JSONArray list = result.getJSONArray("rows");
//                for (int i = 0;i < list.size();i++) {
//                    JSONObject object = list.getJSONObject(i);
//                    SupDrugBatch supDrugBatch = JSONObject.toJavaObject(object,SupDrugBatch.class);
                    SupDrugBatch supDrugBatch = new SupDrugBatch();
                    supDrugBatch.setId(page+"");
                    supDrugBatch.setBatchName("第"+page+"批国家集采");
                    supDrugBatch.setGdCode(page+"");
                    supDrugBatch.setCode(page+"");
                    supDrugBatch.setStatus("1");
                    supDrugBatch.setTaskStartTime(new SimpleDateFormat("yyyy-MM-dd").parse("2018-01-01"));
                    supDrugBatch.setTaskEndTime(new SimpleDateFormat("yyyy-MM-dd").parse("2024-01-01"));
                    iSupDrugBatchService.saveOrUpdate(supDrugBatch);
//                }
//            }
        }
        return ResultUtil.successToObject("同步成功!");
    }

//    @RequestMapping("/gdMaterial")
//    public ResultObject gdMaterial(HttpServletRequest request){
//        String type = request.getParameter("type");
//        type = StringUtils.isBlank(type)? "0" : type;
//        if (type.equals("0")) {
//            gdMaterialInterfaceTask.addComMaterialLog();
//        } else if (type.equals("1")) {
//            gdMaterialInterfaceTask.insertComMaterialData();
//        } else if (type.equals("2")) {
//            gdMaterialInterfaceTask.addOrderLog();
//        } else if (type.equals("3")) {
//            gdMaterialInterfaceTask.insertOrderData();
//        } else if (type.equals("4")) {
//            gdMaterialInterfaceTask.addDeliveryAndStockInLog();
//        } else if (type.equals("5")) {
//            gdMaterialInterfaceTask.insertDeliveryAndStockInData();
//        } else if (type.equals("5")) {
//            gdMaterialInterfaceTask.addInvoiceLog();
//        } else if (type.equals("7")) {
//            gdMaterialInterfaceTask.insertInvoiceData();
//        }
//        return ResultUtil.successToObject("同步成功!");
//    }

    @RequestMapping("/getDrugByProdId")
    public ResultObject getDrugByProdId(@RequestBody JSONObject param) throws Exception {
//        SupDrugBatchDb supDrugBatchDb = JSONObject.toJavaObject(param,SupDrugBatchDb.class);
//            LambdaQueryWrapper<SupDrugBatchDb> queryWrapper = new QueryWrapper<SupDrugBatchDb>().lambda().eq(SupDrugBatchDb::getBatchCode,supDrugBatchDb.getBatchCode()).eq(SupDrugBatchDb::getDetailId,supDrugBatchDb.getDetailId());
//            SupDrugBatchDb supdrug = supDrugBatchDbService.getOne(queryWrapper);
//            if (supdrug == null) {
//                supDrugBatchDb.setId(Tools.genId()+"");
//                supDrugBatchDbService.save(supDrugBatchDb);
//            }
//            return null;
        String a = "0";
        String type = param.getString("type");
        if (StringUtils.isEmpty(type)) {
            type = "3";
        }
        long startTime = System.currentTimeMillis();
        while (a.equals("0")) {
            JSONObject interparam = new JSONObject();
            HttpClientUtil util = new HttpClientUtil("A0001","A0001","eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJyb2xlSWRcIjpcIjFcIixcInJvbGVDb2RlXCI6XCJTVVBFUl9BRE1JTlwiLFwidXNlck5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwidXNlcklkXCI6XCIxXCIsXCJhY2NvdW50XCI6XCJwbGF0Zm9ybWFkbWluXCIsXCJkZXB0Q29kZVwiOlwiMTAwXCJ9IiwieC1hZXAtdWlkIjoie1wicm9sZUlkXCI6XCIxXCIsXCJyb2xlQ29kZVwiOlwiU1VQRVJfQURNSU5cIixcInVzZXJOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJJZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwicGxhdGZvcm1hZG1pblwiLFwiZGVwdENvZGVcIjpcIjEwMFwifSIsImV4cCI6MTY3ODcxNjQxNiwiaWF0IjoxNjc4NjczMjE2LCJqdGkiOiJmMGM4ZWRkODUxOTI0MTc1OTM3MDg0NTkwZGE5Mjk3NCJ9.C0KjbSbJEqUDpRsByfljyZf5ajzlgybPZEHmQtcFqKA");
            interparam.put("page","1");
            interparam.put("limit","500");
            interparam.put("type",type);
            String str=util.getResult("http://19.125.2.167:4900/sup/supapi/api/test/gdTask",interparam,false,"form");
            System.out.println(str);
            long endTime = System.currentTimeMillis();
            if (endTime-startTime < 2 * 1000) {
                break;
            }
        }
        return null;
    }

    @RequestMapping("/test23")
    public ResultList test23() {
//        gdInterfaceTask.importOrderData(null,null);
//        return null;
        QueryWrapper<InterfaceLog> queryWrapper = new QueryWrapper<>();
        String message = "未找到对应医院";
        queryWrapper.eq("import_flag", "2").like("message",message);
        List<InterfaceLog> logList = iInterfaceLogService.list(queryWrapper);

        String ids = "";
        JSONArray list = new JSONArray();
        List<String> a = new ArrayList<>();
        for (int i =0;i < logList.size();i++) {
            JSONObject object = JSONObject.parseObject(logList.get(i).getReturnValue());
            JSONObject o = new JSONObject();
            if (StringUtils.isEmpty(object.getString("buyerId"))) {
                continue;
            }
            if (a.size() > 0 && a.contains(object.getString("buyerId"))) {
                continue;
            }

            a.add(object.getString("buyerId"));
            o.put("name",object.getString("buyerName"));
            o.put("code",object.getString("buyerId"));
            list.add(o);
        }

        return ResultUtil.successToList(list);

    }
    public static void main (String [] args) {
        while (1 == 1) {
            JSONObject interparam = new JSONObject();
            HttpClientUtil util = new HttpClientUtil("A0001","A0001","eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJyb2xlSWRcIjpcIjEsMlwiLFwicm9sZUNvZGVcIjpcIlNVUEVSX0FETUlOLERBVEFfQURNSU5cIixcInVzZXJOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJJZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwicGxhdGZvcm1hZG1pblwiLFwiZGVwdENvZGVcIjpcIjEwMFwifSIsIngtYWVwLXVpZCI6IntcInJvbGVJZFwiOlwiMSwyXCIsXCJyb2xlQ29kZVwiOlwiU1VQRVJfQURNSU4sREFUQV9BRE1JTlwiLFwidXNlck5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwidXNlcklkXCI6XCIxXCIsXCJhY2NvdW50XCI6XCJwbGF0Zm9ybWFkbWluXCIsXCJkZXB0Q29kZVwiOlwiMTAwXCJ9IiwiZXhwIjoxNjY3NTg5ODgzLCJpYXQiOjE2Njc1NDY2ODMsImp0aSI6IjNjMjBlMWRjOTY4YjQ0YmFiMjQ2OGUwZjdjYjYwODI5In0.P0ZljaCEpJRHHCB7DCKLnCoGqzJSeL46EZMYqZX6ON0");
            interparam.put("page","1");
            interparam.put("limit","10");
            interparam.put("type","5");
            String str=util.getResult("http://192.168.73.157:9333/supapi/api/test/gdTask",interparam,false,"form");
            System.out.println(str);
        }
    }


    @GetMapping("/conMaterialDeliveryCompanyTest")
    public List<ConMaterialDeliveryCompany> conMaterialDeliveryCompanyTest(){
        List<ConMaterialDeliveryCompany> list = conMaterialDeliveryCompanyService.list();
        return list;
    }

}
