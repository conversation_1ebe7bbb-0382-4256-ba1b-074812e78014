package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.InterfaceLog;
import com.inspur.ssp.supervise.bean.entity.OrderDetailRecent;
import org.jangod.iweb.core.bean.PageList;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-27
 */
public interface OrderDetailRecentService extends IService<OrderDetailRecent> {
    PageList<OrderDetailRecent> getOrderDetailList(int page,int limit,Map map);
    PageList<OrderDetailRecent> getOrderList(int page,int limit,Map map);

}
