package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ConMaterialDeliveryItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.MaterialSourceAmountVo;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 配送单明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
public interface IConMaterialDeliveryItemService extends IService<ConMaterialDeliveryItem> {

    Result updateDeliveryStatus(ConMaterialDeliveryItem conMaterialDeliveryItem);

    List<Map<String, Object>> getConMaterialDeliveryItemList(Map<String, Object> params);

    List<Map<String, Object>> getConMaterialDeliveryItemDetail(String deliveryCode);

    List<MaterialSourceAmountVo> getMaterialSourceAmountByHospital(Map<String, Object> params);
}
