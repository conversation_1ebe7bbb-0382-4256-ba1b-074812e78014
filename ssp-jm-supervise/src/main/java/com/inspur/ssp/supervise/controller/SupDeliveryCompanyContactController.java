package com.inspur.ssp.supervise.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.service.ISupDeliveryCompanyContactService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/31 20:54
 */
@RestController
@RequestMapping("/supervise/supDeliveryCompanyContact")
public class SupDeliveryCompanyContactController extends AbstractController {

    @Autowired
    private ISupDeliveryCompanyContactService supDeliveryCompanyContactServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;


    @RequestMapping("/getCompanyContact")
    public Result getCompanyContact() {
        try {
            Map<String, Object> params = this.getRequestParams();
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
            List<Map<String, Object>> orderItemList = supDeliveryCompanyContactServiceImpl.getCompanyContact(params);
            PageList<Map<String, Object>> selectPage = new PageList<>(orderItemList);
            return ResultUtil.successToList(selectPage);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("查询失败");
        }
    }

}
