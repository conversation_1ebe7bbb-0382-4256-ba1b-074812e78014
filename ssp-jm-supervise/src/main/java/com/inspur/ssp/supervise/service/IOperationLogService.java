package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.OperationLog;

import java.util.List;
import java.util.Map;

/**
 * 服务类:用户操作日志
 * <AUTHOR>
 * @Date 2023 01 09 15 20
 **/
public interface IOperationLogService extends IService<OperationLog> {

    List<OperationLog> getList(Map<String, Object> map);

    List<OperationLog> getListUserLogin(Map<String, Object> map);

    List<OperationLog> getTreeStructure(List<OperationLog> list,Map<String,Object> map);
}
