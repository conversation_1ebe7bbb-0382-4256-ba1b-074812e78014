package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.SupOrderStockDto;
import com.inspur.ssp.supervise.bean.entity.SupOrderStock;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.Result;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2020-05-12
 */
public interface ISupOrderStockService extends IService<SupOrderStock> {
     Result saveOrderStock(SupOrderStockDto orderStockDto,String userId);
}
