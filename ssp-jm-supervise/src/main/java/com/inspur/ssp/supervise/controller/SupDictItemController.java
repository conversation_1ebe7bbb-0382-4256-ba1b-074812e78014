package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.SupDict;
import com.inspur.ssp.supervise.bean.entity.SupDictItem;
import com.inspur.ssp.supervise.bean.vo.SupDictVo;
import com.inspur.ssp.supervise.service.ISupDictItemService;
import com.inspur.ssp.supervise.service.ISupDictService;
import com.inspur.ssp.supervise.service.impl.SupDictItemServiceImpl;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-05-18
 */
@RestController
@RequestMapping("/supervise/supDictItem")
public class SupDictItemController extends AbstractController {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private ISupDictItemService supDictItemServiceImpl;
    @Autowired
    private ISupDictService supDictServiceImpl;

    /**
     *  根据dict的code值获取下拉数据
     * @return
     */
    @ApiOperation("根据dict的code值获取下拉数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "字典编码", required = true, dataType = "String"),
    })
    @RequestMapping("/getItemList")
    public Result getDictItemListByCode(){

        try{
            Map<String, Object> params = this.getRequestParams();
            String code = (String) params.get("code");
            if (StringUtil.isBlank(code)){
                return ResultUtil.error("字典编码不能为空");
            }else{
                List<SupDictVo> dictItem = supDictItemServiceImpl.getDictItem(code);
                return ResultUtil.successToList(dictItem);
            }
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ResultUtil.error(e.getMessage());
        }
    }
}

