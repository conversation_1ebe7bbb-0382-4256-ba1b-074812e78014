package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDeliveryItem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 配送单明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
public interface ISupDeliveryItemService extends IService<SupDeliveryItem> {

    List<SupDeliveryItem> getDeliveryItemNotExistsOrderItem(Map<String,Object> params);

    List<Map<String,Object>> getDeliveryItemDetail(String deliveryCode);


    List<Map<String,Object>> getDeliveryItemList(Map<String,Object> params);


    List<Map<String,Object>> getDeliveryListOnHospital(Map<String,Object> params);

     Result updateDeliveryStatus(SupDeliveryItem supDeliveryItem) ;

    List<Map<String,Object>> getDeliveryListByHospital(Map<String,Object> params);

    /**
     * 获取超时未入库配送明细设置预警
     * @param params
     * @return
     */
    List<SupDeliveryItem> getOverStockInDeliveryItem(Map<String,Object> params);

    /**
     * 获获取超时未入库配送明细列表
     * @param params
     * @return
     */
    List<Map<String, Object>> getOverStockList(Map<String,Object> params);

    void updateWarning(SupDeliveryItem supDeliveryItem);

    /**
     * 获取超时已完成入库的配送信息
     * @param params
     * @return
     */
    List<SupDeliveryItem> getCompleteStockInDeliveryItem(Map<String,Object> params);

    void flushCountryDeliveryItem(Map<String, Object> upParams);
}
