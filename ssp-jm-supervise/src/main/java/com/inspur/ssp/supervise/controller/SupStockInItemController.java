package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.entity.SupStockIn;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import com.inspur.ssp.supervise.service.ISupStockInItemService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@RestController
@RequestMapping("/supervise/supStockInItem")
public class SupStockInItemController extends AbstractController {
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Autowired
    private ISupStockInItemService supStockInItemServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @ApiOperation(value = "获取配送单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "stockInCode", value = "入库单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "stockInName", value = "配送企业", required = false, dataType = "String"),
    })
    @RequestMapping("/getStockInItemList")
    public Result getStockInItemList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String startTime=(String) params.get("stockInTime[0]");
        String endTime=(String) params.get("stockInTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        String roleValue = "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
           roleValue = user.getRoleValue();
             if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId",hospitalId);

                }
            }
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime",startTime);
            params.put("endTime",endTime);

        }
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> stockInItemList = supStockInItemServiceImpl.getStockInItemList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(stockInItemList);
        return ResultUtil.successToList(selectPage);
    }

}

