package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugCompany;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
public interface ISupDrugCompanyService extends IService<SupDrugCompany> {

    /**
     * 检查生产企业，有则返回，无则新增返回
     * @param drugCompanyName
     * @return
     */
    SupDrugCompany checkDrugCompany(String drugCompanyName,String userId);



}
