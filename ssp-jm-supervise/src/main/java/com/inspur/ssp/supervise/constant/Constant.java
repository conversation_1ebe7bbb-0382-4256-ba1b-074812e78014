package com.inspur.ssp.supervise.constant;

/**
 * Created by chengying 2020/04/27
 */
public class Constant {
    public static String DATA_TYPE_HOSPITAL = "hospital"; //医院类型
    public static String DATA_TYPE_DRUG = "drug";//药品类型
    public static String DATA_TYPE_PURCHASE = "purchase";//药品类型
    public static String DATA_TYPE_PURCHASE_SPEED = "purchaseSpeed";//药品类型
    public static String DATA_TYPE_COUNTRY_ORDER = "countryOrder";//国家采集药品订单
    public static String DATA_TYPE_COUNTRY_ORDER_BACK = "orderNumberBack";//国家采集药品订单
    public static String DATA_TYPE_OFFLINE_ORDER = "offlineOrder";//线下药品订单订单
    public static String DATA_TYPE_COUNTRY_DELIVERY = "countryDelivery";//国家采集药品配送单
    public static String DATA_TYPE_COUNTRY_STOCK_IN = "countryStockIn";//国家采集药品入库单
    public static String DATA_TYPE_COUNTRY_INVOICE = "countryInvoice";//国家采集药品发票单
    public static String DATA_TYPE_HOSPITAL_DRUG = "hospitalDrug";//药品类型


    public static String FAIL_STATUS_TODO="0";//待处理
    public static String FAIL_STATUS_DONE="1";//已处理
    public static String FAIL_STATUS_UNDONE="-1";//不处理

    public static String ERROR_CODE_DATA="001";//要素错误
    public static String ERROR_CODE_BUSI="002";//业务错误

    public static String OPERATION_TYPE_ALL="all";//全量
    public static String OPERATION_TYPE_ADD="add";//新加
    public static String OPERATION_TYPE_DELETE="del";//移除

    public static String BAK_STRING = "bak_";
    public static String ROLLBACK_STRING = "rollback_";

    public static String IMPORT_TODO="0";//导入待处理
    public static String IMPORT_STATR="1";//导入开始
    public static String IMPORT_END="2";//导入结束

    public static String ORDER_STATUS_DELETE="-1";//订单删除
    public static String ORDER_STATUS_ZACCUN="0";//订单暂存
    public static String ORDER_STATUS_SUBMIT="1";//订单提交
    public static String ORDER_STATUS_END="2";//订单完成

    public static String ORDER_UN_PAY="0";//订单未支付
    public static String ORDER_SUCESS_PAY="1";//订单已支付
    public static String ORDER_PART_PAY="2";//订单部分支付

    public static String OFFICIAL_INSURANCE_FUND_NOTICE = "insurance_fund_notice";//医保基金发送超时未支付公文
    public static String OFFICIAL_INSURANCE_SMS_NOTICE = "insurance_sms_notice";//医保基金发送超时未支付短信提醒
    public static String OFFICIAL_INSURANCE_DEDUCTION_NOTICE = "insurance_deduction_notice";//医保基金支付成功详情


    public static String SMS_STATUS_NO="0";//未发送
    public static String SMS_STATUS_SUCCESS="1";//发送成功
    public static String SMS_STATUS_FAIL="2";//发送失败

    public static String SMS_STATUS_BATCH_NO="10";//特殊标记批量发送短信未发送

    public static String DEDUCTION_STATUS_CREATE="0";//生成扣款单
    public static String DEDUCTION_STATUS_HOSPITAL="1";//医院确认
    public static String DEDUCTION_STATUS_SOCIAL="2";//社保确认
    public static String DEDUCTION_STATUS_PAY="3";//支付指令确认
    public static String DEDUCTION_STATUS_END="4";//确认完成


    public static final String GD_INTERFACE_PAGE_NOW="GD_INTERFACE_PAGE_NOW";//广东省接口当前页
    public static final String GD_INTERFACE_PAGE_TOTAL="GD_INTERFACE_PAGE_TOTAL";//广东省接口总页数
    public static final String GZ_INTERFACE_PAGE_NOW="GZ_INTERFACE_PAGE_NOW";//广州省接口当前页
    public static final String GZ_INTERFACE_PAGE_TOTAL="GZ_INTERFACE_PAGE_TOTAL";//广州省接口总页数


    public static final String GD_INTERFACE_PAGE_FAIL_NOW="GD_INTERFACE_PAGE_FAIL_NOW";//广东省接口当前页（接口失败处理）
    public static final String GD_INTERFACE_PAGE_FAIL_TOTAL="GD_INTERFACE_PAGE_FAIL_TOTAL";//广东省接口总页数（接口失败处理）



    public static final String GDHC_INTERFACE_PAGE_NOW="GDHC_INTERFACE_PAGE_NOW";//广东省耗材接口当前页
    public static final String GDHC_INTERFACE_PAGE_TOTAL="GDHC_INTERFACE_PAGE_TOTAL";//广东省耗材接口总页数


    public static final String GDHC_INTERFACE_PAGE_FAIL_NOW="GDHC_INTERFACE_PAGE_FAIL_NOW";//广东省耗材接口当前页（接口失败处理）
    public static final String GDHC_INTERFACE_PAGE_FAIL_TOTAL="GDHC_INTERFACE_PAGE_FAIL_TOTAL";//广东省耗材接口总页数（接口失败处理）



    public static String SOURCE_SZ="1";//深圳
    public static String SOURCE_GD="2";//广东

    public static String SOURCE_GD_HC="21";//广东耗材

    public static String SOURCE_GZ="3";//广州
    public static String SOURCE_OFF_LINE="4";//线下
    public static String GZ_ACCESS_TOKEN="GZ_ACCESS_TOKEN";//广州

    //日志类型
    public final static String LOG_TYPE_JOB = "job";//定时任务



}
