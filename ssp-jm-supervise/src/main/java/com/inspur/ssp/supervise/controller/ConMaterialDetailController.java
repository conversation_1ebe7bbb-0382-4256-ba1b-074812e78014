package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.DrugPriceView;
import com.inspur.ssp.supervise.bean.vo.ConMaterialDetailVo;
import com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo;
import com.inspur.ssp.supervise.service.IConMaterialDetailService;
import com.inspur.ssp.supervise.service.IDrugPriceViewService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.db.util.BeanUtils;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 耗材清单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@RestController
@RequestMapping("/supervise/conMaterialDetail")
public class ConMaterialDetailController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private IConMaterialDetailService conMaterialDetailServiceImpl;

    @Autowired
    private IDrugPriceViewService drugPriceViewServiceImpl;

    @ApiOperation(value = "获取耗材列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "goodsName", value = "耗材名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "耗材类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "耗材编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"),
            @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        IUser currentUser = this.getCurrentUser();
        if (currentUser == null) {
            return ResultUtil.error("用户登录超时，请重新登录");
        }
        PageHelper.startPage(page, limit);


        List<ConMaterialDetailVo> conMaterialDetailVos = conMaterialDetailServiceImpl.selectConMaterialList(params);

        if (CollectionUtils.isNotEmpty(conMaterialDetailVos)) {
            List<String> drugCodes = conMaterialDetailVos.stream().map(ConMaterialDetailVo::getCode).collect(Collectors.toList());
            QueryWrapper<DrugPriceView> priceViewQueryWrapper = new QueryWrapper<>();
            priceViewQueryWrapper.lambda().in(DrugPriceView::getDrugCode, drugCodes);
            List<DrugPriceView> drugPriceViews = drugPriceViewServiceImpl.list(priceViewQueryWrapper);

            for (ConMaterialDetailVo conMaterialDetailVo : conMaterialDetailVos) {
                String code = conMaterialDetailVo.getCode();
                List<DrugPriceView> priceViewArray = drugPriceViews.stream().filter(s -> s.getDrugCode().equals(code)).collect(Collectors.toList());
                BigDecimal price = new BigDecimal(0);
                String source = "";
                if (CollectionUtils.isNotEmpty(priceViewArray)) {
                    for (DrugPriceView drugPriceView : priceViewArray) {
                        if (price.compareTo(new BigDecimal(0)) == 0) {
                            price = drugPriceView.getPrice();
                            source = drugPriceView.getSource();
                        } else {
                            BigDecimal price2 = drugPriceView.getPrice();
                            if (price2.compareTo(price) < 0) {
                                price = price2;
                                source = drugPriceView.getSource();
                            } else if (price2.compareTo(price) == 0) {
                                String source2 = drugPriceView.getSource();
                                if (Integer.parseInt(source) > Integer.parseInt(source2)) {
                                    source = source2;
                                }
                            }
                        }
                    }
                }
                conMaterialDetailVo.setPrice(price);
                conMaterialDetailVo.setSource(source);
                List<SupDrugPriceVo> priceArray = BeanUtils.convertList(priceViewArray, SupDrugPriceVo.class);
                conMaterialDetailVo.setPriceArray(priceArray);
            }
        }
//        PageList<SupDrugDetailVo> selectPage = new PageList<>(conMaterialDetailVos);
        return ResultUtil.successToList(conMaterialDetailVos);
    }


    @ApiOperation(value = "耗材详细信息")
    @RequestMapping("/info/{conMaterialDetailId}")
    public Result info(@ApiParam("耗材id") @PathVariable("conMaterialDetailId") String conMaterialDetailId) {
        Map<String, Object> Detail = conMaterialDetailServiceImpl.getConMaterialDetailById(conMaterialDetailId);
        if (Detail == null) {
            return ResultUtil.error("耗材未找到");
        }
        return ResultUtil.successToObject(Detail);
    }

    @ApiOperation("根据平台分组获取耗材数量")
    @PostMapping("/getConMaterialCountBySource")
    public Result getConMaterialCountBySource() {
        Map<String, Object> params = this.getRequestParams();
        String allType = (String) params.get("allType");
        if (StringUtil.isBlank(allType) || !allType.equals("all")) {
            IUser user = (IUser) this.getCurrentUser();
            if(user == null){
                return  ResultUtil.error("登录过期，请重新登录");

            }
        }
        List<Map<String, Object>> conMaterialCountBySource = conMaterialDetailServiceImpl.getConMaterialCountBySource(params);
        return ResultUtil.successToList("查询成功", conMaterialCountBySource);
    }



}

