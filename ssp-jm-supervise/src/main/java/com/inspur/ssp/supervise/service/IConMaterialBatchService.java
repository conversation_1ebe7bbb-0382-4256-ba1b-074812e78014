package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.ConMaterialBatch;
import com.inspur.ssp.supervise.bean.entity.ConMaterialContractInfo;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07
 */
public interface IConMaterialBatchService extends IService<ConMaterialBatch> {
    /**
     * 根据批次code获取批次
     * @param projectCode
     * @return
     */
    ConMaterialBatch selectByCode(String projectCode);

    /**
     * 获取最新一次的排序序号
     * @return
     */
    int getSortOrderNew();

    /**
     * 保存集采批次
     * @param conMaterialContractInfo
     */
    void savaConMaterialBatch(ConMaterialContractInfo conMaterialContractInfo);
}
