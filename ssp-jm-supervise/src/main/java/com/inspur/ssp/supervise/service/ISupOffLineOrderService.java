package com.inspur.ssp.supervise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.dto.SupOrderContrastPriceDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugVo;
import org.jangod.iweb.core.bean.IUser;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2021-2-1
 */
public interface ISupOffLineOrderService{
}
