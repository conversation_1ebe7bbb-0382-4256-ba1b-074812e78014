package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.bean.vo.*;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@RestController
@RequestMapping("/supervise/conMaterialOrder")
public class ConMaterialOrderController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private IConMaterialOrderService conMaterialOrderServiceImpl;

    @Autowired
    private IConMaterialOrderItemService conMaterialOrderItemServiceImpl;

    @Autowired
    private ISupDictItemService supDictItemServiceImpl;

    @Autowired
    private IConMaterialDetailService conMaterialDetailServiceImpl;

    @Autowired
    private ISupDrugCompanyService supDrugCompanyServiceImpl;

    @Autowired
    private ISupHospitalService supHospitalServiceImpl;

    @ApiOperation(value = "获取订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<ConMaterialOrder> queryWrapper = new QueryWrapper<>();
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                queryWrapper.between("SUBMIT_TIME", beginTime, endTime);
            } else if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    queryWrapper.lambda().eq(ConMaterialOrder::getHospitalId, hospitalUser.getHospitalId());
                }
            }
            String orderNum = (String) params.get("orderNum");
            String hospitalName = (String) params.get("hospitalName");
            if (StringUtil.isNotBlank(hospitalName)) {
                queryWrapper.lambda().like(ConMaterialOrder::getHospitalName, hospitalName);
            }
            String source = (String) params.get("source");
            String warning = (String) params.get("warning");
            String orderStatus = (String) params.get("orderStatus");
            String stockStatus = (String) params.get("stockStatus");
            String payStatus = (String) params.get("payStatus");
            String hospitalId = (String) params.get("hospitalId");
            String type = (String) params.get("type");
            String regionId = (String) params.get("regionId");
            if (StringUtil.isNotBlank(regionId)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getRegionId,regionId);
            }
            if (StringUtil.isNotBlank(hospitalId)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getHospitalId, hospitalId);
            }
            if (StringUtil.isNotBlank(type)) {
                if ("1".equals(type)) {
                    queryWrapper.lambda().eq(ConMaterialOrder::getWarning, type);
                }
                if ("2".equals(type)) {
                    queryWrapper.lambda().ne(ConMaterialOrder::getWarning, "1");
                }
            }


            if (StringUtil.isNotBlank(stockStatus)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getStockStatus, stockStatus);
            }
            if (StringUtil.isNotBlank(source)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getSource, source);
            }
            if (StringUtil.isNotBlank(payStatus)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getPayStatus, payStatus);
            }

            if (StringUtil.isNotEmpty(orderNum)) {
                queryWrapper.lambda().likeRight(ConMaterialOrder::getOrderNum, orderNum);
            }
            if (StringUtil.isNotEmpty(warning)) {
                if (warning.equals("1")) {
                    queryWrapper.lambda().eq(ConMaterialOrder::getWarning, warning);
                } else {
                    queryWrapper.lambda().like(ConMaterialOrder::getWarning, warning);
                }
            }
            if (StringUtil.isNotEmpty(orderStatus)) {
                queryWrapper.lambda().eq(ConMaterialOrder::getOrderStatus, orderStatus);
            }
            queryWrapper.lambda().orderByDesc(ConMaterialOrder::getSubmitTime).orderByAsc(ConMaterialOrder::getHospitalName);
            PageHelper.startPage(page, limit);
            List<ConMaterialOrder> list = conMaterialOrderServiceImpl.list(queryWrapper);
//            PageList<ConMaterialOrder> selectPage = new PageList<>(list);
            return ResultUtil.successToList(list);
        } catch (ParseException e) {
            e.printStackTrace();
            return ResultUtil.error("订单查询失败");
        }
    }


    @ApiOperation(value = "订单详情")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String orderId) {
        try {
            ConMaterialOrder order = conMaterialOrderServiceImpl.getById(orderId);
            if (order == null) {
                return ResultUtil.error("无此订单，请联系管理员");
            }
            String warning = order.getWarning();
            String[] split = warning.split(",");
            List<SupDictVo> warningData = supDictItemServiceImpl.getDictItem("WARNING");
            List<String> dictLabel = new ArrayList<>();
            for (String s : split) {
                Optional<SupDictVo> supDictVoData = warningData.stream().filter(item -> item.getValue().equals(s)).findFirst();
                if (supDictVoData.isPresent()) {
                    // 存在
                    SupDictVo supDictVo = supDictVoData.get();
                    String label = supDictVo.getLabel();
                    dictLabel.add(label);
                }
            }
            order.setWarning(String.join(" | ", dictLabel));
            QueryWrapper<ConMaterialOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ConMaterialOrderItem::getOrderId, orderId);
            List<ConMaterialOrderItemVo> orderItem = new ArrayList<>();
            List<ConMaterialOrderItem> items = conMaterialOrderItemServiceImpl.list(queryWrapper);
            for (ConMaterialOrderItem item : items) {
                ConMaterialOrderItemVo orderItemVo = BeanUtil.copyProperties(item, ConMaterialOrderItemVo.class);
                String detailId = item.getDetailId();
                ConMaterialDetail detail = conMaterialDetailServiceImpl.getById(detailId);
                orderItemVo.setDosageForm(detail.getDosageForm());
                orderItemVo.setPackingSpecs(detail.getPackingSpecs());
                orderItemVo.setSpecs(detail.getSpecs());
                orderItemVo.setCountry(detail.getCountry());
                orderItemVo.setConCompanyId(detail.getConCompanyId());
                orderItemVo.setRegCredName(detail.getRegCredName());
                orderItemVo.setRegCredModel(detail.getRegCredModel());
                orderItemVo.setRegCredNum(detail.getRegCredNum());
                orderItemVo.setRegCredSpec(detail.getRegCredSpec());
                SupDrugCompany supDrugCompany = supDrugCompanyServiceImpl.getById(detail.getConCompanyId());
                if (Objects.nonNull(supDrugCompany)) {
                    orderItemVo.setConCompanyName(supDrugCompany.getName());
                }
                orderItem.add(orderItemVo);
            }
            SupHospital hospital = supHospitalServiceImpl.getById(order.getHospitalId());

            ConMaterialOrderDetailVo orderDetailVo = new ConMaterialOrderDetailVo();
            orderDetailVo.setData(order);
            orderDetailVo.setOrderItem(orderItem);
            orderDetailVo.setHospital(hospital);

            return ResultUtil.successToObject(orderDetailVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }


    @ApiOperation("根据平台分组获取耗材采购情况")
    @PostMapping("/getConMaterialSourceAmountByCity")
    public Result getConMaterialSourceAmountByCity() {
        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("submitTime[0]");
        String endTime = (String) params.get("submitTime[1]");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }
        Map<String, Object> data = conMaterialOrderServiceImpl.getConMaterialSourceAmountByCity(params);
        return ResultUtil.successToObject("查询成功", data);
    }

    @ApiOperation("采购金额趋势")
    @RequestMapping("/getConMaterialOrderPriceData")
    public Result getConMaterialOrderPriceData() {
        try {
            Map<String, Object> params = this.getRequestParams();
            //根据时间区间查询
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);
            }
            List<Map<String, Object>> priceData = conMaterialOrderServiceImpl.getConMaterialOrderPriceData(params);
            return ResultUtil.successToList("查询成功", priceData);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error(e.getMessage());
        }
    }
}

