package com.inspur.ssp.supervise.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetailExportExcel;
import com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 药品清单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
public interface ISupDrugDetailExportExcelService extends IService<SupDrugDetail> {

    List<SupDrugDetailExportExcel> selectDrugListJcExportExcel(Map<String, Object> params);

    List<SupDrugDetailExportExcel> selectDrugListExportExcel(Map<String, Object> params);

}
