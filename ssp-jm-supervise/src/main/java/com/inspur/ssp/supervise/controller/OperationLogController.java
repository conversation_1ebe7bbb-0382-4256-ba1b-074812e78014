package com.inspur.ssp.supervise.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.OperationLog;
import com.inspur.ssp.supervise.bean.enumdto.OperationLogEnum;
import com.inspur.ssp.supervise.service.IOperationLogService;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023 01 10 15 22
 **/
@RestController
@RequestMapping("/supervise/Operation")
public class OperationLogController extends AbstractController {
    @Qualifier("operationLogServiceImpl")
    @Autowired
    private IOperationLogService operationLogService;


    @PostMapping("/getOperationLogList")
    public Result getOperationLogList(@RequestParam int page, @RequestParam int limit) {
        if (this.getCurrentUser() == null) {
            return ResultUtil.error("获取用户失败，请重新登录");
        }
        try {

            Map<String, Object> params = this.getRequestParams();
            Map<String, Object> map = new HashMap<>();
            String code = (String) params.get("code");
            String codeType = (String) params.get("codeType");
            String startLogTime = (String) params.get("createTime[0]");
            String endLogTime = (String) params.get("createTime[1]");
            String checked = (String) params.get("checked");
            QueryWrapper<OperationLog> query = new QueryWrapper<>();
            if (StringUtil.isNotBlank(code)) {
                query.eq("CODE", code);
                map.put("code", code);
            }
            if (StringUtil.isNotBlank(startLogTime) && StringUtil.isNotBlank(endLogTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(startLogTime);
                Date endTime = formatter.parse(endLogTime);
                query.between("CREATE_TIME", beginTime, endTime);
                map.put("beginTime", startLogTime);
                map.put("endTime", endLogTime);
            }
            if (StringUtil.isNotBlank(codeType)) {
                query.eq("CODE_TYPE", codeType);
                map.put("codeType", codeType);
            }
            if (StringUtil.isNotBlank(checked)) {
                map.put("checked", checked);
            }
            query.orderByDesc("CREATE_TIME");
            Boolean isChecked = false;
            if (!(codeType.equals(OperationLogEnum.GD.getCodeType()) ||
                    codeType.equals(OperationLogEnum.GZ.getCodeType()) || codeType.equals(OperationLogEnum.GDHC.getCodeType()) ||
                    codeType.equals(OperationLogEnum.SHENZHEN.getCodeType()) || codeType.equals(OperationLogEnum.userLogin.getCodeType()))) {
                PageHelper.startPage(page, limit);
                isChecked  = true;
            }


            List<OperationLog> list = new ArrayList<>();
            List<OperationLog> listAll = new ArrayList<>();


            if (codeType.equals(OperationLogEnum.userLogin.getCodeType())) {
                list = operationLogService.getListUserLogin(map); //单独处理用户登录
            } else if (codeType.equals(OperationLogEnum.drugDetail.getCodeType())) {
                list = operationLogService.list(query);//单独处理药品
            } else if(isChecked){
                list = operationLogService.list(query);
            }else{
                list = operationLogService.getList(map); //处理树结构的
            }

            listAll = operationLogService.getTreeStructure(list, map);


            PageList<OperationLog> selectPage = new PageList<>(listAll);
            if (codeType.equals(OperationLogEnum.GD.getCodeType()) ||
                    codeType.equals(OperationLogEnum.GZ.getCodeType()) || codeType.equals(OperationLogEnum.GDHC.getCodeType()) ||
                    codeType.equals(OperationLogEnum.SHENZHEN.getCodeType()) || codeType.equals(OperationLogEnum.userLogin.getCodeType())) {
                selectPage.setTotal(listAll.size());
                listAll.sort(Comparator.comparing(OperationLog::getGroupData).reversed()); //排序
                listAll = listAll.stream().skip((page - 1) * limit).limit(limit).collect(Collectors.toList()); //分页
                selectPage.setRows(listAll);
            }

            return ResultUtil.successToList("查询成功", selectPage);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.errorToList(e.getMessage());
        }
    }

    @ApiOperation("记录用户登陆操作日志")
    @PostMapping("/userloginLog")
    public Result saveUserloginLog() throws Exception {
        IUser user = this.getCurrentUser();
        String id = Tools.genId() + "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        OperationLog oplog = new OperationLog();
        oplog.setId(id);
        oplog.setUserId(user.getId());
        oplog.setUserName(user.getName());
        oplog.setCodeType("userLogin");
        oplog.setOperationName("用户登陆");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String time = df.format(new Date());// new Date()为获取当前系统时间
        Date createTime = df.parse(time);
        oplog.setCreateTime(createTime);
        operationLogService.save(oplog);
        return ResultUtil.success();
    }

    @ApiOperation("记录支付凭证操作日志")
    @RequestMapping("/payVoucherLog")
    public Result savePayVoucherLog() throws Exception {
        IUser user = this.getCurrentUser();
        Map<String, Object> params = this.getRequestParams();
        String id = Tools.genId() + "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        OperationLog oplog = new OperationLog();
        oplog.setId(id);
        oplog.setUserId(user.getId());
        oplog.setUserName(user.getName());
        oplog.setCodeType("payVoucher");
        oplog.setOperationName("上传了支付凭证");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String time = df.format(new Date());// new Date()为获取当前系统时间
        Date createTime = df.parse(time);
        oplog.setCreateTime(createTime);
        oplog.setInvoiceNo(params.get("invoiceNo").toString());
        oplog.setFileName(params.get("fileName").toString());
        operationLogService.save(oplog);
        return ResultUtil.success();
    }


}
