package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.DrugViewDto;
import com.inspur.ssp.supervise.bean.entity.DrugView;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * VIEW 服务类
 * </p>
 *
 * <AUTHOR> @since 2020-04-30
 */
public interface IDrugViewService extends IService<DrugView> {
    List<DrugView> getDrug(Map<String,Object> params);
    List<Map<String,Object>> getDrugBySource(Map<String,Object> params);
    List<DrugViewDto> selectOffLineDrug(Map<String, Object> params);
}
