package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.excel.InvoiceItemExcel;
import com.inspur.ssp.supervise.bean.dto.excel.OverStockExcel;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.dao.BusiException;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@RestController
@RequestMapping("/supervise/supInvoiceItem")
public class SupInvoiceItemController extends AbstractController {
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;


    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Value("${deliveryAdmin}")
    private String deliveryAdmin;
    @Value("${hospitalAdmin}")
    private String hospitalAdmin;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;

    @ApiOperation(value = "获取发票单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "deliveryCode", value = "发票单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
    })
    @RequestMapping("/getInvoiceItemList")
    public Result getInvoiceItemList(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("invoiceDate[0]");
        String endTime = (String) params.get("invoiceDate[1]");
        String stockInStartTime = (String) params.get("stockInDate[0]");
        String stockInEndTime = (String) params.get("stockInDate[1]");
        String orderStartTime = (String) params.get("submitTime[0]");
        String orderEndTime = (String) params.get("submitTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        String roleValue;
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
             roleValue = user.getRoleValue();
            if (roleValue.contains(hospitalAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId", hospitalId);

                }
            }
        }

        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }
        if (StringUtil.isNotBlank(orderStartTime) && StringUtil.isNotBlank(orderEndTime)) {
            params.put("orderStartTime", orderStartTime);
            params.put("orderEndTime", orderEndTime);

        }
        if (StringUtil.isNotBlank(stockInStartTime) && StringUtil.isNotBlank(stockInEndTime)) {
            params.put("stockInStartTime", stockInStartTime);
            params.put("stockInEndTime", stockInEndTime);

        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> invoiceItemList = supInvoiceItemServiceImpl.getInvoiceItemList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(invoiceItemList);
        return ResultUtil.successToList(selectPage);

    }
    @ApiOperation(value = "获取发票单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "deliveryCode", value = "发票单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
    })
    @RequestMapping("/getInvoiceItemListToExcel")
    public void getInvoiceItemListToExcel(HttpServletResponse response, HttpServletRequest request) throws Exception {

        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("invoiceDate[0]");
        String endTime = (String) params.get("invoiceDate[1]");
        String stockInStartTime = (String) params.get("stockInDate[0]");
        String stockInEndTime = (String) params.get("stockInDate[1]");
        String orderStartTime = (String) params.get("submitTime[0]");
        String orderEndTime = (String) params.get("submitTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            throw new BusiException("用户登录超时，请重新登录。");
        } else {
            String roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    throw new BusiException("用户未绑定医院，请联系管理员绑定。");
                } else {
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId", hospitalId);

                }
            }
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);

        }
        if (StringUtil.isNotBlank(orderStartTime) && StringUtil.isNotBlank(orderEndTime)) {
            params.put("orderStartTime", orderStartTime);
            params.put("orderEndTime", orderEndTime);

        }
        if (StringUtil.isNotBlank(stockInStartTime) && StringUtil.isNotBlank(stockInEndTime)) {
            params.put("stockInStartTime", stockInStartTime);
            params.put("stockInEndTime", stockInEndTime);

        }
        List<Map<String, Object>> invoiceItemList = supInvoiceItemServiceImpl.getInvoiceItemList(params);
        List<InvoiceItemExcel> excelList = new ArrayList<>();
        for(Map<String,Object> map : invoiceItemList){
            InvoiceItemExcel excelObj = BeanUtil.convert(map, InvoiceItemExcel.class);
            excelList.add(excelObj);
        }

        String fileName="超时未支付";
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), InvoiceItemExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(InvoiceItemExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,contentWriteCellStyle))
                .sheet(fileName)
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(excelList);

    }

    @ApiOperation(value = "获取发票明细列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "deliveryCode", value = "发票单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
    })
    @RequestMapping("/getInvoiceItemListNew")
    public Result getInvoiceItemListNew(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
        String startTime = (String) params.get("invoiceTime[0]");
        String endTime = (String) params.get("invoiceTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        String roleValue = "";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId", hospitalId);

                }
            }
        }

        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> invoiceItemList = supInvoiceItemServiceImpl.getInvoiceItemListNew(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(invoiceItemList);
        return ResultUtil.successToList(selectPage);

    }


    @ApiOperation(value = "获取发票明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
    })
    @RequestMapping("/getInvoiceItem")
    public Result getInvoiceItem(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
//        String deliveryItemId = (String) params.get("deliveryItemId");
        String orderItemId = (String) params.get("orderItemId");
        LambdaQueryWrapper<SupInvoiceItem> queryWrapper = new LambdaQueryWrapper<>();
//        if (StringUtil.isNotBlank(deliveryItemId)) {
//            queryWrapper.eq(SupInvoiceItem::getDeliveryItemId, deliveryItemId);
//        } else {
//            return ResultUtil.errorToObject("配送明细id不能为空");
//        }
        if (StringUtil.isNotBlank(orderItemId)) {
            queryWrapper.eq(SupInvoiceItem::getOrderItemId, orderItemId);
        } else {
            return ResultUtil.errorToObject("订单明细id不能为空");
        }
        PageHelper.startPage(page, limit);
        List<SupInvoiceItem> list = supInvoiceItemServiceImpl.list(queryWrapper);
        PageList<SupInvoiceItem> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("保存支付凭证")
    @PostMapping("/savePayVoucher")
    public Result savePayVoucher(@RequestBody SupInvoiceItem supInvoiceItem) {

        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            supInvoiceItem.setLastModifier(user.getId());
            supInvoiceItem.setLastModifiedTime(new Date());
        }
        if (Objects.isNull(supInvoiceItem.getPayPrice()) || supInvoiceItem.getPayPrice().compareTo(BigDecimal.ZERO) == 0) {
            return ResultUtil.error("支付价格为大于0的金额。");
        }
        if (StringUtil.isEmpty(supInvoiceItem.getDocInfo())) {
            return ResultUtil.error("请上传支付凭证。");
        }
        if (Objects.isNull(supInvoiceItem.getPayTime())) {
            return ResultUtil.error("请选择支付时间。");
        }
        if (!supInvoiceItem.getMany()) {
            if (StringUtil.isEmpty(supInvoiceItem.getId())) {
                return ResultUtil.error("发票Id不能为空，请联系管理员。");
            }
        }
        Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);

        return result;
    }


    @ApiOperation("保存发票支付凭证")
    @PostMapping("/saveInvoicePayVoucher")
    public Result saveInvoicePayVoucher(@RequestBody SupInvoiceItem supInvoiceItem) {

        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        } else {
            supInvoiceItem.setLastModifier(user.getId());
            supInvoiceItem.setLastModifiedTime(new Date());
        }
        if (Objects.isNull(supInvoiceItem.getPayPrice()) || supInvoiceItem.getPayPrice().compareTo(BigDecimal.ZERO) == 0) {
            return ResultUtil.error("支付价格为大于0的金额。");
        }
        if (StringUtil.isEmpty(supInvoiceItem.getDocInfo())) {
            return ResultUtil.error("请上传支付凭证。");
        }
        if (Objects.isNull(supInvoiceItem.getPayTime())) {
            return ResultUtil.error("请选择支付时间。");
        }
        if (!supInvoiceItem.getMany()) {
            if (StringUtil.isEmpty(supInvoiceItem.getInvoiceId())) {
                return ResultUtil.error("发票Id不能为空，请联系管理员。");
            }
        }
        List<String> ids = new ArrayList<String>();
        Boolean many = supInvoiceItem.getMany();
        if (!many) {
            String invoiceId = supInvoiceItem.getInvoiceId();
            //查找该发票单号所有明细
            List<SupInvoiceItem> supInvoiceItems = supInvoiceItemServiceImpl.list(new QueryWrapper<SupInvoiceItem>().eq("INVOICE_ID", invoiceId));
            supInvoiceItem.setInvoiceVouchers(supInvoiceItems);
        } else {
            List<SupInvoiceItem> invoiceVouchers = supInvoiceItem.getInvoiceVouchers();
            for (SupInvoiceItem invoiceVoucher : invoiceVouchers) {
                String invoiceId = invoiceVoucher.getId();
                ids.add(invoiceId);
            }
            List<SupInvoiceItem> supInvoiceItems = supInvoiceItemServiceImpl.list(new QueryWrapper<SupInvoiceItem>().in("INVOICE_ID", ids));
            supInvoiceItem.setInvoiceVouchers(supInvoiceItems);
        }
        supInvoiceItem.setMany(true);
        Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);

        return result;
    }

}

