package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupInvoiceItem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.Result;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
public interface ISupInvoiceItemService extends IService<SupInvoiceItem> {
    List<Map<String, Object>> getInvoiceItemDetail(String invoiceCode);

    List<Map<String, Object>> getInvoiceItemList(Map<String, Object> params);


    List<Map<String,Object>> getInvoiceItemListNew(Map<String,Object> params);

    Result savePayVoucher(SupInvoiceItem supInvoiceItem);

    List<Map<String, Object>> getOverTimeNoPayList(Map<String, Object> params);

    List<SupInvoiceItem> getNoPayOnItemNoPay(Map<String, Object> map);

    void flushCountryInvoiceItem(Map<String, Object> upParams);
}
