package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ConMaterialInvoiceItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupInvoiceItem;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
public interface IConMaterialInvoiceItemService extends IService<ConMaterialInvoiceItem> {

    Result savePayVoucher(ConMaterialInvoiceItem conMaterialInvoiceItem);

    List<Map<String, Object>> getConMaterialInvoiceItemList(Map<String, Object> params);

}
