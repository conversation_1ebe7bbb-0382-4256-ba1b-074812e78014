package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gdsyj.webservice.util.PrintValue;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupHospital;
import com.inspur.ssp.supervise.bean.entity.SupJob;
import com.inspur.ssp.supervise.service.ISupJobService;
import com.sun.org.apache.xalan.internal.xsltc.compiler.util.ResultTreeType;
import io.swagger.annotations.*;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import javax.tools.Tool;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-08
 */
@RestController
@RequestMapping("/supervise/supJob")
public class SupJobController extends AbstractController {

  private Logger logger= LoggerFactory.getLogger(getClass());

    @Autowired
    private ISupJobService supJobServiceImpl;

    @ApiOperation("获取定时任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/getJobList")
    public Result getJobList(@RequestParam int page, @RequestParam int limit){
        try {
            Map<String, Object> params = this.getRequestParams();
            String name = (String) params.get("name");
            String jobGroup = (String) params.get("jobGroup");
            String status = (String) params.get("status");

            QueryWrapper<SupJob> queryWrapper = new QueryWrapper<>();
            if(StringUtil.isNotBlank(name)){
                queryWrapper.like("NAME",name);
            }
            if(StringUtil.isNotBlank(jobGroup)){
                queryWrapper.like("JOB_GROUP",jobGroup);
            }
            if(StringUtil.isNotBlank(status)){
                queryWrapper.eq("STATUS",status);
            }
            queryWrapper.orderByDesc("CREATION_TIME");
            queryWrapper.orderByDesc("LAST_MODIFICATION_TIME");
            PageHelper.startPage(page,limit);
            List<SupJob> list = supJobServiceImpl.list(queryWrapper);
            PageList<SupJob> listPageList = new PageList<SupJob>(list);
            return ResultUtil.successToList(listPageList);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("定时任务查询失败"+e.getMessage());
        }
    }

    @ApiOperation("保存定时任务")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "定时任务对象",required = true ) SupJob supJob){

        try {
            String name = supJob.getName();
            String jobGroup = supJob.getJobGroup();
            String action = supJob.getAction();
            String method = supJob.getMethod();
            String cron = supJob.getCron();
            if(StringUtil.isBlank(name)){
                return  ResultUtil.error("定时任务名称不能为空");
            }
            if(StringUtil.isBlank(jobGroup)){
                return  ResultUtil.error("定时任务分组名称不能为空");
            }
            if(StringUtil.isBlank(action)){
                return  ResultUtil.error("执行类名称不能为空");
            }
            if(StringUtil.isBlank(method)){
                return  ResultUtil.error("执行方法不能为空");
            }
            if(StringUtil.isBlank(cron)){
                return  ResultUtil.error("执行时间不能为空");
            }
            //校验
            SupJob jobName = supJobServiceImpl.getOne(new QueryWrapper<SupJob>().lambda().eq(SupJob::getName, supJob.getName()));
            if (jobName != null && StringUtil.isNotBlank(jobName.getId())) {
                return ResultUtil.error("定时任务名称已存在");
            }
            //新增
            supJob.setId(Tools.genId()+"");
            if(supJob.getCreationTime() == null){
                supJob.setCreationTime(new Date());
            }

            supJob.setStatus("0");
            supJobServiceImpl.save(supJob);
            return ResultUtil.successToObject("定时任务保存成功",supJob);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("定时任务保存失败"+e.getMessage());
        }
    }

    @ApiOperation("修改定时任务")
    @RequestMapping("/edit")
    public Result edit(@ApiParam(value = "定时任务对象",required = true )  SupJob supJob){
        try {
            //先删除 再新增
            String id = supJob.getId();
            if(StringUtil.isBlank(id)){
                return ResultUtil.error("定时任务id不能为空");
            }
            supJobServiceImpl.removeById(id);
            supJobServiceImpl.deleteJob(id);
            supJob.setLastModificationTime(new Date());
            Result result = this.save(supJob);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("定时任务修改失败"+e.getMessage());
        }
    }

    @ApiOperation("获取定时任务详细信息")
    @RequestMapping("/info/{jobId}")
    public Result info(@ApiParam(value = "定时任务id",required = true  )@PathVariable("jobId") String jobId){
        SupJob job = supJobServiceImpl.getById(jobId);
        if(job==null){
            return ResultUtil.error("定时任务id 不存在"+jobId);
        }
        return ResultUtil.successToObject(job);
    }

    @ApiOperation("删除定时任务")
    @RequestMapping("/deleteById/{jobId}")
    public Result deleteById(@ApiParam(value = "定时任务id",required = true ) @PathVariable("jobId") String jobId){
        try {
            supJobServiceImpl.removeById(jobId);
            supJobServiceImpl.deleteJob(jobId);
            return ResultUtil.success("删除定时任务成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("删除定时任务失败"+e.getMessage());
        }
    }

    @ApiOperation("修改定时任务状态")
    @RequestMapping("/updateStatus/{jobId}")
    public Result updateStatus(@ApiParam(value = "定时任务id",required = true) @PathVariable("jobId") String jobId){
        try {
            SupJob job = supJobServiceImpl.getById(jobId);
            if(job==null){
                return ResultUtil.error("定时任务id 不存在"+jobId);
            }
            String status = job.getStatus();
            if("0".equals(status)){
                //准备启动
                supJobServiceImpl.insertJob(job);
                job.setStatus("1");
            }
            if("1".equals(status)){
                //准备关闭
                supJobServiceImpl.deleteJob(jobId);
                job.setStatus("0");
            }
            job.setLastModificationTime(new Date());
            supJobServiceImpl.updateById(job);
            return ResultUtil.success("修改定时任务状态成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("修改定时任务状态失败"+e.getMessage());
        }
    }
}

