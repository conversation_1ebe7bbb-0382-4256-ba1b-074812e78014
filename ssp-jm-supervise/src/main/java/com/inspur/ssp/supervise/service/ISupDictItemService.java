package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDictItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.SupDictVo;
import org.jangod.iweb.core.bean.Result;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2020-05-18
 */
public interface ISupDictItemService extends IService<SupDictItem> {

    List<SupDictVo> getDictItem(String code) throws Exception;

}
