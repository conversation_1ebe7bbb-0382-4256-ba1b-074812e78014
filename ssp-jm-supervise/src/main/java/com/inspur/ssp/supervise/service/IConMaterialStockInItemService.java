package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ConMaterialStockInItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupStockInItem;
import org.jangod.iweb.core.bean.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
public interface IConMaterialStockInItemService extends IService<ConMaterialStockInItem> {

    Result saveStockVoucher(ConMaterialStockInItem conMaterialStockInItem) ;

    List<Map<String, Object>> getConMaterialStockInItemList(Map<String, Object> params);

}
