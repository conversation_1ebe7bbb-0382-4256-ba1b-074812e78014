package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryOrderExportDto;
import com.inspur.ssp.supervise.bean.dto.purchase.SupOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2020-04-30
 */
public interface ISupOrderItemService extends IService<SupOrderItem> {

    List<Map<String, Object>> getPurchaseAmountBySource(Map<String,Object> params);

    List<Map<String, Object>> getAmountByHospital(Map<String,Object> params);

    List<Map<String, Object>> getSourceAmountByHospital(Map<String,Object> params);

    List<Map<String, Object>> getSourceAmountByCity(Map<String,Object> params);

    List<Map<String, Object>> getOrderItemList(Map<String,Object> params);

    List<SupOrderItemExportExcel> getOrderItemListToExcel(Map<String,Object> params);

    List<Map<String, Object>> getDrugAmountByMonth(Map<String,Object> params);

    List<Map<String, Object>> getDrugAmountByQuarter(Map<String,Object> params);

    List<Map<String, Object>> getDrugAmountByYear(Map<String,Object> params);

    String convertOrderStatus(String type);

    List<Map<String,Object>> getOrderItemPurchase(Map<String,Object> params);

    /**
     * 获取超时未配送订单设置预警
     * @param params
     * @return
     */
    List<SupOrderItem> getOverDeliveryOrderItem(Map<String,Object> params);

    /**
     * 获取超时未配送订单列表
     * @param params
     * @return
     */
    List<Map<String, Object>> getOverDeliveryList(Map<String,Object> params);

    /**
     * 将历史绑定错误的药品归集到新的药品中
     */
    void flushCountryOrderItem(Map<String, Object> params);

    /**
     * 将订单刷新成集采
     * */
    void updateCountryByDetail(Map<String, Object> params);

    List<Map<String, Object>> suporderItemList();

    List<SupOrderItem> disposePartialStorage(String orderCode);

    /**
     * 根据busicode获取订单明细中的合同编号
     * @param busiCode
     * @return
     */
    SupOrderItem getOrderItemByBusiCode(String busiCode);
}
