package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.bsp.entity.*;
import com.inspur.ssp.bsp.service.*;
import com.inspur.ssp.supervise.bean.dto.SupOfficialDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.TemplateUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultObject;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.jangod.iweb.util.Tools.genId;


/**
 * <p>
 * 公文内容 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-20
 */
@RestController
@RequestMapping("/supervise/supOfficial")
public class SupOfficialController extends AbstractController {
    @Autowired
    private ISupOfficialService supOfficialServiceImpl;
    @Autowired
    private ISupOfficialReceiveService supOfficialReceiveServiceImpl;
    @Autowired
    private IPubUserService pubUserServiceImpl;
    @Autowired
    private ISupHospitalService supHospitalServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupOfficialHospitalNoticeService supOfficialHospitalNoticeServiceImpl;
    @Autowired
    private IPubSmsTemplateService pubSmsTemplateServiceImpl;
    @Autowired
    private IPubSmsService pubSmsServiceImpl;

    @ApiOperation(value = "获取公文列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "公文标题", required = false, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "公文状态", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String userId="";
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            userId=user.getId();
        }
        params.put("startSendTime",params.get("sendTime[0]"));
        params.put("endSendTime",params.get("sendTime[1]"));

        params.put("sendUerId",userId);
        PageHelper.startPage(page, limit);
        List<SupOfficial> list = supOfficialServiceImpl.getOfficialList(params);
        PageList<SupOfficial> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation("查看智能分类结果")
    @PostMapping("/showFileResult")
    public Result showFileResult(@RequestBody SupOfficialDto supFileDto) {
        List<Map<String,Object>> fileList = new ArrayList<>();
        List<Map<String,Object>> fileCommonList = new ArrayList<>();
        List<Map<String, Object>> docList = supFileDto.getDocList();
        //查找医院
        List<SupHospital> hospitals = supHospitalServiceImpl.list();
        //对所有文件进行遍历-公共文件
        for (Map<String, Object> docMap : docList) {
            String docName = ((String) docMap.get("name")).substring(0, ((String) docMap.get("name")).lastIndexOf("."));
            List<SupHospital> supHospitals = hospitals.stream().filter(h -> (docName.contains(h.getName()))).collect(Collectors.toList());
            if(supHospitals.size()==0){
                fileCommonList.add(docMap);
            }
        }
        //对医院遍历
        for (SupHospital hospital : hospitals) {
            List<Map<String, Object>> files = docList.stream().filter(d -> (((String) d.get("name")).substring (0,((String) d.get("name")).lastIndexOf("."))).contains(hospital.getName())).collect(Collectors.toList());
            if(fileCommonList.size()>0){
                files.addAll(fileCommonList);
            }
            if(files.size()>0){
                Map<String,Object> map=new HashMap<String,Object>();
                map.put("hospitalId",hospital.getId());
                List<SupHospitalUser> supHospitalUsers = supHospitalUserServiceImpl.list(new QueryWrapper<SupHospitalUser>().eq("HOSPITAL_ID", hospital.getId()));
                List<String> receiveUserIds =new ArrayList<>();
                List<String> receiveUserNames =new ArrayList<>();
                for (SupHospitalUser supHospitalUser : supHospitalUsers) {
                    String id = supHospitalUser.getUserId();
                    PubUser pubUser = pubUserServiceImpl.getById(id);
                    if(!Objects.isNull(pubUser)&&"1".equals(pubUser.getStatus())){
                        receiveUserIds.add(pubUser.getId());
                        receiveUserNames.add(pubUser.getName());
                    }
                }
                map.put("receiveUserIds",receiveUserIds);
                map.put("receiveUserNames", StringUtils.join(receiveUserNames, ","));
                map.put("hospitalName",hospital.getName());
                map.put("docInfo",files);
                fileList.add(map);
            }
        }
        return ResultUtil.successToList(fileList);
    }

    @ApiOperation("智能暂存/发送公文")
    @PostMapping("/smartPost")
    public Result smartPost(@RequestBody SupOfficialDto supOfficialDto) {
        IUser user = (IUser) this.getCurrentUser();
        String userId="";
        String userName="";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            userId=user.getId();
            userName=user.getName();
        }
        String status = supOfficialDto.getStatus();
        if(StringUtil.isBlank(status)){
            return ResultUtil.error("操作类型不能为空");
        }

        List<Map<String, Object>> docList = supOfficialDto.getDocList();

        for (Map<String, Object> map : docList) {
            //获取接收者
            List<String> receiveUserIds = (List<String>) map.get("receiveUserIds");
            //获取发送附件
            String docInfo = (String) map.get("docInfo");
            if(receiveUserIds.size()>0){
                SupOfficial supOfficial = BeanUtil.copyProperties(supOfficialDto,SupOfficial.class);
                String  officialId =Tools.genId() + "";
                supOfficial.setId(officialId);
                supOfficial.setDocInfo(docInfo);
                supOfficial.setLastModifitionTime(new Date());
                supOfficial.setLastModifitor(userId);
                supOfficial.setStatus(status);
                supOfficial.setNum(0);
                supOfficial.setSendUserId(userId);
                supOfficial.setSendUserName(userName);
                if(status.equals("1")){
                    supOfficial.setSendTime(new Date());
                }
                supOfficial.setCreationTime(new Date());
                supOfficial.setCreator(userId);
                supOfficialServiceImpl.save(supOfficial);

                List<SupOfficialReceive> supOfficialReceives = new ArrayList<>();
                for (String receiveUserId : receiveUserIds) {
                    SupOfficialReceive supOfficialReceive = new SupOfficialReceive();
                    supOfficialReceive.setId(genId()+"");
                    supOfficialReceive.setIsSee("0");
                    supOfficialReceive.setCreationTime(new Date());
                    supOfficialReceive.setStatus(status);
                    supOfficialReceive.setCreator(userId);
                    supOfficialReceive.setUserId(receiveUserId);
                    supOfficialReceive.setOfficialId(officialId);
                    supOfficialReceives.add(supOfficialReceive);
                }
                //批量插入
                if(supOfficialReceives.size()>0){
                    supOfficialReceiveServiceImpl.saveBatch(supOfficialReceives,supOfficialReceives.size());
                }
            }

        }
        return ResultUtil.success("发送成功");
    }

    @ApiOperation("保存/修改普通公文")
    @PostMapping("/edit")
    public Result edit(@RequestBody SupOfficialDto supOfficialDto) {
        IUser user = (IUser) this.getCurrentUser();
        String userId="";
        String userName="";
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            userId=user.getId();
            userName=user.getName();
        }
        String status = supOfficialDto.getStatus();
        if(StringUtil.isBlank(status)){
            return ResultUtil.error("操作类型不能为空");
        }

        List<String> receiveUserIds = supOfficialDto.getReceiveUserIds();
        if (receiveUserIds.size()==0&&"1".equals(status)) {
            return ResultUtil.error("请选择收件人");
        }
        String officialId = supOfficialDto.getId();
        if(StringUtil.isBlank(officialId)){
            officialId =Tools.genId() + "";
        }else{
            //清除之前接收缓存
            supOfficialReceiveServiceImpl.remove(new QueryWrapper<SupOfficialReceive>().eq("official_id",officialId));

        }
        SupOfficial supOfficial = BeanUtil.copyProperties(supOfficialDto,SupOfficial.class);
        supOfficial.setLastModifitionTime(new Date());
        supOfficial.setLastModifitor(userId);
        supOfficial.setStatus(status);
        supOfficial.setNum(0);
        supOfficial.setSendUserId(userId);
        supOfficial.setSendUserName(userName);
        if(status.equals("1")){
            supOfficial.setSendTime(new Date());
        }
        List<SupOfficialReceive> supOfficialReceives = new ArrayList<>();
        for (String receiveUserId : receiveUserIds) {
            SupOfficialReceive supOfficialReceive = new SupOfficialReceive();
            supOfficialReceive.setId(genId()+"");
            supOfficialReceive.setIsSee("0");
            supOfficialReceive.setCreationTime(new Date());
            supOfficialReceive.setStatus(status);
            supOfficialReceive.setCreator(userId);
            supOfficialReceive.setUserId(receiveUserId);
            supOfficialReceive.setOfficialId(officialId);
            supOfficialReceives.add(supOfficialReceive);
        }

        if(StringUtil.isNotEmpty(supOfficialDto.getId())){
            supOfficialServiceImpl.updateById(supOfficial);
        }else{
            supOfficial.setCreationTime(new Date());
            supOfficial.setCreator(userId);
            supOfficial.setId(officialId);
            supOfficialServiceImpl.save(supOfficial);
        }
        //批量插入
        if(supOfficialReceives.size()>0){
            supOfficialReceiveServiceImpl.saveBatch(supOfficialReceives,supOfficialReceives.size());
        }
        return ResultUtil.successToObject(supOfficial);
    }

    @ApiOperation(value = "公文详细信息")
    @RequestMapping("/info/{supOfficialId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isUpdateSee", value = "是否更新查阅次数", required = false, dataType = "String"),
    })
    public Result info(@ApiParam("公文id") @PathVariable("supOfficialId") String supOfficialId) {
        String isUpdateSee = this.getParameter("isUpdateSee");
        SupOfficial supOfficial = supOfficialServiceImpl.getById(supOfficialId);
        SupOfficialDto supOfficialDto = BeanUtil.copyProperties(supOfficial,SupOfficialDto.class);
        List<SupOfficialReceive> supOfficialReceives = supOfficialReceiveServiceImpl.list(new QueryWrapper<SupOfficialReceive>().eq("official_id", supOfficial.getId()));
        List<String> receiveUserIds=new ArrayList<>();
        List<String> receiveUserNames=new ArrayList<>();
        if(supOfficialReceives.size()>0){
            List<PubUser> list = pubUserServiceImpl.list();
            for (SupOfficialReceive supOfficialReceive : supOfficialReceives) {
                String userId = supOfficialReceive.getUserId();
                receiveUserIds.add(userId);
                Optional<PubUser> pubUser = list.stream().filter(item -> item.getId().equals(userId)).findFirst();
                if (pubUser.isPresent()) {
                    // 存在
                    PubUser receiveUser = pubUser.get();
                    String name = receiveUser.getName();
                    receiveUserNames.add(name);
                }
            }
        }
        supOfficialDto.setReceiveUserIds(receiveUserIds);
        supOfficialDto.setReceiveUserNames(String.join("、",receiveUserNames));
        if (supOfficial == null) {
            return ResultUtil.error("公文ID不存在");
        }
        if("1".equals(isUpdateSee)){
            //更新查阅次数
            supOfficial.setNum(supOfficial.getNum()+1);
            supOfficialServiceImpl.updateById(supOfficial);
        }
        return ResultUtil.successToObject(supOfficialDto);
    }

    @ApiOperation(value = "删除公文")
    @RequestMapping("/delete/{supOfficialId}")
    public Result deleteById(@ApiParam("公文ID") @PathVariable("supOfficialId") String supOfficialId) {
        SupOfficial supOfficial = supOfficialServiceImpl.getById(supOfficialId);
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            ResultUtil.error("用户登录超时，请重新登录");
        }else{
            supOfficial.setLastModifitor(currentUser.getId());
        }
        supOfficial.setStatus("-1");
        supOfficial.setLastModifitionTime(new Date());
        supOfficialServiceImpl.updateById(supOfficial);
        return ResultUtil.success("删除成功");
    }
    @ApiOperation(value = "撤回公文")
    @RequestMapping("/revert/{supOfficialId}")
    public Result revertById(@ApiParam("公文ID") @PathVariable("supOfficialId") String supOfficialId) {
        SupOfficial supOfficial = supOfficialServiceImpl.getById(supOfficialId);
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            ResultUtil.error("用户登录超时，请重新登录");
        }else{
            supOfficial.setLastModifitor(currentUser.getId());
        }
        if(supOfficial.getNum()==0){
            supOfficial.setStatus("0");
            supOfficial.setLastModifitionTime(new Date());
            String officialId = supOfficial.getId();
            SupOfficialReceive supOfficialReceive = new SupOfficialReceive();
            supOfficialReceive.setStatus("0");
            supOfficialReceive.setLastModifitionTime(new Date());
            supOfficialReceiveServiceImpl.update(supOfficialReceive,new UpdateWrapper<SupOfficialReceive>().eq("OFFICIAL_ID",officialId));
            supOfficialServiceImpl.updateById(supOfficial);
            return ResultUtil.success("撤回成功");
        }else{
            return ResultUtil.success("用户已查阅，无法撤回");
        }

    }

    @ApiOperation(value = "公文通知，根据配置自动发送公文")
    @PostMapping("/sendTemplate/{officialTemplateCode}")
    public Result officialNotice(@ApiParam("公文模板编码") @PathVariable("officialTemplateCode") String officialTemplateCode, @RequestBody Map<String,Object> data)  {

        IUser user = this.getCurrentUser();
        if(user == null) {
            return  ResultUtil.error("用户登录超时，请重新登录");
        }else{
            PubUser pubUser = new PubUser();
            pubUser.setId(user.getId());
            pubUser.setName(user.getName());
            data.put("user",pubUser);
            data.put("officialTemplateCode",officialTemplateCode);
            Result result = supOfficialServiceImpl.officialNotice(data);
            return result;
        }
    }


    @ApiOperation(value = "超时未支付短信提醒")
    @PostMapping("/sendSmsTemplate/{smsTemplateCode}")
    public Result smsNotice(@ApiParam("公文模板编码") @PathVariable("smsTemplateCode") String smsTemplateCode, @RequestBody Map<String,Object> data) throws IOException {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return  ResultUtil.error("用户登录超时，请重新登录");
        }
        PubUser pubUser = pubUserServiceImpl.getById(user.getId());
        data.put("user", pubUser);
        data.put("smsTemplateCode",smsTemplateCode);
        return supOfficialServiceImpl.sendSms(data);

        /*QueryWrapper<PubSmsTemplate> templateQueryWrapper = new QueryWrapper<>();
        templateQueryWrapper.lambda().eq(PubSmsTemplate::getType, smsTemplateCode);
        PubSmsTemplate pubSmsTemplate = pubSmsTemplateServiceImpl.getOne(templateQueryWrapper);
        if(Objects.nonNull(pubSmsTemplate)){
            IUser user = this.getCurrentUser();
            String hospitalId = (String)data.get("hospitalId");
            if(StringUtil.isEmpty(hospitalId)){
                return ResultUtil.error("请选择医院");
            }
            String busiId = (String)data.get("busiId");

            SupHospital supHospital = supHospitalServiceImpl.getById(hospitalId);
            data.put("hospitalName", supHospital.getName());

            String content = pubSmsTemplate.getContent();
            String newContent = (String)TemplateUtil.expTemplateProcess(content, data, null);
            PubSms pubSms = new PubSms();
            pubSms.setBusiId(busiId);
            pubSms.setContent(newContent);
            pubSms.setCreationTime(new Date());
            pubSms.setStatus(Constant.SMS_STATUS_NO);//待发送
            pubSms.setTemplateId(pubSmsTemplate.getId());


            String hospitalPhone = supHospital.getPhone();
            Pattern pattern = Pattern.compile("^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16([5,6])|(17[0-8])|(18[0-9]))|(19[1,8,9]))\\d{8}$");
            boolean matches = pattern.matcher(hospitalPhone).matches();
            if(hospitalPhone.length()==11&&matches){
                pubSms.setName(supHospital.getName());
                pubSms.setPhone(supHospital.getPhone());
                pubSms.setId(Tools.genId() + "");
                pubSmsServiceImpl.save(pubSms);
            }else{
                return ResultUtil.errorToObject("该医疗机构手机号设置不合理");
            }*/


           /* List<SupHospitalUser> hospitalUser = supHospitalUserServiceImpl.list(new QueryWrapper<SupHospitalUser>().eq("HOSPITAL_ID", hospitalId));
            if(hospitalUser.size()>0){
                for (SupHospitalUser supHospitalUser : hospitalUser) {
                    String userId = supHospitalUser.getUserId();
                    PubUser pubUser  = pubUserServiceImpl.getById(userId);
                    String phone = pubUser.getPhone();
                    if(StringUtil.isNotBlank(phone)){
                        pubSms.setName(pubUser.getName());
                        pubSms.setPhone(pubUser.getPhone());
                        pubSms.setId(Tools.genId() + "");
                        pubSmsServiceImpl.save(pubSms);
                    }
                }
            }else{
                return ResultUtil.errorToObject("该医疗机构暂无关联负责人");
            }*/
        /*}else{
            return ResultUtil.error("未设置短信发送模板");
        }
        return ResultUtil.success();*/
    }


    @ApiOperation("查看公文通知已经发送了多少次")
    @PostMapping("/getSendNum")
    public ResultObject getNoticeNum(String busiId, String hospitalId) {

        SupOfficialHospitalNotice officialHospitalNotice = supOfficialHospitalNoticeServiceImpl.getOne(new QueryWrapper<SupOfficialHospitalNotice>().lambda().eq(SupOfficialHospitalNotice::getBusiId,busiId)
                .eq(SupOfficialHospitalNotice::getHospitalId,hospitalId),false);
        if(Objects.nonNull(officialHospitalNotice)){
            return ResultUtil.successToObject(officialHospitalNotice.getNoticeNum());
        }
        return ResultUtil.successToObject(1);
    }

    @ApiOperation(value = "获取短信日志列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "接收人", required = false, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "手机号", required = false, dataType = "String"),
    })
    @RequestMapping("/smsLogList")
    public Result smsLogList(@RequestParam int page, @RequestParam int limit, @RequestParam String name,
                             @RequestParam String status,@RequestParam String phone) {
        Map<String, Object> params = this.getRequestParams();
        String userId="";
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        String beginCreationTime=(String) params.get("creationTime[0]");
        String endCreationTime=(String) params.get("creationTime[1]");

        LambdaQueryWrapper<PubSms> queryWrapper = new QueryWrapper<PubSms>().lambda().like(StringUtil.isNotEmpty(name), PubSms::getName, name)
                .eq(StringUtil.isNotEmpty(status), PubSms::getStatus, status).eq(StringUtil.isNotEmpty(phone), PubSms::getPhone, phone)
                .between(StringUtil.isNotEmpty(beginCreationTime) && StringUtil.isNotEmpty(endCreationTime), PubSms::getCreationTime, beginCreationTime, endCreationTime)
                .orderByDesc(PubSms::getId);


        PageHelper.startPage(page, limit);
        List<PubSms> list = pubSmsServiceImpl.list(queryWrapper);
        PageList<PubSms> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

}

