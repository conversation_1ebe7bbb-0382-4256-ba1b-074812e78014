package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.supervise.bean.dto.SupOrderContrastPriceDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderItemDto;
import com.inspur.ssp.supervise.bean.dto.purchase.*;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.bean.vo.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.ebus.HttpClientUtil;
import com.inspur.ssp.supervise.utils.excel.ExcelUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.annotation.AepSecurity;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-04-30
 */
@RestController
@RequestMapping("/supervise/supOrder")
public class SupOrderController extends AbstractController {
    @Autowired
    private ISupOrderService supOrderServiceImpl;

    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;

    @Autowired
    private ISupHospitalService supHospitalServiceImpl;

    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;

    @Autowired
    private ISupDrugCompanyService supDrugCompanyServiceImpl;

    @Autowired
    private ISupOrderPayService supOrderPayServiceImpl;

    @Autowired
    private ISupOrderStockService supOrderStockServiceImpl;
    @Autowired
    private ISupDictItemService supDictItemServiceImpl;
    @Autowired
    private ISupFileService supFileServiceImpl;
    @Autowired
    private ICdBatchLogService cdBatchLogServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @ApiOperation(value = "获取药品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/drugList")
    public Result drugList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        PageHelper.startPage(page, limit);
        List<SupOrderDrugVo> list = supOrderServiceImpl.selectOrderDrugList(params);
        PageList<SupOrderDrugVo> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("比价操作，获取最低的价格")
    @PostMapping("/contrastPrice")
    public Result contrastPrice(@RequestBody SupOrderContrastPriceDto orderContrastPriceDto) {
        SupOrderDrugSelectVo supOrderDrugSelectVo = supOrderServiceImpl.contrastPrice(orderContrastPriceDto);
        if (supOrderDrugSelectVo == null) {
            return ResultUtil.error("该药品未设置价格，请联系管理员设置价格。");
        }
        return ResultUtil.successToObject(supOrderDrugSelectVo);
    }

    @ApiOperation("获取所有价格的药品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @PostMapping("/selectOtherDrug")
    public Result selectOtherDrug(@RequestParam int page, @RequestParam int limit, @RequestBody SupOrderContrastPriceDto orderContrastPriceDto) {
        PageHelper.startPage(page, limit);
        List<SupOrderDrugSelectVo> supOrderDrugSelectVos = supOrderServiceImpl.selectOtherDrug(orderContrastPriceDto);
        PageList<SupOrderDrugSelectVo> selectPage = new PageList<>(supOrderDrugSelectVos);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("保存订单")
    @PostMapping("/save")
    public Result save(@RequestBody JSONObject map) throws Exception {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        SupHospital hospital = BeanUtil.convert(map.getJSONObject("hospital"), SupHospital.class);
        if (hospital == null) {
            return ResultUtil.error("用户未绑定医院，请联系管理员绑定。");
        }
        JSONObject source = map.getJSONObject("source");
        if (source == null) {
            return ResultUtil.error("未检测到订单药品，请选择药品下单。");
        }
        for (Object obj : source.values()) {
            if (obj == null) {
                return ResultUtil.error("未检测到订单药品，请选择药品下单。");
            }
            SupOrderDataDto supOrderDataDto = BeanUtil.convert((JSONObject) JSONObject.toJSON(obj), SupOrderDataDto.class);
            supOrderDataDto.setHospital(hospital);

            List<SupOrderItemDto> orderItem = supOrderDataDto.getOrderItem();
            if (CollectionUtils.isEmpty(orderItem)) {
                return ResultUtil.error("未检测到订单药品，请选择药品下单。");
            }
            BigDecimal totalPrice = new BigDecimal("0");
            for (SupOrderItemDto orderItemDto : orderItem) {
                BigDecimal itemPrice = orderItemDto.getUnitPrice().multiply(orderItemDto.getAmount());
                if (itemPrice.compareTo(orderItemDto.getItemPrice()) != 0) {
                    return ResultUtil.error("【" + orderItemDto.getCatalogName() + "】存在恶意篡改总价行为，无法提交");
                }
                if (StringUtil.isNotEmpty(orderItemDto.getSystemContrast())) {
                    if (orderItemDto.getSystemContrast().equals("0")) {
                        if (StringUtil.isEmpty(orderItemDto.getReason())) {
                            return ResultUtil.error("【" + orderItemDto.getCatalogName() + "】为非系统自动筛选结果，必须填写原因。");
                        }
                    }
                }
                totalPrice = totalPrice.add(itemPrice);
            }
            if (totalPrice.compareTo(supOrderDataDto.getTotalPrice()) != 0) {
                return ResultUtil.error("存在恶意篡改合计价格行为，无法提交");
            }
            PubUser pubUser = new PubUser();
            pubUser.setId(user.getId());
            pubUser.setName(user.getName());
            supOrderServiceImpl.save(pubUser, supOrderDataDto);
        }
        return ResultUtil.success();
    }


    @ApiOperation("保存医院线下订单")
    @PostMapping("/saveHospitalOrder")
    public Result saveHospitalOrder(@RequestBody JSONObject map) throws Exception {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        SupHospital hospital = BeanUtil.convert(map.getJSONObject("hospital"), SupHospital.class);
        if (hospital == null) {
            return ResultUtil.error("用户未绑定医院，请联系管理员绑定。");
        }
        SupOrderDataDto supOrderDataDto = BeanUtil.convert((JSONObject) JSONObject.toJSON(map), SupOrderDataDto.class);
        BigDecimal totalPrice = new BigDecimal("0");

        List<SupOrderItemDto> orderItem = supOrderDataDto.getOrderItem();
        if (CollectionUtils.isEmpty(orderItem)) {
            return ResultUtil.error("未检测到订单药品，请选择药品下单。");
        }
        for (SupOrderItemDto orderItemDto : orderItem) {
            BigDecimal itemPrice = orderItemDto.getUnitPrice().multiply(orderItemDto.getAmount());
            if (itemPrice.compareTo(orderItemDto.getItemPrice()) != 0) {
                return ResultUtil.error("【" + orderItemDto.getCatalogName() + "】存在恶意篡改总价行为，无法提交");
            }
            totalPrice = totalPrice.add(itemPrice);
        }
        String orderNum = supOrderDataDto.getOrderNum();
        if(StringUtil.isNotBlank(orderNum)){
            SupOrder supOrder = supOrderServiceImpl.getOne(new LambdaQueryWrapper<SupOrder>().eq(SupOrder::getOrderNum, orderNum));
            if(Objects.nonNull(supOrder)){
                return ResultUtil.error("订单号已存在");
            }
        }
        PubUser pubUser = new PubUser();
        pubUser.setId(user.getId());
        pubUser.setName(user.getName());
        supOrderServiceImpl.save(pubUser, supOrderDataDto);
        return ResultUtil.success();
    }



    @ApiOperation(value = "获取订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<SupOrder> queryWrapper = new QueryWrapper<>();
            String beginOrderTime=(String) params.get("submitTime[0]");
            String endOrderTime=(String) params.get("submitTime[1]");
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                queryWrapper.between("SUBMIT_TIME", beginTime, endTime);
            }
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    queryWrapper.lambda().eq(SupOrder::getHospitalId, hospitalUser.getHospitalId());
                }
            }
            String orderNum = (String) params.get("orderNum");
            String hospitalName = (String) params.get("hospitalName");
            if (StringUtil.isNotBlank(hospitalName)) {
                queryWrapper.lambda().like(SupOrder::getHospitalName, hospitalName);
            }
            String source = (String) params.get("source");
            String warning = (String) params.get("warning");
            String orderStatus = (String) params.get("orderStatus");
            String stockStatus = (String) params.get("stockStatus");
            String payStatus = (String) params.get("payStatus");
            String hospitalId = (String) params.get("hospitalId");
            String type = (String) params.get("type");

            String regionId = (String) params.get("regionId");
            if (StringUtil.isNotBlank(regionId)) {
                queryWrapper.lambda().eq(SupOrder::getRegionId, regionId);
            }
            if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
                queryWrapper.lambda().eq(SupOrder::getRegionId,user.getOrgCode());
            }

            if (StringUtil.isNotBlank(hospitalId)) {
                queryWrapper.lambda().eq(SupOrder::getHospitalId, hospitalId);
            }
            if(StringUtil.isNotBlank(type)){
                if("1".equals(type)){
                    queryWrapper.lambda().eq(SupOrder::getWarning, type);
                }
                if("2".equals(type)){
                    queryWrapper.lambda().ne(SupOrder::getWarning, "1");
                }
            }


            if (StringUtil.isNotBlank(stockStatus)) {
                queryWrapper.lambda().eq(SupOrder::getStockStatus, stockStatus);
            }
            if (StringUtil.isNotBlank(source)) {
                queryWrapper.lambda().eq(SupOrder::getSource, source);
            }
            if (StringUtil.isNotBlank(payStatus)) {
                queryWrapper.lambda().eq(SupOrder::getPayStatus, payStatus);
            }

            if (StringUtil.isNotEmpty(orderNum)) {
                queryWrapper.lambda().likeRight(SupOrder::getOrderNum, orderNum);
            }
            if (StringUtil.isNotEmpty(warning)) {
                if ("1".equals(warning)) {
                    queryWrapper.lambda().eq(SupOrder::getWarning, warning);
                } else if(!"0".equals(warning) && !"-1".equals(warning)) {
                    queryWrapper.lambda().like(SupOrder::getWarning, warning);
                }
                //异常的,前端传值
                if ("-1".equals(warning)) {
                    queryWrapper.lambda().ne(SupOrder::getWarning, "1");
                }

            }
            if (StringUtil.isNotEmpty(orderStatus)) {
                queryWrapper.lambda().eq(SupOrder::getOrderStatus, orderStatus);
            }
            queryWrapper.lambda().orderByDesc(SupOrder::getSubmitTime).orderByAsc(SupOrder::getHospitalName);
            PageHelper.startPage(page, limit);
            List<SupOrder> list = supOrderServiceImpl.list(queryWrapper);
            PageList<SupOrder> selectPage = new PageList<>(list);
            return ResultUtil.successToList(selectPage);
        } catch (ParseException e) {
            e.printStackTrace();
            return ResultUtil.error("订单查询失败");
        }
    }


    @ApiOperation(value = "订单详情")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String orderId) {
        try {
            SupOrder order = supOrderServiceImpl.getById(orderId);
            if (order == null) {
                return ResultUtil.error("无此订单，请联系管理员");
            }
            SupOrderVo data = BeanUtil.copyProperties(order, SupOrderVo.class);
            String warning = data.getWarning();
            String[] split = warning.split(",");
            List<SupDictVo> warningData = supDictItemServiceImpl.getDictItem("WARNING");
            List<String> dictLabel = new ArrayList<>();
            for (String s : split) {
                Optional<SupDictVo> supDictVoData = warningData.stream().filter(item -> item.getValue().equals(s)).findFirst();
                if (supDictVoData.isPresent()) {
                    // 存在
                    SupDictVo supDictVo = supDictVoData.get();
                    String label = supDictVo.getLabel();
                    dictLabel.add(label);
                }
            }
            data.setWarning(String.join(" | ", dictLabel));
            QueryWrapper<SupOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SupOrderItem::getOrderId, orderId);
            List<SupOrderItemVo> orderItem = new ArrayList<>();
            List<SupOrderItem> items = supOrderItemServiceImpl.list(queryWrapper);
            for (SupOrderItem item : items) {
                SupOrderItemVo supOrderItemVo = BeanUtil.copyProperties(item, SupOrderItemVo.class);
                String detailId = item.getDetailId();
                SupDrugDetail supDrugDetail = supDrugDetailServiceImpl.getById(detailId);
                supOrderItemVo.setDosageForm(supDrugDetail.getDosageForm());
                supOrderItemVo.setPackingSpecs(supDrugDetail.getPackingSpecs());
                supOrderItemVo.setSpecs(supDrugDetail.getSpecs());
//                supOrderItemVo.setCountry(supDrugDetail.getCountry());
                supOrderItemVo.setCountry(item.getCountryTag());//是否集采用订单明细里的，因为药品表会变化。
                supOrderItemVo.setDrugCompanyId(supDrugDetail.getDrugCompanyId());
                SupDrugCompany supDrugCompany = supDrugCompanyServiceImpl.getById(supDrugDetail.getDrugCompanyId());
                if (Objects.nonNull(supDrugCompany)) {
                    supOrderItemVo.setDrugCompanyName(supDrugCompany.getName());
                }
                orderItem.add(supOrderItemVo);
            }
            SupHospital hospital = supHospitalServiceImpl.getById(order.getHospitalId());
            QueryWrapper<SupOrderPay> payQueryWrapper = new QueryWrapper<>();
            payQueryWrapper.lambda().eq(SupOrderPay::getOrderId, orderId)
                    .orderByDesc(SupOrderPay::getPayTime);
            List<SupOrderPay> orderPays = supOrderPayServiceImpl.list(payQueryWrapper);
            SupOrderDetailVo orderDetailVo = new SupOrderDetailVo();
            orderDetailVo.setData(data);
            orderDetailVo.setOrderItem(orderItem);
            orderDetailVo.setHospital(hospital);
            if (CollectionUtils.isNotEmpty(orderPays)) {
                orderDetailVo.setOrderPays(orderPays);
            }
            return ResultUtil.successToObject(orderDetailVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }

    @ApiOperation("验证订单支付状态")
    @PostMapping("/checkPayStatus/{orderId}")
    public Result checkPayStatus(@PathVariable("orderId") String orderId) {
        SupOrder order = supOrderServiceImpl.getById(orderId);
        if (order == null) {
            return ResultUtil.error("无此订单，请联系管理员");
        }
        QueryWrapper<SupOrderPay> payQueryWrapper = new QueryWrapper<>();
        payQueryWrapper.lambda().eq(SupOrderPay::getOrderId, orderId);
        int count = supOrderPayServiceImpl.count(payQueryWrapper);
        if (count == 0) {
            return ResultUtil.error("此订单还未上传支付凭证，无法变更。");
        }
      /*  payQueryWrapper.select("COALESCE(sum(pay_price),0.0000) as payTotalPrice");
        Map<String, Object> map = supOrderPayServiceImpl.getMap(payQueryWrapper);
        BigDecimal payTotalPrice = (BigDecimal) map.get("payTotalPrice");
        BigDecimal totalPrice = order.getTotalPrice();*/
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("status", "1");
       /* if (payTotalPrice.compareTo(totalPrice) != 0) {
            reMap.put("status", "0");
            reMap.put("msg", "实际支付总金额不等于订单金额");
        }*/

        return ResultUtil.successToObject(reMap);
    }


    @ApiOperation("验证订单入库状态")
    @PostMapping("/checkStockStatus/{orderId}")
    public Result checkStockStatus(@PathVariable("orderId") String orderId) {
        SupOrder order = supOrderServiceImpl.getById(orderId);
        if (order == null) {
            return ResultUtil.error("无此订单，请联系管理员");
        }
        //查询订单是否上传过入库凭证
        QueryWrapper<SupOrderStock> stockQueryWrapper = new QueryWrapper<>();
        stockQueryWrapper.lambda().eq(SupOrderStock::getOrderId, orderId);
        int count = supOrderStockServiceImpl.count(stockQueryWrapper);
        if (count == 0) {
            return ResultUtil.error("此订单还未上传入库凭证，无法变更。");
        }
        //查询每个订单项的凭证库存量 是否合理
        QueryWrapper<SupOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupOrderItem::getOrderId, orderId);
        List<SupOrderItem> orderItems = supOrderItemServiceImpl.list(queryWrapper);
        Map<String, Object> reMap = new HashMap<>();
        String msg = "";
        reMap.put("status", "1");
        for (SupOrderItem orderItem : orderItems) {
           /* BigDecimal amount = orderItem.getAmount();*/
            Map<String, Object> map = supOrderStockServiceImpl.getMap(new QueryWrapper<SupOrderStock>().select("sum(item_stock) as stockTotalCount").eq("ORDER_ITEM_ID", orderItem.getId()));
           /* if (map != null) {
                BigDecimal stockTotalCount = (BigDecimal) map.get("stockTotalCount");
                if (stockTotalCount.compareTo(amount) != 0) {
                    reMap.put("status", "0");
                    msg = msg + "【订单项入库数量不等于采购数量】";
                    reMap.put("msg", msg);
                    break;
                }
            } else {
                return ResultUtil.error("此订单还存在未上传入库凭证的订单项，无法变更。");
            }*/
            if(map==null){
                return ResultUtil.error("此订单还存在未上传入库凭证的订单项，无法变更。");
            }
        }
        String payStatus = order.getPayStatus();
        if (!"1".equals(payStatus)) {
            msg = msg + "【订单未完成支付】";
            reMap.put("status", "0");
            reMap.put("msg", msg);
        }
        return ResultUtil.successToObject(reMap);
    }

    @ApiOperation("修改订单入库状态")
    @PostMapping("/updateStockStatus/{orderId}")
    public Result updateStockStatus(@PathVariable("orderId") String orderId) {
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        SupOrder order = supOrderServiceImpl.getById(orderId);
        if (order == null) {
            return ResultUtil.error("无此订单，请联系管理员");
        }

        //查询每个订单项的凭证库存量 是否合理
        /*QueryWrapper<SupOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupOrderItem::getOrderId, orderId);
          List<SupOrderItem> orderItems = supOrderItemServiceImpl.list(queryWrapper);
         List<String> warnings = new ArrayList<>(Arrays.asList(order.getWarning().split(",")));
       for (SupOrderItem orderItem : orderItems) {
            BigDecimal amount = orderItem.getAmount();
            Map<String, Object> map = supOrderStockServiceImpl.getMap(new QueryWrapper<SupOrderStock>().select("sum(item_stock) as stockTotalCount").eq("ORDER_ITEM_ID", orderItem.getId()));
            if (map != null) {
                BigDecimal stockTotalCount = (BigDecimal) map.get("stockTotalCount");
                if (stockTotalCount.compareTo(amount) != 0) {
                    //订单项入库数不等于采购数量
                    warnings.add("7");
                    warnings.remove("1");
                }
            }
        }*/
        String payStatus = order.getPayStatus();
        if ("1".equals(payStatus)) {
            order.setEndTime(new Date());
        }
      /*  order.setWarning(String.join(",", warnings));*/
        order.setStockStatus("1");
        supOrderServiceImpl.updateById(order);
        return ResultUtil.success();
    }

    @ApiOperation("修改订单支付状态")
    @PostMapping("/updatePayStatus/{orderId}")
    public Result updatePayStatus(@PathVariable("orderId") String orderId) {
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        SupOrder order = supOrderServiceImpl.getById(orderId);
        if (order == null) {
            return ResultUtil.error("无此订单，请联系管理员");
        }
        QueryWrapper<SupOrderPay> payQueryWrapper = new QueryWrapper<>();
        payQueryWrapper.lambda().eq(SupOrderPay::getOrderId, orderId);
        int count = supOrderPayServiceImpl.count(payQueryWrapper);
        if (count == 0) {
            return ResultUtil.error("此订单还未上传支付凭证，无法变更。");
        }
      /*     payQueryWrapper.select("COALESCE(sum(pay_price),0.0000) as payTotalPrice");
        Map<String, Object> map = supOrderPayServiceImpl.getMap(payQueryWrapper);
        BigDecimal payTotalPrice = (BigDecimal) map.get("payTotalPrice");
        BigDecimal totalPrice = order.getTotalPrice();
        List<String> warnings = new ArrayList<>(Arrays.asList(order.getWarning().split(",")));
       if (payTotalPrice.compareTo(totalPrice) != 0) {
            //实际支付金额不等于订单金额
            warnings.add("6");
            warnings.remove("1");
        }*/
        String stockStatus = order.getStockStatus();
        if ("1".equals(stockStatus)) {
            order.setEndTime(new Date());
        }
    /*    order.setWarning(String.join(",", warnings));*/
        order.setPayStatus("1");
        supOrderServiceImpl.updateById(order);
        return ResultUtil.success();
    }

    @ApiOperation("上传国家采集订单excel")
    @RequestMapping("/importCountryOrder")
    public Result importCountryOrder(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_COUNTRY_ORDER, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupCountryOrderExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }

    @ApiOperation("上传退回订单excel")
    @RequestMapping("/importExitCountryOrder")
    public Result importExitCountryOrder(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_COUNTRY_ORDER_BACK, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupOffLineBackOrderExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }



    @ApiOperation("上传线下订单excel")
    @RequestMapping("/importOfflineOrder")
    public Result importOfflineOrder(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_OFFLINE_ORDER, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupOffLineOrderExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    @ApiOperation(value = "当前用户超时未支付订单个数")
    @RequestMapping("/getOrderStatus")
    public Result getOrderStatus(){
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            return ResultUtil.error("用户登录超时，请重新登录");
        }else{
            //医院Id
            String roleValue = currentUser.getRoleValue();
            QueryWrapper<SupOrder> queryWrapper = new QueryWrapper<>();
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",currentUser.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    queryWrapper.lambda().eq(SupOrder::getHospitalId, hospitalUser.getHospitalId());
                }
            }
            queryWrapper.lambda().like(SupOrder::getWarning, "8");
            int count = supOrderServiceImpl.count( queryWrapper);
            Map<String, Object> map = new HashMap<>();
            map.put("count",count);
            return ResultUtil.successToObject(map);
        }
    }

    @ApiOperation(value = "当前用户医院订单回款统计")
    @RequestMapping("/getOrderInvoiceList")
    public Result getOrderInvoiceList(){
        Map<String, Object> params = this.getRequestParams();
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            return ResultUtil.error("用户登录超时，请重新登录");
        }else{
            //医院Id
            String roleValue = currentUser.getRoleValue();

            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) &&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",currentUser.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    params.put("hospitalId",hospitalUser.getHospitalId());
                }
            }
            String startTime=(String) params.get("invoiceDate[0]");
            String endTime=(String) params.get("invoiceDate[1]");
            if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
                params.put("startTime",startTime);
                params.put("endTime",endTime);

            }

            IUser user =  this.getCurrentUser();
            if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
                params.put("regionId",user.getOrgCode());
            }
            List<Map<String, Object>> orderInvoiceList = supOrderServiceImpl.getOrderInvoiceList(params);
            return ResultUtil.successToList(orderInvoiceList);
        }
    }


    @RequestMapping("/exportOrder")
    public void exportOrderItem(HttpServletResponse response, HttpServletRequest request) throws Exception {
        Map<String, Object> params = this.getRequestParams();
        String submitTime = (String) params.get("submitTime");
        if (StringUtil.isNotBlank(submitTime)) {
            String beginOrderTime = submitTime.split(",")[0];
            String endOrderTime = submitTime.split(",")[1];
            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);

            }
        }
        List<SupOrderExportExcel> SupOrderExportExcels = supOrderServiceImpl.getOrderListToExcel(params);
        for (SupOrderExportExcel supOrderExportExcel : SupOrderExportExcels) {


            String orderSource = supOrderExportExcel.getSource();
            String orderStatus = supOrderExportExcel.getOrderStatus();
            String payStatus = supOrderExportExcel.getPayStatus();
            String stockStatus = supOrderExportExcel.getStockStatus();
            String orderWarning = supOrderExportExcel.getWarning();
            String source= "1".equals(orderSource) ? "深圳市药品电子交易平台" : ("2".equals(orderSource) ? "广东省药品电子交易平台" : "广州药品电子交易平台");
            String status;
            if("0".equals(orderStatus)){
                 status="待确认";
            }else if("1".equals(orderStatus)){
                 status="待发货";
            }else if("2".equals(orderStatus)){
                 status="部分发货";
            }else if("3".equals(orderStatus)){
                status="已发货";
            }else if("4".equals(orderStatus)){
                status="已完成";
            }else{
                status="已取消";
            }
            String pay = "1".equals(payStatus) ? "已支付" : ("2".equals(payStatus) ? "部分支付" : "未支付");
            String stock = "1".equals(stockStatus) ? "已入库" : ("2".equals(stockStatus) ? "部分入库" : "未入库");
            String warning = "1".equals(orderWarning) ? "正常" : "异常" ;
            supOrderExportExcel.setSource(source);
            supOrderExportExcel.setOrderStatus(status);
            supOrderExportExcel.setPayStatus(pay);
            supOrderExportExcel.setStockStatus(stock);
            supOrderExportExcel.setWarning(warning);
        }
        String fileName = "";
        fileName = URLEncoder.encode("订单信息统计数据", "UTF-8");

        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), SupOrderExportExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(SupOrderExportExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet(fileName)
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(SupOrderExportExcels);
    }

    public static void main (String [] args) {
        while (1 == 1) {
            JSONObject interparam = new JSONObject();
            com.inspur.ssp.supervise.utils.ebus.HttpClientUtil util = new HttpClientUtil("A0001","A0001","eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ7XCJyb2xlSWRcIjpcIjEsMlwiLFwicm9sZUNvZGVcIjpcIlNVUEVSX0FETUlOLERBVEFfQURNSU5cIixcInVzZXJOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJJZFwiOlwiMVwiLFwiYWNjb3VudFwiOlwicGxhdGZvcm1hZG1pblwiLFwiZGVwdENvZGVcIjpcIjEwMFwifSIsIngtYWVwLXVpZCI6IntcInJvbGVJZFwiOlwiMSwyXCIsXCJyb2xlQ29kZVwiOlwiU1VQRVJfQURNSU4sREFUQV9BRE1JTlwiLFwidXNlck5hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwidXNlcklkXCI6XCIxXCIsXCJhY2NvdW50XCI6XCJwbGF0Zm9ybWFkbWluXCIsXCJkZXB0Q29kZVwiOlwiMTAwXCJ9IiwiZXhwIjoxNjY3NTg5ODgzLCJpYXQiOjE2Njc1NDY2ODMsImp0aSI6IjNjMjBlMWRjOTY4YjQ0YmFiMjQ2OGUwZjdjYjYwODI5In0.P0ZljaCEpJRHHCB7DCKLnCoGqzJSeL46EZMYqZX6ON0");
            interparam.put("page","1");
            interparam.put("limit","100");
            interparam.put("type","5");
            String str=util.getResult("http://127.0.0.1:9333/supapi/api/test/gdTask",interparam,false,"form");
            System.out.println(str);
        }
    }
}

