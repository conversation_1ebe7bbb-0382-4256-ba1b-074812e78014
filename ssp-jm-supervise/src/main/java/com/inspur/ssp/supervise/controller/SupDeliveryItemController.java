package com.inspur.ssp.supervise.controller;



import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.excel.OverDeliveryExcel;
import com.inspur.ssp.supervise.bean.dto.excel.OverStockExcel;
import com.inspur.ssp.supervise.bean.entity.SupDelivery;
import com.inspur.ssp.supervise.bean.entity.SupDeliveryItem;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.service.ISupDeliveryItemService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.dao.BusiException;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 配送单明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@RestController
@RequestMapping("/supervise/supDeliveryItem")
public class SupDeliveryItemController extends AbstractController {
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupDeliveryItemService supDeliveryItemServiceImpl;

    @ApiOperation(value = "获取超时未入库配送单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "orderCode", value = "订单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "集采", required = false, dataType = "String"),
    })
    @RequestMapping("/getOverStockList")
    public Result getOverStockList(@RequestParam int page, @RequestParam int limit) {

        Map<String, Object> params = this.getRequestParams();
        String deliveryStartTime=(String) params.get("submitTime[0]");
        String deliveryEndTime=(String) params.get("submitTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        String roleValue;
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            roleValue = user.getRoleValue();
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) && !roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId",hospitalId);
                }
            }
        }
        if (StringUtil.isNotBlank(deliveryStartTime) && StringUtil.isNotBlank(deliveryEndTime)) {
            params.put("deliveryStartTime",deliveryStartTime);
            params.put("deliveryEndTime",deliveryEndTime);
        }
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> invoiceItemList = supDeliveryItemServiceImpl.getOverStockList(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(invoiceItemList);
        return ResultUtil.successToList(selectPage);
    }
    @RequestMapping("/getOverStockListToExcel")
    public void getOverStockListToExcel(HttpServletResponse response, HttpServletRequest request) throws Exception {

        Map<String, Object> params = this.getRequestParams();
        String deliveryStartTime=(String) params.get("submitTime[0]");
        String deliveryEndTime=(String) params.get("submitTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            throw new BusiException("用户登录超时，请重新登录。");
        }else{
            String roleValue = user.getRoleValue();
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    throw new BusiException("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId",hospitalId);
                }
            }
        }
        if (StringUtil.isNotBlank(deliveryStartTime) && StringUtil.isNotBlank(deliveryEndTime)) {
            params.put("deliveryStartTime",deliveryStartTime);
            params.put("deliveryEndTime",deliveryEndTime);
        }

        List<Map<String, Object>> invoiceItemList = supDeliveryItemServiceImpl.getOverStockList(params);
        List<OverStockExcel> excelList = new ArrayList<>();
        for(Map<String,Object> map : invoiceItemList){
            OverStockExcel excelObj = BeanUtil.convert(map,OverStockExcel.class);
            excelList.add(excelObj);
        }

        String fileName="超时未入库";
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), OverStockExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(OverStockExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,contentWriteCellStyle))
                .sheet(fileName)
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(excelList);
    }

    @ApiOperation(value = "获取配送单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "deliveryCode", value = "配送单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deliveryName", value = "配送企业", required = false, dataType = "String"),
    })
    @RequestMapping("/getDeliveryItemList")
    public Result getDeliveryItemList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        String startTime=(String) params.get("deliveryTime[0]");
        String endTime=(String) params.get("deliveryTime[1]");
        IUser user = (IUser) this.getCurrentUser();
        String roleValue;
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            roleValue = user.getRoleValue();
           if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole) &&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId",hospitalId);

                }
            }
        }
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime",startTime);
            params.put("endTime",endTime);

        }
        if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> deliveryItemList = supDeliveryItemServiceImpl.getDeliveryItemList(params);
        return ResultUtil.successToList(deliveryItemList);
    }


    @ApiOperation(value = "获取配送情况各医院/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deliveryName", value = "配送企业", required = false, dataType = "String"),
            @ApiImplicitParam(name = "stockStatus", value = "入库状态", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "采购平台", required = false, dataType = "String"),
    })
    @RequestMapping("/getDeliveryListByHospital")
    public Result getDeliveryListByHospital() {
        Map<String, Object> params = this.getRequestParams();
        String startTime=(String) params.get("deliveryTime[0]");
        String endTime=(String) params.get("deliveryTime[1]");
        String roleValue;
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            roleValue = user.getRoleValue();
           if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId",hospitalId);

                }
            }
        }

        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime",startTime);
            params.put("endTime",endTime);

        }

        if (roleValue.contains(areaRegionAdmin) &&StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
            params.put("regionId",user.getOrgCode());
        }

        List<Map<String, Object>> deliveryItemList = supDeliveryItemServiceImpl.getDeliveryListByHospital(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(deliveryItemList);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation(value = "获取配送单列表/根据用户权限")
    @ApiImplicitParams({

            @ApiImplicitParam(name = "orderCode", value = "订单编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "orderId", value = "订单id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deliveryCode", value = "配送单编号", required = false, dataType = "String"),
    })
    @RequestMapping("/getDeliveryItemByCode")
    public Result getDeliveryItemByCode() {
        Map<String, Object> params = this.getRequestParams();
        List<Map<String, Object>> deliveryItemList= supDeliveryItemServiceImpl.getDeliveryItemList(params);
        return ResultUtil.successToList(deliveryItemList);
    }

}

