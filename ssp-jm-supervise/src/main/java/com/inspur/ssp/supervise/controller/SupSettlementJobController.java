package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupSettlementJob;
import com.inspur.ssp.supervise.service.ISupSettlementJobService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@RestController
@RequestMapping("/supervise/supSettlementJob")
public class SupSettlementJobController extends AbstractController {

    @Autowired
    private ISupSettlementJobService supSettlementJobServiceImpl;

    @ApiOperation(value = "配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        QueryWrapper<SupSettlementJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .between(StringUtil.isNotEmpty(startDate) && StringUtil.isNotEmpty(endDate), SupSettlementJob::getExecuteDate, startDate, endDate)
                .orderByDesc(SupSettlementJob::getExecuteDate);
        PageHelper.startPage(page, limit);
        List<SupSettlementJob> list = supSettlementJobServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @RequestMapping("save")
    public Result save(@RequestBody SupSettlementJob supSettlementJob) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        String id = supSettlementJob.getId();
        if (StringUtil.isEmpty(id)) {
            supSettlementJob.setId(Tools.genId() + "");
        }
        supSettlementJob.setExecuteYear(DateFormatUtils.format(supSettlementJob.getExecuteDate(), "yyyy"));
        this.supSettlementJobServiceImpl.saveOrUpdate(supSettlementJob);
        return ResultUtil.success();
    }

    @RequestMapping("del/{id}")
    public Result del(@PathVariable("id") String id) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        this.supSettlementJobServiceImpl.removeById(id);
        return ResultUtil.success();
    }

    @RequestMapping("initJob")
    public Result initJob(@RequestParam String year) throws ParseException {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        this.supSettlementJobServiceImpl.remove(new QueryWrapper<SupSettlementJob>().lambda().eq(SupSettlementJob::getExecuteYear, year));
        List<SupSettlementJob> supSettlementJobs = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            SupSettlementJob supSettlementJob1 = new SupSettlementJob();
            supSettlementJob1.setId(Tools.genId() + "");
            supSettlementJob1.setCode("5");
            supSettlementJob1.setName("医疗机构对账自动审核");
            supSettlementJob1.setExecuteYear(year);
            String month = i < 10 ? 0 + "" + (i + 1) : (i + 1) + "";
            supSettlementJob1.setExecuteDate(DateUtils.parseDate(year + "-" + month + "-" + "08", "yyyy-MM-dd"));
            supSettlementJobs.add(supSettlementJob1);

            SupSettlementJob supSettlementJob2 = new SupSettlementJob();
            supSettlementJob2.setId(Tools.genId() + "");
            supSettlementJob2.setCode("6");
            supSettlementJob2.setName("配送企业对账自动复核");
            supSettlementJob2.setExecuteYear(year);
            supSettlementJob2.setExecuteDate(DateUtils.parseDate(year + "-" + month + "-" + "10", "yyyy-MM-dd"));
            supSettlementJobs.add(supSettlementJob2);
        }
        this.supSettlementJobServiceImpl.saveBatch(supSettlementJobs, supSettlementJobs.size());
        return ResultUtil.success();
    }

}

