package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto;
import com.inspur.ssp.supervise.bean.dto.purchase.SupCountryPurchaseExcel;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetailExportExcel;
import com.inspur.ssp.supervise.bean.vo.*;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.service.impl.SupDrugBatchDbServiceImpl;
import com.inspur.ssp.supervise.service.impl.SupDrugBatchServiceImpl;
import com.inspur.ssp.supervise.service.impl.SupOrderItemServiceImpl;
import com.inspur.ssp.supervise.utils.DosageFormUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.db.util.BeanUtils;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 药品清单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-24
 */
@RestController
@RequestMapping("/supervise/supDrugDetail")
public class SupDrugDetailController extends AbstractController {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;

    @Autowired
    private ISupDrugDetailExportExcelService supDrugDetailExportExcelServiceImpl;

    @Autowired
    private ISupDrugSourceService supDrugSourceServiceImpl;
    @Autowired
    private ISupDrugPriceService supDrugPriceServiceImpl;
    @Autowired
    private IDrugPriceViewService drugPriceViewServiceImpl;
    @Autowired
    private IDrugViewService drugViewServiceImpl;
    @Autowired
    private SupDrugBatchServiceImpl supDrugBatchService;
    @Autowired
    private SupOrderItemServiceImpl supOrderItemService;

    @Autowired
    private SupDrugBatchDbServiceImpl supDrugBatchDbService;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;

    @ApiOperation(value = "获取药品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "goodsName", value = "药品名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "药品类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "药品编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"), @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"), @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        if (Objects.nonNull(params.get("country")) && ((String) params.get("country")).equals("2")) {//线下的
            IUser currentUser = this.getCurrentUser();
            if (currentUser == null) {
                return ResultUtil.error("用户登录超时，请重新登录");
            } else {
                //医院Id
                String roleValue = currentUser.getRoleValue();

                if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                    SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", currentUser.getId()));
                    if (Objects.isNull(hospitalUser)) {
                        return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                    } else {
                        params.put("hospitalId", hospitalUser.getHospitalId());
                    }
                }
            }
        }
        PageHelper.startPage(page, limit);
        List<SupDrugDetailVo> supDrugDetailVos = supDrugDetailServiceImpl.selectDrugList(params);

        if (CollectionUtils.isNotEmpty(supDrugDetailVos)) {
            List<String> drugCodes = supDrugDetailVos.stream().map(SupDrugDetailVo::getCode).collect(Collectors.toList());
            QueryWrapper<DrugPriceView> priceViewQueryWrapper = new QueryWrapper<>();
            priceViewQueryWrapper.lambda().in(DrugPriceView::getDrugCode, drugCodes);
            List<DrugPriceView> drugPriceViews = drugPriceViewServiceImpl.list(priceViewQueryWrapper);

            for (SupDrugDetailVo supDrugDetailVo : supDrugDetailVos) {
                String code = supDrugDetailVo.getCode();
                List<DrugPriceView> priceViewArray = drugPriceViews.stream().filter(s -> s.getDrugCode().equals(code)).collect(Collectors.toList());
                BigDecimal price = new BigDecimal(0);
                String source = "";
                if (CollectionUtils.isNotEmpty(priceViewArray)) {
                    for (DrugPriceView drugPriceView : priceViewArray) {
                        if (price.compareTo(new BigDecimal(0)) == 0) {
                            price = drugPriceView.getPrice();
                            source = drugPriceView.getSource();
                        } else {
                            BigDecimal price2 = drugPriceView.getPrice();
                            if (price2.compareTo(price) < 0) {
                                price = price2;
                                source = drugPriceView.getSource();
                            } else if (price2.compareTo(price) == 0) {
                                String source2 = drugPriceView.getSource();
                                if (Integer.parseInt(source) > Integer.parseInt(source2)) {
                                    source = source2;
                                }
                            }
                        }

                    }
                }
                supDrugDetailVo.setPrice(price);
                supDrugDetailVo.setSource(source);
                List<SupDrugPriceVo> priceArray = BeanUtils.convertList(priceViewArray, SupDrugPriceVo.class);
                supDrugDetailVo.setPriceArray(priceArray);
            }
        }
        PageList<SupDrugDetailVo> selectPage = new PageList<>(supDrugDetailVos);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation(value = "获取药品列表（推荐平台）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "goodsName", value = "药品名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "药品类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "药品编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"), @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"), @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
    })
    @RequestMapping("/getDrugListWithSource")
    public Result getDrugListWithSource(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        PageHelper.startPage(page, limit);
        List<SupDrugDetailVo> supDrugDetailVos = supDrugDetailServiceImpl.selectDrugList(params);

        List<String> drugCodes = supDrugDetailVos.stream().map(SupDrugDetailVo::getCode).collect(Collectors.toList());
        QueryWrapper<DrugPriceView> priceViewQueryWrapper = new QueryWrapper<>();
        priceViewQueryWrapper.lambda().in(DrugPriceView::getDrugCode, drugCodes);
        List<DrugPriceView> drugPriceViews = drugPriceViewServiceImpl.list(priceViewQueryWrapper);

        for (SupDrugDetailVo supDrugDetailVo : supDrugDetailVos) {
            String code = supDrugDetailVo.getCode();
            List<DrugPriceView> priceViewArray = drugPriceViews.stream().filter(s -> s.getDrugCode().equals(code)).collect(Collectors.toList());
            BigDecimal price = new BigDecimal(0);
            String source = "";
            if (CollectionUtils.isNotEmpty(priceViewArray)) {
                for (DrugPriceView drugPriceView : priceViewArray) {
                    if (price.compareTo(new BigDecimal(0)) == 0) {
                        price = drugPriceView.getPrice();
                        source = drugPriceView.getSource();
                    } else {
                        BigDecimal price2 = drugPriceView.getPrice();
                        if (price2.compareTo(price) < 0) {
                            price = price2;
                            source = drugPriceView.getSource();
                        } else if (price2.compareTo(price) == 0) {
                            String source2 = drugPriceView.getSource();
                            if (Integer.parseInt(source) > Integer.parseInt(source2)) {
                                source = source2;
                            }
                        }
                    }

                }
            }
            supDrugDetailVo.setPrice(price);
            supDrugDetailVo.setSource(source);
            List<SupDrugPriceVo> priceArray = BeanUtils.convertList(priceViewArray, SupDrugPriceVo.class);
            supDrugDetailVo.setPriceArray(priceArray);
        }

        PageList<SupDrugDetailVo> selectPage = new PageList<>(supDrugDetailVos);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation(value = "获取药品所有列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "goodsName", value = "药品名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "药品类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "药品编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "packingSpecs", value = "包装规格", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"),
            @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"), @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
    })
    @RequestMapping("/all")
    public Result listAll() {
        Map<String, Object> params = this.getRequestParams();
        List<SupDrugDetailVo> supDrugDetailVos = supDrugDetailServiceImpl.selectDrugList(params);
        return ResultUtil.successToObject(supDrugDetailVos);
    }

    @ApiOperation(value = "获取药品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "goodsName", value = "药品名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "药品类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "药品编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"), @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"),
    })
    @RequestMapping("/getSupDrugList")
    public Result getSupDrugList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        PageHelper.startPage(page, limit);
        List<SupOrderDrugSelectVo> supOrderDrugSelectVos = supDrugDetailServiceImpl.selectSupDrugList(params);
        PageList<SupOrderDrugSelectVo> selectPage = new PageList<>(supOrderDrugSelectVos);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation(value = "获取平台药品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "goodsName", value = "药品名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "catalogId", value = "药品类别id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "药品编码", required = false, dataType = "String"),
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dosageForm", value = "剂型", required = false, dataType = "String"),
            @ApiImplicitParam(name = "company", value = "生产企业", required = false, dataType = "String"), @ApiImplicitParam(name = "approvalNumber", value = "批准文号", required = false, dataType = "String"),
    })
    @RequestMapping("/getSupDrugListBySource")
    public Result getSupDrugListBySource(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        PageHelper.startPage(page, limit);
        List<SupOrderDrugSelectVo> supOrderDrugSelectVos = supDrugDetailServiceImpl.getSupDrugListBySource(params);
        PageList<SupOrderDrugSelectVo> selectPage = new PageList<>(supOrderDrugSelectVos);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation("编辑药品")
    @RequestMapping("/edit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "failId", value = "导入Excel失败日志Id", required = true, dataType = "String"),
    })
    public Result edit(@ApiParam(value = "SupDrugDetail药品对象", required = true) @RequestBody JSONObject data) {
        SupDrugDetail supDrugDetail = BeanUtil.copyProperties(data, SupDrugDetail.class);
        String specs = supDrugDetail.getSpecs();
        String standardCode = supDrugDetail.getStandardCode();
        String packingSpecs = supDrugDetail.getPackingSpecs();
        String id = supDrugDetail.getId();
        String drugCountry = supDrugDetail.getCountry();
        String dosage = supDrugDetail.getDosageForm();
        if (StringUtil.isBlank(dosage)) {
            return ResultUtil.error("剂型不能为空");
        } else {
            String dosageForm = DosageFormUtil.formatDosageForm(dosage);
            supDrugDetail.setDosageFormGroup(dosageForm);
        }
        if (StringUtil.isBlank(specs)) {
            return ResultUtil.error("药品规格不能为空");
        }
        if (StringUtil.isBlank(standardCode)) {
            return ResultUtil.error("药品本位码不能为空");
        }
        if (StringUtil.isBlank(packingSpecs)) {
            return ResultUtil.error("包装规格不能为空");
        }

        LambdaQueryWrapper<SupDrugDetail> queryWrapper = new LambdaQueryWrapper<SupDrugDetail>().eq(SupDrugDetail::getSpecs, supDrugDetail.getSpecs()).eq(SupDrugDetail::getPackingSpecs, supDrugDetail.getPackingSpecs()).eq(SupDrugDetail::getStandardCode, supDrugDetail.getStandardCode()).eq(SupDrugDetail::getPacking, supDrugDetail.getPacking()).eq(SupDrugDetail::getDosageForm, supDrugDetail.getDosageForm());
        if (StringUtil.isNotBlank(id) && "1".equals(drugCountry)) {
            queryWrapper.ne(SupDrugDetail::getId, id);
        }
        queryWrapper.orderByDesc(SupDrugDetail::getVersion);
        List<SupDrugDetail> list = supDrugDetailServiceImpl.list(queryWrapper);
        if (list.size() > 0) {
            SupDrugDetail drugDetail = list.get(0);
            String country = list.get(0).getCountry();
            if ("1".equals(country)) {
                return ResultUtil.error("药品已存在,且为国家集中采购药品");
            } else {
                if ("0".equals(country) && "1".equals(drugCountry)) {
                    return ResultUtil.error("药品已存在,且为非国家集中采购药品，暂无法升级为国集药品");
                }
                return ResultUtil.successToObject("药品已存在", drugDetail);
            }
        }
        data.put("userId", this.getUserId());
        Result result = supDrugDetailServiceImpl.editDrugDetail(data);
        return result;
    }


    @ApiOperation(value = "药品详细信息")
    @RequestMapping("/info/{supDrugDetailId}")
    public Result info(@ApiParam("药品id") @PathVariable("supDrugDetailId") String supDrugDetailId) {
        Map<String, Object> drugDetail = supDrugDetailServiceImpl.getDrugDetailById(supDrugDetailId);
        if (drugDetail == null) {
            return ResultUtil.error("药品ID不存在");
        }
        return ResultUtil.successToObject(drugDetail);
    }

    /**
     * 获取医院采购过的药品以及相应的价格
     * @param purchaseDrugDetailDto
     * @return
     */
    @GetMapping("/info/purchase")
    public Result infoPurchase(PurchaseDrugDetailDto purchaseDrugDetailDto){
        PageHelper.startPage(purchaseDrugDetailDto.getPage(), purchaseDrugDetailDto.getLimit());
        Map<String, Object> map = supDrugDetailServiceImpl.getByPurchaseDrugDetailId(purchaseDrugDetailDto);
        return ResultUtil.successToObject(map);
    }

    @ApiOperation(value = "删除药品")
    @RequestMapping("/delete/{supDrugDetailId}")
    public Result deleteById(@ApiParam("药品ID") @PathVariable("supDrugDetailId") String supDrugDetailId) {
        //删除药品来源。删除药品价格
        SupDrugDetail supDrugDetail = supDrugDetailServiceImpl.getById(supDrugDetailId);
        String code = supDrugDetail.getCode();
        List<SupDrugSource> supDrugSources = supDrugSourceServiceImpl.list(new QueryWrapper<SupDrugSource>().eq("DRUG_CODE", code));
        List<String> ids = new ArrayList<>();
        if (supDrugSources.size() > 0) {
            supDrugSourceServiceImpl.remove(new QueryWrapper<SupDrugSource>().eq("DRUG_CODE", code));
            for (SupDrugSource supDrugSource : supDrugSources) {
                String id = supDrugSource.getId();
                ids.add(id);
            }
            supDrugPriceServiceImpl.remove(new QueryWrapper<SupDrugPrice>().in("SOURCE_ID", ids));
        }
        supDrugDetailServiceImpl.remove(new QueryWrapper<SupDrugDetail>().eq("CODE", code));
        //修改订单

//        Map<String,Object> orderParams = new HashMap<>();
//        orderParams.put("detailId",new SimpleDateFormat().format(supDrugDetail.getId()));
//        orderParams.put("endTime",new SimpleDateFormat().format(supDrugBatch.getTaskEndTime()));
//        orderParams.put("detailId",drugDetail.getId());
//        orderParams.put("batch",drugDetail.getBatch());
//        orderParams.put("country","1");
//        supOrderItemService.updateCountryByDetail(orderParams);

        //删除集采目录
//        LambdaQueryWrapper<SupDrugBatchDb> queryWrapper = new QueryWrapper<SupDrugBatchDb>().lambda().eq(SupDrugBatchDb::getDetailId,supDrugDetail.getId());
//
//        supDrugBatchDbService.remove(queryWrapper);

        return ResultUtil.success("删除成功");
    }

    @ApiOperation(value = "更新药品状态")
    @RequestMapping("/updateStatus")
    public Result updateStatus(@ApiParam(value = "SupDrugDetail药品对象", required = true) SupDrugDetail supDrugDetail) {
        UpdateWrapper<SupDrugDetail> updateWrapper = new UpdateWrapper<SupDrugDetail>();
        updateWrapper.eq("ID", supDrugDetail.getId());
        boolean update = supDrugDetailServiceImpl.update(supDrugDetail, updateWrapper);
        if (update) {
            return ResultUtil.success("操作成功");
        } else {
            return ResultUtil.error("操作失败");
        }
    }

    @ApiOperation(value = "升级版本")
    @RequestMapping("/updateVersion")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "failId", value = "导入失败日志id", required = false, dataType = "String"),
    })
    public Result updateVersion(@ApiParam(value = "SupDrugDetail药品对象", required = true) @RequestBody JSONObject data) {
        Result result = supDrugDetailServiceImpl.updateVersion(data);
        return result;
    }


    @ApiOperation(value = "获取药品所有列表/平台")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "country", value = "是否国家采集", required = false, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String"),
            @ApiImplicitParam(name = "source", value = "来源", required = false, dataType = "String"),
    })
    @RequestMapping("/getDrugBySource")
    public Result getDrugBySource(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        PageHelper.startPage(page, limit);
        List<Map<String, Object>> drugBySource = drugViewServiceImpl.getDrugBySource(params);
        PageList<Map<String, Object>> selectPage = new PageList<>(drugBySource);
        return ResultUtil.successToList(selectPage);
    }


    @RequestMapping("/exportDrugDetail")
    public void exportDrugDetail(HttpServletResponse response, HttpServletRequest request) throws IOException {
        Map<String, Object> params = this.getRequestParams();
        if (Objects.nonNull(params.get("country")) && ((String) params.get("country")).equals("2")) {//线下的
            IUser currentUser = this.getCurrentUser();
            if (currentUser == null) {
            } else {
                //医院Id
                String roleValue = currentUser.getRoleValue();

                if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)) {
                    SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", currentUser.getId()));
                    if (Objects.isNull(hospitalUser)) {
                    } else {
                        params.put("hospitalId", hospitalUser.getHospitalId());
                    }
                }
            }
        }
        String  country = (String) params.get("country");
        List<SupDrugDetailExportExcel> supDrugDetailExportExcel=null;
        if("1".equals(country)){
            supDrugDetailExportExcel= supDrugDetailExportExcelServiceImpl.selectDrugListJcExportExcel(params);
        }else{
            supDrugDetailExportExcel = supDrugDetailExportExcelServiceImpl.selectDrugListExportExcel(params);
        }

        if (CollectionUtils.isNotEmpty(supDrugDetailExportExcel)) {
            List<String> drugCodes = supDrugDetailExportExcel.stream().map(SupDrugDetailExportExcel::getCode).collect(Collectors.toList());
            QueryWrapper<DrugPriceView> priceViewQueryWrapper = new QueryWrapper<>();
            priceViewQueryWrapper.lambda().in(DrugPriceView::getDrugCode, drugCodes);
            List<DrugPriceView> drugPriceViews = drugPriceViewServiceImpl.list(priceViewQueryWrapper);

            for (SupDrugDetailExportExcel supDrugDetailList : supDrugDetailExportExcel) {
                String attribute = supDrugDetailList.getAttribute();
                if ("1".equals(attribute)) {
                    supDrugDetailList.setAttribute("国基");
                }else{
                    supDrugDetailList.setAttribute("空");
                }
                String code = supDrugDetailList.getCode();
                List<DrugPriceView> priceViewArray = drugPriceViews.stream().filter(s -> s.getDrugCode().equals(code)).collect(Collectors.toList());
                BigDecimal price = new BigDecimal(0);
                String source = "";
                if (CollectionUtils.isNotEmpty(priceViewArray)) {
                    for (DrugPriceView drugPriceView : priceViewArray) {
                        if (price.compareTo(new BigDecimal(0)) == 0) {
                            price = drugPriceView.getPrice();
                            source = drugPriceView.getSource();
                        } else {
                            BigDecimal price2 = drugPriceView.getPrice();
                            if (price2.compareTo(price) < 0) {
                                price = price2;
                                source = drugPriceView.getSource();
                            } else if (price2.compareTo(price) == 0) {
                                String source2 = drugPriceView.getSource();
                                if (Integer.parseInt(source) > Integer.parseInt(source2)) {
                                    source = source2;
                                }
                            }
                        }
                    }
                }
                supDrugDetailList.setPrice(String.valueOf(price));
            }
        }

        String fileName = URLEncoder.encode("药品目录", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), SupDrugDetailExportExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(SupDrugDetailExportExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet("药品目录")
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(supDrugDetailExportExcel);

    }


}

