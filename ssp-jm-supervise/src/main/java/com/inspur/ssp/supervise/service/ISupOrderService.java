package com.inspur.ssp.supervise.service;

import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.supervise.bean.dto.SupOrderContrastPriceDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderDataDto;
import com.inspur.ssp.supervise.bean.dto.purchase.SupOrderExportExcel;
import com.inspur.ssp.supervise.bean.dto.purchase.SupOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.entity.SupOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugSelectVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderDrugVo;
import com.inspur.ssp.supervise.bean.vo.SupOrderHospitalVo;
import org.jangod.iweb.core.bean.IUser;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2020-04-30
 */
public interface ISupOrderService extends IService<SupOrder> {

    List<SupOrderDrugVo> selectOrderDrugList(Map<String,Object> params);

    SupOrderDrugSelectVo contrastPrice(SupOrderContrastPriceDto orderContrastPriceDto);

    List<SupOrderDrugSelectVo> selectOtherDrug(SupOrderContrastPriceDto orderContrastPriceDto);

    SupOrder save(PubUser user, SupOrderDataDto orderDataDto) throws Exception;

    List<Map<String,Object>> getOrderNumByHospital(Map<String, Object> map);

    List<Map<String, Object>> getOrderNumBySource(Map<String, Object> map);

   Map<String, Object> getOrderCount(Map<String, Object> map);

    void updateOrderWarning(String orderId);

    List<Map<String, Object>> getOrderInvoiceList(Map<String, Object> map);


    void createDeliveryAndStockAndInvoice (List<SupOrderItem> orderItem, Map<String, Object> map );

     String convertDeliveryStatus(String type);

    void updateOrderStatus(String orderId);

    Map<String, Object> getCityData(Map<String, Object> params);

    List<Map<String, Object>> getOrderPriceData(Map<String, Object> params);

    List<SupOrderExportExcel> getOrderListToExcel(Map<String,Object> params);

    List<SupOrder> getOrderNotExistsDeliveryItem();

}
