package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.CdBatchLog;
import com.inspur.ssp.supervise.service.ICdBatchLogService;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 批次记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@RestController
@RequestMapping("/supervise/cdBatchLog")
public class CdBatchLogController extends AbstractController {
    @Autowired
    private ICdBatchLogService cdBatchLogServiceImpl;
    @PostMapping("/query")
    public Result query() {
        try {
            if (this.getCurrentUser() == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }

            int page = 1;
            int limit = 10;
            Map<String, Object> params = this.getRequestParams();
            if (params.containsKey("page")) {
                page = Integer.parseInt(params.get("page") + "");
            }
            if (params.containsKey("limit")) {
                limit = Integer.parseInt(params.get("limit") + "");
            }
            String busiType = (String)params.get("busiType");
            if(StringUtil.isBlank(busiType)){
                return ResultUtil.error("业务类型不能为空");
            }
            QueryWrapper<CdBatchLog> query = new QueryWrapper<>();
            query.ne("TOTAL",0);
            query.like("BUSI_TYPE",busiType);
            query.orderByDesc("import_time"); //根据导入时间进行排序
            PageHelper.startPage(page, limit);  //进行分页
            List<CdBatchLog> list = cdBatchLogServiceImpl.list(query);
            PageList<CdBatchLog> selectPage = new PageList<>(list);
            return ResultUtil.successToList("查询成功", selectPage);
        } catch (Exception e) {
            return ResultUtil.errorToList(e.getMessage());
        }
    }
}

