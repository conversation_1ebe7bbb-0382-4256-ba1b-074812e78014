package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ImportConfig;
import com.inspur.ssp.supervise.bean.entity.ImportFailLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 导入失败表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface IImportFailLogService extends IService<ImportFailLog> {
    ImportFailLog getImportFailLogById(String id);

    void updateImportFailLog(String failId);

}
