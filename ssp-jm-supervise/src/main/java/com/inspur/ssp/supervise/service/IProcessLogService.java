package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.ProcessLog;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jangod.iweb.core.bean.IUser;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
public interface IProcessLogService extends IService<ProcessLog> {

    void saveProcessLog(IUser user, String step,String deductionId,String status);

}
