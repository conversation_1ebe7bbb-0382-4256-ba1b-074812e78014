package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.entity.SupReconcile;
import com.inspur.ssp.supervise.bean.entity.SupSettlementCourse;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import com.inspur.ssp.supervise.service.ISupReconcileItemService;
import com.inspur.ssp.supervise.service.ISupReconcileService;
import com.inspur.ssp.supervise.service.ISupSettlementCourseService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@RestController
@RequestMapping("/supervise/supReconcile")
public class SupReconcileController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${hospitalAdmin}")
    private String hospitalAdmin;
    @Value("${deliveryAdmin}")
    private String deliveryAdmin;

    @Autowired
    private ISupReconcileService supReconcileServiceImpl;

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private ISupReconcileItemService supReconcileItemServiceImpl;

    @Autowired
    private ISupSettlementCourseService supSettlementCourseServiceImpl;

    @ApiOperation(value = "结算单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String num,
                       @RequestParam(required = false) String curNode, @RequestParam(required = false) String bizStatus,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        QueryWrapper<SupReconcile> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtil.isNotEmpty(num), SupReconcile::getNum, num)
                .eq(StringUtil.isNotEmpty(curNode), SupReconcile::getCurNode, curNode)
                .eq(StringUtil.isNotEmpty(bizStatus), SupReconcile::getBizStatus, bizStatus)
                .between(StringUtil.isNotEmpty(startDate) && StringUtil.isNotEmpty(endDate), SupReconcile::getCreationTime, startDate, endDate);
        String roleValue = user.getRoleValue();
        if (!(roleValue.contains(adminRole))) {
            queryWrapper.lambda().eq(SupReconcile::getCreator, user.getId());
        }
        queryWrapper.lambda().orderByDesc(SupReconcile::getCreationTime);
        PageHelper.startPage(page, limit);
        List<SupReconcile> list = supReconcileServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @ApiOperation("保存对账单 ")
    @PostMapping("/save")
    public Result save(@RequestBody JSONArray list) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.error("结算单不能为空。");
        }
        supReconcileServiceImpl.save(user, list);
        return ResultUtil.success();
    }

    @ApiOperation(value = "对账单查看")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String id, String type) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (StringUtil.isEmpty(id)) {
            return ResultUtil.error("结算单id不能为空。");
        }
        SupReconcile supReconcile = supReconcileServiceImpl.getById(id);

        HashMap<String, Object> params = new HashMap<>();
        params.put("reconcileId", id);

        String roleValue = user.getRoleValue();
        if (roleValue.contains(hospitalAdmin)) {
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
            if (Objects.isNull(hospitalUser)) {
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            } else {
                String hospitalId = hospitalUser.getHospitalId();
                params.put("hospitalId", hospitalId);

            }
        }
        if (roleValue.contains(deliveryAdmin)) {
            params.put("deliveryName", user.getName());
            params.put("itemStatus", "1");
        }

        /*if (supReconcile.getCurNode().equals("7")) { //生成支付单
            params.put("itemStatus", "1");
        }*/

        List<Map<String, Object>> items = supReconcileItemServiceImpl.getReconcileItemList(params);

        List<String> settlementIds = items.stream().map(item -> (String) item.get("settlementId")).collect(Collectors.toList());

        List<SupSettlementCourse> courseList = supSettlementCourseServiceImpl.list(new QueryWrapper<SupSettlementCourse>().lambda()
                .eq(SupSettlementCourse::getActive, "0")
                .and(i -> i.eq(SupSettlementCourse::getBizId, id).or(j -> j.in(SupSettlementCourse::getBizId, settlementIds))));
        LinkedHashMap<String, List<SupSettlementCourse>> collect = courseList.stream().collect(
                Collectors.groupingBy((c) -> c.getCurNodeId() + "," + c.getCurNodeName(), LinkedHashMap::new, Collectors.toList()));
        HashMap<String, Object> result = new HashMap<>();
        result.put("data", supReconcile);
        result.put("items", items);
        result.put("courses", collect);
        return ResultUtil.successToObject(result);
    }
}

