package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupDrugAmountTemp;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
public interface ISupDrugAmountTempService extends IService<SupDrugAmountTemp> {
    List< SupDrugAmountTemp> getDrugAmountTempList(Map<String,Object> params);
    List<Map<String,Object>> getDrugAmountOnMonth(Map<String,Object> params);
    List<Map<String,Object>> getDrugAmountOnQuarter(Map<String,Object> params);
}
