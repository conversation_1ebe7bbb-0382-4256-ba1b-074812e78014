package com.inspur.ssp.supervise.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.supervise.bean.entity.DrugPriceView;
import com.inspur.ssp.supervise.bean.entity.SupDrugBatch;
import com.inspur.ssp.supervise.bean.entity.SupDrugBatchDb;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;
import com.inspur.ssp.supervise.bean.vo.SupDrugDetailVo;
import com.inspur.ssp.supervise.bean.vo.SupDrugPriceVo;
import com.inspur.ssp.supervise.service.IDrugPriceViewService;
import com.inspur.ssp.supervise.service.ISupDrugDetailService;
import com.inspur.ssp.supervise.service.ISupOrderItemService;
import com.inspur.ssp.supervise.service.impl.SupDrugBatchDbServiceImpl;
import com.inspur.ssp.supervise.service.impl.SupDrugBatchServiceImpl;
import com.inspur.ssp.supervise.service.impl.SupDrugDetailServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.core.bean.ResultObject;
import org.jangod.iweb.core.dao.BusiException;
import org.jangod.iweb.db.util.BeanUtils;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/supervise/drugbatch")
public class DrugBatchController {

    @Autowired
    private SupDrugDetailServiceImpl drugDetailServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;
    @Autowired
    private SupDrugBatchServiceImpl supDrugBatchService;

    @Autowired
    private SupDrugBatchDbServiceImpl supDrugBatchDbService;

    @Autowired
    private IDrugPriceViewService drugPriceViewServiceImpl;

    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;

    @RequestMapping("/list")
    public ResultList getList(@RequestBody JSONObject params){
        try {
            Integer page = params.getInteger("page");
            Integer limit = params.getInteger("limit");
            params.put("guanlian",params.getString("flag"));
            if (page == null ||limit == null) {
                return ResultUtil.errorToList("分页参数不能为空!");
            }

            String batchCode = params.getString("batchCode");
            if (StringUtils.isBlank(batchCode)) {
                return ResultUtil.errorToList("批次不能为空!");
            }
            PageList<SupDrugDetailVo> list = drugDetailServiceImpl.getSupDrugDetail(params);
            List<SupDrugDetailVo> supDrugDetailVos = list.getRows();

            if(CollectionUtils.isNotEmpty(supDrugDetailVos)){
                List<String> drugCodes = supDrugDetailVos.stream().map(SupDrugDetailVo::getCode).collect(Collectors.toList());
                QueryWrapper<DrugPriceView> priceViewQueryWrapper = new QueryWrapper<>();
                priceViewQueryWrapper.lambda().in(DrugPriceView::getDrugCode,drugCodes);
                List<DrugPriceView> drugPriceViews = drugPriceViewServiceImpl.list(priceViewQueryWrapper);

                for (SupDrugDetailVo supDrugDetailVo : supDrugDetailVos) {
                    String code = supDrugDetailVo.getCode();
                    List<DrugPriceView> priceViewArray = drugPriceViews.stream().filter(s->s.getDrugCode().equals(code)).collect(Collectors.toList());
                    BigDecimal price = new BigDecimal(0);
                    String source = "";
                    if(CollectionUtils.isNotEmpty(priceViewArray) ){
                        for (DrugPriceView drugPriceView:priceViewArray) {
                            if(price.compareTo(new BigDecimal(0)) == 0){
                                price = drugPriceView.getPrice();
                                source = drugPriceView.getSource();
                            } else {
                                BigDecimal price2 = drugPriceView.getPrice();
                                if(price2.compareTo(price) < 0){
                                    price = price2;
                                    source = drugPriceView.getSource();
                                }else if(price2.compareTo(price) == 0){
                                    String source2 = drugPriceView.getSource();
                                    if(Integer.parseInt(source) > Integer.parseInt(source2)){
                                        source = source2;
                                    }
                                }
                            }

                        }
                    }
                    supDrugDetailVo.setPrice(price);
                    supDrugDetailVo.setSource(source);
                    List<SupDrugPriceVo> priceArray = BeanUtils.convertList(priceViewArray, SupDrugPriceVo.class);
                    supDrugDetailVo.setPriceArray(priceArray);
                }
            }
            return ResultUtil.successToList(list);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.errorToList(e.getMessage());
        }
    }

    //订单同步为集采
    @RequestMapping("/syncOrderItemCountryByOrderNum")
    public ResultObject syncOrderItemJcByOrderNum (@RequestBody JSONObject params) {
        String batchCode = params.getString("batchCode");
        if (StringUtil.isBlank(batchCode)) {
            return ResultUtil.errorToObject("batchCode不能为空!");
        }

        String orderNum = params.getString("orderNum");
        if (StringUtil.isBlank(orderNum)) {
            return ResultUtil.errorToObject("detailIds不能为空!");
        }

        try {
            //获取当前批次信息
            LambdaQueryWrapper<SupDrugBatch> supDrugBatchQueryWrapper = new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getCode, batchCode);
            SupDrugBatch supDrugBatch = supDrugBatchService.getOne(supDrugBatchQueryWrapper);
            if (supDrugBatch == null) {
                throw new BusiException("该批次不存在!");
            }

            QueryWrapper<SupOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ORDER_NUM",orderNum);
            SupOrderItem supOrderItem = supOrderItemServiceImpl.getOne(queryWrapper);


            String [] detailIdList = {supOrderItem.getDetailId()};
            for (String detailId:detailIdList) {
                SupDrugBatchDb supDrugBatchDb = new SupDrugBatchDb();
                supDrugBatchDb.setDetailId(detailId);
                supDrugBatchDb.setNameCn(params.getString("goodsName"));
                supDrugBatchDb.setBatchCode(params.getString("batchCode"));
                //保存集采信息
                supDrugBatchDbService.saveDetailSupBatch(supDrugBatchDb);

                //订单刷新成集采
                Map<String, Object> orderParams = new HashMap<>();
                orderParams.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskStartTime()));
                orderParams.put("endTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskEndTime()));
                orderParams.put("detailId", detailId);
                orderParams.put("batch", batchCode);
                orderParams.put("country", "1");
                supOrderItemServiceImpl.updateCountryByDetail(orderParams);
            }

            return ResultUtil.successToObject("绑定成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.errorToObject(e.getMessage());
        }

    }

    //订单同步为集采
    @RequestMapping("/syncOrderItemCountry")
    public ResultObject syncOrderItemJc (@RequestBody JSONObject params) {
        String batchCode = params.getString("batchCode");
        if (StringUtil.isBlank(batchCode)) {
            return ResultUtil.errorToObject("batchCode不能为空!");
        }

        String detailIds = params.getString("detailIds");
        if (StringUtil.isBlank(detailIds)) {
            return ResultUtil.errorToObject("detailIds不能为空!");
        }

        try {
            //获取当前批次信息
            LambdaQueryWrapper<SupDrugBatch> supDrugBatchQueryWrapper = new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getCode, batchCode);
            SupDrugBatch supDrugBatch = supDrugBatchService.getOne(supDrugBatchQueryWrapper);
            if (supDrugBatch == null) {
                throw new BusiException("该批次不存在!");
            }


            String [] detailIdList = detailIds.split(",");
            for (String detailId:detailIdList) {
                SupDrugBatchDb supDrugBatchDb = new SupDrugBatchDb();
                supDrugBatchDb.setDetailId(detailId);
                supDrugBatchDb.setNameCn(params.getString("goodsName"));
                supDrugBatchDb.setBatchCode(params.getString("batchCode"));
                //保存集采信息
                supDrugBatchDbService.saveDetailSupBatch(supDrugBatchDb);

                //订单刷新成集采
                Map<String, Object> orderParams = new HashMap<>();
                orderParams.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskStartTime()));
                orderParams.put("endTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskEndTime()));
                orderParams.put("detailId", detailId);
                orderParams.put("batch", batchCode);
                orderParams.put("country", "1");
                supOrderItemServiceImpl.updateCountryByDetail(orderParams);
            }

            return ResultUtil.successToObject("绑定成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.errorToObject(e.getMessage());
        }

    }

    //取消关联
    @RequestMapping("/syncOrderItemNotCountry")
    public ResultObject syncOrderItem (@RequestBody JSONObject params) {
        String batchCode = params.getString("batchCode");
        if (StringUtil.isBlank(batchCode)) {
            return ResultUtil.errorToObject("batchCode不能为空!");
        }

        String detailIds = params.getString("detailIds");
        if (StringUtil.isBlank(detailIds)) {
            return ResultUtil.errorToObject("detailIds不能为空!");
        }

        try {
            //获取当前批次信息
            LambdaQueryWrapper<SupDrugBatch> supDrugBatchQueryWrapper = new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getCode, batchCode);
            SupDrugBatch supDrugBatch = supDrugBatchService.getOne(supDrugBatchQueryWrapper);
            if (supDrugBatch == null) {
                throw new BusiException("该批次不存在!");
            }


            String [] detailIdList = detailIds.split(",");
            for (String detailId:detailIdList) {
                SupDrugBatchDb supDrugBatchDb = new SupDrugBatchDb();
                supDrugBatchDb.setDetailId(detailId);
                supDrugBatchDb.setNameCn(params.getString("goodsName"));
                supDrugBatchDb.setBatchCode(params.getString("batchCode"));
                //保存集采信息
                supDrugBatchDbService.removeBatch(supDrugBatchDb);

                //订单刷新成集采
                Map<String, Object> orderParams = new HashMap<>();
                orderParams.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskStartTime()));
                orderParams.put("endTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(supDrugBatch.getTaskEndTime()));
                orderParams.put("detailId", detailId);
                orderParams.put("batch", "");
                orderParams.put("country", "0");
                supOrderItemServiceImpl.updateCountryByDetail(orderParams);
            }

            return ResultUtil.successToObject("绑定成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.errorToObject(e.getMessage());
        }

    }
}