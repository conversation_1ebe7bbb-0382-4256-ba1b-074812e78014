package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupDeliveryCompany;
import com.inspur.ssp.supervise.service.ISupDeliveryCompanyService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@RestController
@RequestMapping("/supervise/supDeliveryCompany")
public class SupDeliveryCompanyController extends AbstractController {
    @Autowired
    private ISupDeliveryCompanyService supDeliveryCompanyServiceImpl;
    @ApiOperation(value = "获取配送企业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "配送企业名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "配送企业编码", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupDeliveryCompany> queryWrapper = new LambdaQueryWrapper<SupDeliveryCompany>()
                .orderByAsc(SupDeliveryCompany::getSortOrder).orderByDesc(SupDeliveryCompany::getLastModifitionTime);
        //查询字段
        String code = (String) params.get("code");  //查询编码
        String name = (String) params.get("name");   //查询名称
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupDeliveryCompany::getCode,code);
        }
        if (StringUtil.isNotBlank(name)){
            queryWrapper.like(SupDeliveryCompany::getName, name);
        }
        PageHelper.startPage(page, limit);
        List<SupDeliveryCompany> list = supDeliveryCompanyServiceImpl.list(queryWrapper);
        PageList<SupDeliveryCompany> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }



    @ApiOperation("保存配送企业")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "SupDeliveryCompany配送企业对象", required = true) SupDeliveryCompany supDeliveryCompany) {
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        IUser currentUser = this.getCurrentUser();
        SupDeliveryCompany deliveryCompany = supDeliveryCompanyServiceImpl.getOne(new QueryWrapper<SupDeliveryCompany>().lambda().eq(SupDeliveryCompany::getCode, supDeliveryCompany.getCode()));
        if (deliveryCompany != null && StringUtil.isNotBlank(deliveryCompany.getId())) {
            return ResultUtil.error("配送企业编码已存在");
        }

        SupDeliveryCompany delivery = supDeliveryCompanyServiceImpl.getOne(new QueryWrapper<SupDeliveryCompany>().lambda().eq(SupDeliveryCompany::getName, supDeliveryCompany.getName()));

        if (delivery != null && StringUtil.isNotBlank(delivery.getId())) {
            return ResultUtil.error("配送企业名称已存在");
        }

        supDeliveryCompany.setStatus("1");
        supDeliveryCompany.setId(String.valueOf(Tools.genId()));
        supDeliveryCompany.setCreationTime(new Date());
        supDeliveryCompany.setLastModifitionTime(new Date());
        supDeliveryCompany.setCreator(currentUser.getId());
        supDeliveryCompany.setLastModifitor(currentUser.getId());
        supDeliveryCompanyServiceImpl.save(supDeliveryCompany);
        return ResultUtil.success("保存成功");

    }
    @ApiOperation(value = "修改配送企业")
    @RequestMapping("/update")
    public Result update(@ApiParam(value = "SupDeliveryCompany配送企业对象", required = true) SupDeliveryCompany supDeliveryCompany) {
        SupDeliveryCompany deliveryCompany = supDeliveryCompanyServiceImpl.getOne(new QueryWrapper<SupDeliveryCompany>().lambda().eq(SupDeliveryCompany::getCode, supDeliveryCompany.getCode()).ne(SupDeliveryCompany::getId,supDeliveryCompany.getId()));
        if (deliveryCompany != null && StringUtil.isNotBlank(deliveryCompany.getId())) {
            return ResultUtil.error("配送企业编码已存在");
        }

        SupDeliveryCompany delivery= supDeliveryCompanyServiceImpl.getOne(new QueryWrapper<SupDeliveryCompany>().lambda().eq(SupDeliveryCompany::getName, supDeliveryCompany.getName()).ne(SupDeliveryCompany::getId,supDeliveryCompany.getId()));
        if (delivery != null && StringUtil.isNotBlank(delivery.getId())) {
            return ResultUtil.error("配送企业名称已存在");
        }


        supDeliveryCompany.setLastModifitionTime(new Date());
        IUser currentUser = this.getCurrentUser();
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        supDeliveryCompany.setLastModifitor(currentUser.getId());
        supDeliveryCompanyServiceImpl.updateById(supDeliveryCompany);
        return ResultUtil.success("修改成功");
    }

    @ApiOperation(value = "配送企业详细信息")
    @RequestMapping("/info/{deliveryCompanyId}")
    public Result info(@ApiParam("配送企业id") @PathVariable("deliveryCompanyId") String deliveryCompanyId) {
        SupDeliveryCompany supDeliveryCompany = supDeliveryCompanyServiceImpl.getById(deliveryCompanyId);
        if (supDeliveryCompany == null) {
            return ResultUtil.error("配送企业ID不存在");
        }
        return ResultUtil.successToObject(supDeliveryCompany);
    }

    @ApiOperation(value = "删除配送企业")
    @RequestMapping("/delete/{deliveryCompanyId}")
    public Result deleteById(@ApiParam("配送企业ID") @PathVariable("deliveryCompanyId") String deliveryCompanyId) {
        supDeliveryCompanyServiceImpl.removeById(deliveryCompanyId);
        return ResultUtil.success("删除成功");
    }
    @ApiOperation(value = "更新配送企业状态")
    @RequestMapping("/updateStatus")
    public  Result updateStatus(@ApiParam(value = "supDeliveryCompany配送企业对象",required = true) SupDeliveryCompany supDeliveryCompany){
        UpdateWrapper<SupDeliveryCompany> updateWrapper = new UpdateWrapper<SupDeliveryCompany>();
        updateWrapper.eq("ID", supDeliveryCompany.getId());
        boolean update = supDeliveryCompanyServiceImpl.update(supDeliveryCompany, updateWrapper);
        if(update){
            return ResultUtil.success("操作成功");
        }else{
            return ResultUtil.error("操作失败");
        }
    }

}


