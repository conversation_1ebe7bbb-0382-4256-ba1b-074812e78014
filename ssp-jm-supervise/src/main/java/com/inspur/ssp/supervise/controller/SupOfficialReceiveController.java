package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupOfficial;
import com.inspur.ssp.supervise.bean.entity.SupOfficialReceive;
import com.inspur.ssp.supervise.bean.vo.SupOfficialReceiveVo;
import com.inspur.ssp.supervise.service.ISupOfficialReceiveService;
import com.inspur.ssp.supervise.service.ISupOfficialService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公文接收表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-20
 */
@RestController
@RequestMapping("/supervise/supOfficialReceive")
public class SupOfficialReceiveController extends AbstractController {
    @Autowired
    private ISupOfficialReceiveService supOfficialReceiveServiceImpl;
    @Autowired
    private ISupOfficialService supOfficialServiceImpl;
    @ApiOperation(value = "获取公文接收列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "isSee", value = "是否查阅", required = false, dataType = "String"),
            @ApiImplicitParam(name = "title", value = "标题", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            params.put("userId",user.getId());
        }
        PageHelper.startPage(page, limit);
        List<SupOfficialReceiveVo> officialLReceiveList = supOfficialReceiveServiceImpl.getOfficialLReceiveList(params);
        PageList<SupOfficialReceiveVo> selectPage = new PageList<>(officialLReceiveList);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation(value = "删除公文")
    @RequestMapping("/delete/{supOfficialReceiveId}")
    public Result deleteById(@ApiParam("公文ID") @PathVariable("supOfficialReceiveId") String supOfficialReceiveId) {
        SupOfficialReceive supOfficialReceive = supOfficialReceiveServiceImpl.getById(supOfficialReceiveId);
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            ResultUtil.error("用户登录超时，请重新登录");
        }else{
            supOfficialReceive.setLastModifitor(currentUser.getId());
        }
        supOfficialReceive.setStatus("-1");
        supOfficialReceive.setLastModifitionTime(new Date());
        supOfficialReceiveServiceImpl.updateById(supOfficialReceive);
        return ResultUtil.success("删除成功");
    }
    @ApiOperation(value = "查阅公文")
    @RequestMapping("/see/{supOfficialReceiveId}")
    public Result see(@ApiParam("公文ID") @PathVariable("supOfficialReceiveId") String supOfficialReceiveId) {
        SupOfficialReceive supOfficialReceive = supOfficialReceiveServiceImpl.getById(supOfficialReceiveId);
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            return  ResultUtil.error("用户登录超时，请重新登录");
        }else{
            supOfficialReceive.setLastModifitor(currentUser.getId());
        }
        supOfficialReceive.setIsSee("1");
        supOfficialReceive.setSeeTime(new Date());
        supOfficialReceive.setLastModifitionTime(new Date());
        supOfficialReceiveServiceImpl.updateById(supOfficialReceive);
        String officialId = supOfficialReceive.getOfficialId();
        SupOfficial supOfficial = supOfficialServiceImpl.getById(officialId);
        supOfficial.setNum(supOfficial.getNum()+1);
        supOfficialServiceImpl.updateById(supOfficial);
        return  ResultUtil.success();
    }
    @ApiOperation(value = "查阅当前用户待阅件个数")
    @RequestMapping("/getReceiveCount")
    public Result getReceiveCount(){
        IUser currentUser = this.getCurrentUser();
        if(currentUser == null) {
            return ResultUtil.error("用户登录超时，请重新登录");
        }else{
            int count = supOfficialReceiveServiceImpl.count(new QueryWrapper<SupOfficialReceive>().eq("USER_ID", currentUser.getId()).eq("STATUS", "1").eq("IS_SEE", "0"));
            return ResultUtil.successToObject(count);
        }
    }

    @ApiOperation(value = "获取公文接收列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "isSee", value = "是否查阅", required = false, dataType = "String"),
            @ApiImplicitParam(name = "officialId", value = "公文id", required = true, dataType = "String"),
    })
    @RequestMapping("/getReceiveList")
    public Result getReceiveList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        String officialId = (String) params.get("officialId");
        if(StringUtil.isBlank(officialId)){
            return ResultUtil.error("公文id不存在");
        }
        PageHelper.startPage(page, limit);
        List<SupOfficialReceiveVo> officialLReceiveList = supOfficialReceiveServiceImpl.getOfficialLReceiveList(params);
        PageList<SupOfficialReceiveVo> selectPage = new PageList<>(officialLReceiveList);
        return ResultUtil.successToList(selectPage);
    }
}

