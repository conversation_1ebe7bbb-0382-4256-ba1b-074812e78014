package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.ConMaterialDelivery;
import com.inspur.ssp.supervise.bean.entity.SupDelivery;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.service.IConMaterialDeliveryItemService;
import com.inspur.ssp.supervise.service.IConMaterialDeliveryService;
import com.inspur.ssp.supervise.service.IConMaterialDetailService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 配送主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@RestController
@RequestMapping("/supervise/conMaterialDelivery")
public class ConMaterialDeliveryController extends AbstractController {

    @Autowired
    private IConMaterialDeliveryService conMaterialDeliveryServiceImpl;

    @Autowired
    private IConMaterialDeliveryItemService conMaterialDeliveryItemServiceImpl;


    @ApiOperation(value = "配送单详情")
    @RequestMapping("/show/{deliveryCode}")
    public Result show(@PathVariable("deliveryCode") String deliveryCode) {
        try {
            Map<Object, Object> map = new HashMap<>();
            LambdaQueryWrapper<ConMaterialDelivery> queryWrapper = new LambdaQueryWrapper<ConMaterialDelivery>().eq(ConMaterialDelivery::getCode, deliveryCode);
            ConMaterialDelivery conMaterialDelivery = conMaterialDeliveryServiceImpl.getOne(queryWrapper);
            if(Objects.isNull(conMaterialDelivery)){
                return ResultUtil.error("无此配送单，请联系管理员");
            }else{
                List<Map<String, Object>> deliveryItemDetail = conMaterialDeliveryItemServiceImpl.getConMaterialDeliveryItemDetail(deliveryCode);
                map.put("data",conMaterialDelivery);
                map.put("deliveryItem",deliveryItemDetail);
            }

            return ResultUtil.successToObject(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }


}

