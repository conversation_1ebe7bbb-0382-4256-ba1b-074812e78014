package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.bsp.service.IPubUserService;
import com.inspur.ssp.bsp.service.impl.PubUserServiceImpl;
import com.inspur.ssp.supervise.bean.entity.SupHospital;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.service.ISupHospitalService;
import com.inspur.ssp.supervise.service.ISupHospitalUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@RestController
@RequestMapping("/supervise/supHospitalUser")
public class SupHospitalUserController extends AbstractController {
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupHospitalService supHospitalServiceImpl;

    /**
     * 查询医院用户
     *
     * @return Result
     */
    @ApiOperation(" 查询医院用户，已关联或未关联分组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "用户姓名查询条件", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalId", value = "医院id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "flag", value = "1:已关联，0:未关联", required = true, dataType = "String"),
            @ApiImplicitParam(name = "page", value = "当前页", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "分页大小", required = false, dataType = "Integer")
    })
    @RequestMapping("/queryHospitalUser")
    public Result query(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            PageList<List<PubUser>> listPageList = supHospitalUserServiceImpl.queryHospitalUser(params);
            return ResultUtil.successToList(listPageList);
        } catch (Exception e) {
            return ResultUtil.error(e.getMessage());
        }
    }

    @ApiOperation("医院关联用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "ids", value = "所选用户id", required = true, dataType = "String"),
    })
    @RequestMapping("/relateItem")
    public Result relateItem() {
        try {

            IUser currentUser = this.getCurrentUser();
            if(currentUser == null) {
                ResultUtil.error("获取用户失败，请重新登录");
            }
            Map<String, Object> params = this.getRequestParams();
            String hospitalId = (String) params.get("hospitalId");
            String ids = (String) params.get("ids");
            if (StringUtil.isNotBlank(ids) && StringUtil.isNotBlank(hospitalId)) {
                String[] userIds = ids.split(",");
                for (String id : userIds) {
                    SupHospitalUser supHospitalUser = new SupHospitalUser();
                    supHospitalUser.setId(Tools.genId() + "");
                    supHospitalUser.setUserId(id);
                    supHospitalUser.setHospitalId(hospitalId);
                    supHospitalUser.setCreator(currentUser.getId());
                    supHospitalUser.setCreationTime(new Date());
                    supHospitalUserServiceImpl.save(supHospitalUser);
                }
            }
            return ResultUtil.success("关联成功");
        } catch (Exception e) {
            return ResultUtil.error(e.getMessage());
        }
    }

    @ApiOperation("医院解除关联用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院Id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "ids", value = "所选用户id", required = true, dataType = "String"),
    })
    @RequestMapping("/dissociatedItem")
    public Result dissociatedItem() {
        try {
            Map<String, Object> params = this.getRequestParams();
            String hospitalId = (String) params.get("hospitalId");
            String ids = (String) params.get("ids");
            if (StringUtil.isNotBlank(ids) && StringUtil.isNotBlank(hospitalId)) {
                String[] userIds = ids.split(",");
                for (String id : userIds) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("hospital_id", hospitalId);
                    map.put("user_id", id);
                    supHospitalUserServiceImpl.removeByMap(map);
                }
            }
            return ResultUtil.success("取消关联成功");
        } catch (Exception e) {
            return ResultUtil.error(e.getMessage());
        }
    }


    @ApiOperation("获取用户所属医院")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "医院Id", required = true, dataType = "path"),
    })
    @RequestMapping("/userHospital/{userId}")
    public Result userHospital(@PathVariable("userId") String userId) {
        try {
            QueryWrapper<SupHospitalUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SupHospitalUser::getUserId,userId);
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(queryWrapper, false);
            if(Objects.isNull(hospitalUser)){
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            }
            SupHospital supHospital = supHospitalServiceImpl.getById(hospitalUser.getHospitalId());
            return ResultUtil.successToObject(supHospital);
        } catch (Exception e) {
            return ResultUtil.error(e.getMessage());
        }
    }
}

