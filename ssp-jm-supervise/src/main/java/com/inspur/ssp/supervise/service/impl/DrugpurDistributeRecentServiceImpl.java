package com.inspur.ssp.supervise.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.DrugpurDistributeRecent;
import com.inspur.ssp.supervise.bean.entity.DrugpurInvoice;
import com.inspur.ssp.supervise.mapper.DrugpurDistributeRecentMapper;
import com.inspur.ssp.supervise.mapper.DrugpurInvoiceMapper;
import com.inspur.ssp.supervise.service.DrugpurDistributeRecentService;
import com.inspur.ssp.supervise.service.DrugpurInvoiceService;
import org.jangod.iweb.core.bean.PageList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 批次记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
public class DrugpurDistributeRecentServiceImpl extends ServiceImpl<DrugpurDistributeRecentMapper, DrugpurDistributeRecent> implements DrugpurDistributeRecentService {

    @Autowired
    private DrugpurDistributeRecentMapper drugpurDistributeRecentMapper;

    @Override
    public PageList<DrugpurDistributeRecent> getList(int page, int limit, Map map) {
        PageHelper.startPage(page,limit);
        PageList<DrugpurDistributeRecent> pageList  = new PageList(drugpurDistributeRecentMapper.getList(map));
        return pageList;
    }
}
