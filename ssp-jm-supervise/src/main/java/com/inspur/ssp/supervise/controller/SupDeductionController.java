package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.SupDeductionDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.math.NumberUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@RestController
@RequestMapping("/supervise/supDeduction")
public class SupDeductionController extends AbstractController {

    @Autowired
    private ISupDeductionService supDeductionServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupDeductionItemService supDeductionItemServiceImpl;
    @Autowired
    private IProcessLogService processLogServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Autowired
    private ISupInsuranceLogService supInsuranceLogServiceImpl;
    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;
    @Autowired
    private ISupOrderService supOrderServiceImpl;
    @Autowired ISupDeliveryItemService supDeliveryItemServiceImpl;

    @ApiOperation(value = "获取扣款单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<SupDeduction> queryWrapper = new QueryWrapper<>();
            String beginDeductionTime=(String) params.get("creationTime[0]");
            String endDeductionTime=(String) params.get("creationTime[1]");
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (StringUtil.isNotBlank(beginDeductionTime) && StringUtil.isNotBlank(endDeductionTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginDeductionTime);
                Date endTime = formatter.parse(endDeductionTime);
                queryWrapper.between("CREATION_TIME", beginTime, endTime);
            }else if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    queryWrapper.lambda().eq(SupDeduction::getHospitalId, hospitalUser.getHospitalId());
                }
            }
            String hospitalName = (String) params.get("hospitalName");
            String status = (String) params.get("status");


            if (StringUtil.isNotEmpty(status)) {
                queryWrapper.lambda().eq(SupDeduction::getStatus, status);
            }
            if (StringUtil.isNotBlank(hospitalName)) {
                queryWrapper.lambda().like(SupDeduction::getHospitalName, hospitalName);
            }

            queryWrapper.lambda().orderByDesc(SupDeduction::getCreationTime).orderByAsc(SupDeduction::getHospitalName).ne(SupDeduction::getStatus,"-1");
            PageHelper.startPage(page, limit);
            List<SupDeduction> list = supDeductionServiceImpl.list(queryWrapper);
            PageList<SupDeduction> selectPage = new PageList<>(list);
            return ResultUtil.successToList(selectPage);
        } catch (ParseException e) {
            e.printStackTrace();
            return ResultUtil.error("订单查询失败");
        }
    }

    @ApiOperation(value = "扣款单详情")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String deductionId) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("deductionId",deductionId);
            List<Map<String,Object>> deductionItems = supDeductionItemServiceImpl.queryList(map);
            return ResultUtil.successToList(deductionItems);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }

    @ApiOperation("保存扣款单")
    @PostMapping("/save")
    public Result save(@RequestBody JSONArray list) throws Exception {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        for (Object obj : list) {
            if (obj == null) {
                return ResultUtil.error("未检测到扣款数据");
            }
            SupDeductionDto supDeductionDto = BeanUtil.convert((JSONObject) JSONObject.toJSON(obj), SupDeductionDto.class);
            supDeductionServiceImpl.save(user,supDeductionDto);
        }
        return ResultUtil.success();
    }

    @ApiOperation("修改")
    @PostMapping("/update")
    public Result update(@RequestBody SupDeduction supDeduction) throws Exception {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        String status = supDeduction.getStatus();
        if(StringUtil.isBlank(status)){
            return ResultUtil.error("状态不能为空");
        }
        //申请撤销
        if("-1".equals(status)){
            SupDeduction deduction = supDeductionServiceImpl.getById(supDeduction.getId());
            status = deduction.getStatus();
            if(status.equals("3")){
                //删除支付指令数据
                String code = supDeduction.getCode();
                supInsuranceLogServiceImpl.remove(new LambdaQueryWrapper<SupInsuranceLog>().eq(SupInsuranceLog::getDeductionCode,code));

                Map<String, Object> map = new HashMap<>();
                map.put("deductionId",supDeduction.getId());
                List<Map<String,Object>> deductionItems = supDeductionItemServiceImpl.queryList(map);
                for (Object o : deductionItems) {
                    JSONObject jo=(JSONObject) JSONObject.toJSON(o);

                    String  invoiceItemId = (String) jo.get("invoiceItemId");
                    String orderItemId = (String) jo.get("orderItemId");//订单明细id
                    String deliveryItemId = (String) jo.get("deliveryItemId");//订单

                    SupInvoiceItem supInvoiceItem = new SupInvoiceItem();
                    supInvoiceItem.setLastModifier(user.getId());
                    supInvoiceItem.setLastModifiedTime(new Date());
                    supInvoiceItem.setId(invoiceItemId);
                    supInvoiceItem.setPayTime(null);
                    supInvoiceItem.setDocInfo("[]");
                    supInvoiceItem.setPayPrice(new BigDecimal(0));
                    supInvoiceItem.setInsurancePayFlag("0");
                    supInvoiceItem.setPayStatus("1");
                    supInvoiceItem.setMany(false);
                    Result result = supInvoiceItemServiceImpl.savePayVoucher(supInvoiceItem);
                    if (result.getState() == 0) {
                        return result;
                    }
                    QueryWrapper<SupDeliveryItem> deliveryItemQueryWrapper = new QueryWrapper<>();
                    deliveryItemQueryWrapper.lambda().eq(SupDeliveryItem::getId, deliveryItemId);
                    SupDeliveryItem deliveryItem = supDeliveryItemServiceImpl.getOne(deliveryItemQueryWrapper);
                    if (StringUtil.isEmpty(deliveryItem.getWarning()) || deliveryItem.getWarning().equals("1")) {
                        deliveryItem.setWarning("8");
                    } else if (!deliveryItem.getWarning().contains("8")) {
                        deliveryItem.setWarning(deliveryItem.getWarning() + "," + "8");
                    }
                    supDeliveryItemServiceImpl.updateById(deliveryItem);
                    SupOrderItem supOrderItem= supOrderItemServiceImpl.getById(orderItemId);
                    //更改订单明细预警超时
                    supOrderServiceImpl.updateOrderWarning(supOrderItem.getOrderId());
                }

            }
            status=String.valueOf(Integer.parseInt(status)-1);
            supDeduction.setStatus(status);

        }
        supDeduction.setLastModifitionTime(new Date());
        supDeduction.setLastModifitor(user.getId());
        supDeductionServiceImpl.updateById(supDeduction);
        processLogServiceImpl.saveProcessLog(user, supDeduction.getStatus(),supDeduction.getId(),"1");
        //如果是申请撤销生成支付指令

        return ResultUtil.success();
    }
}

