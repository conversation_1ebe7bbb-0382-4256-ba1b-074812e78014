package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupOfficialReceive;
import com.baomidou.mybatisplus.extension.service.IService;
import com.inspur.ssp.supervise.bean.vo.SupOfficialReceiveVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公文接收表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-20
 */
public interface ISupOfficialReceiveService extends IService<SupOfficialReceive> {
    List<SupOfficialReceiveVo> getOfficialLReceiveList(Map<String,Object> params);
}
