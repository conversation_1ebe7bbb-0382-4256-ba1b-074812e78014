package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.SupRecommendOrderDto;
import com.inspur.ssp.supervise.bean.dto.SupOrderItemDto;
import com.inspur.ssp.supervise.bean.dto.SupRecommendOrderItemDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.bean.vo.*;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-13
 */
@RestController
@RequestMapping("/supervise/supRecommendOrder")
public class SupRecommendOrderController extends AbstractController {

    @Autowired
    private ISupRecommendOrderService supRecommendOrderServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupRecommendOrderItemService supRecommendOrderItemServiceImpl;
    @Autowired
    private ISupDictItemService supDictItemServiceImpl;
    @Autowired
    private ISupHospitalService supHospitalServiceImpl;
    @Autowired
    private ISupDrugDetailService supDrugDetailServiceImpl;

    @Autowired
    private ISupDrugCompanyService supDrugCompanyServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;

    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;
    @ApiOperation(value = "获取订单推荐列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            QueryWrapper<SupRecommendOrder> queryWrapper = new QueryWrapper<>();
            String beginOrderTime=(String) params.get("submitTime[0]");
            String endOrderTime=(String) params.get("submitTime[1]");
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();

            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date beginTime = formatter.parse(beginOrderTime);
                Date endTime = formatter.parse(endOrderTime);
                queryWrapper.between("CREATION_TIME", beginTime, endTime);
            }else  if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    queryWrapper.lambda().eq(SupRecommendOrder::getHospitalId, hospitalUser.getHospitalId());
                }
            }
            String hospitalName = (String) params.get("hospitalName");
            String warning = (String) params.get("warning");
            String orderNum = (String) params.get("orderNum");
            if (StringUtil.isNotEmpty(orderNum)) {
                queryWrapper.lambda().like(SupRecommendOrder::getOrderNum, orderNum);
            }
            if (StringUtil.isNotEmpty(warning)) {
                queryWrapper.lambda().eq(SupRecommendOrder::getWarning, warning);
            }
            if (StringUtil.isNotBlank(hospitalName)) {
                queryWrapper.lambda().like(SupRecommendOrder::getHospitalName, hospitalName);
            }

            queryWrapper.lambda().orderByDesc(SupRecommendOrder::getCreationTime).orderByAsc(SupRecommendOrder::getHospitalName);
            PageHelper.startPage(page, limit);
            List<SupRecommendOrder> list = supRecommendOrderServiceImpl.list(queryWrapper);
            PageList<SupRecommendOrder> selectPage = new PageList<>(list);
            return ResultUtil.successToList(selectPage);
        } catch (ParseException e) {
            e.printStackTrace();
            return ResultUtil.error("订单查询失败");
        }
    }




    @ApiOperation(value = "订单详情")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String orderId) {
        try {
            SupRecommendOrder order = supRecommendOrderServiceImpl.getById(orderId);
            if (order == null) {
                return ResultUtil.error("无此订单，请联系管理员");
            }

            String warning = order.getWarning();
            String[] split = warning.split(",");
            List<SupDictVo> warningData = supDictItemServiceImpl.getDictItem("WARNING");
            List<String> dictLabel = new ArrayList<>();
            for (String s : split) {
                Optional<SupDictVo> supDictVoData = warningData.stream().filter(item -> item.getValue().equals(s)).findFirst();
                if (supDictVoData.isPresent()) {
                    // 存在
                    SupDictVo supDictVo = supDictVoData.get();
                    String label = supDictVo.getLabel();
                    dictLabel.add(label);
                }
            }
            order.setWarning(String.join(" | ", dictLabel));


            QueryWrapper< SupRecommendOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq( SupRecommendOrderItem::getOrderId, orderId);
            List<SupRecommendOrderItemVo> orderItem = new ArrayList<>();
            List< SupRecommendOrderItem> items = supRecommendOrderItemServiceImpl.list(queryWrapper);
            for ( SupRecommendOrderItem item : items) {
                SupRecommendOrderItemVo supOrderItemVo = BeanUtil.copyProperties(item, SupRecommendOrderItemVo.class);
                String detailId = item.getDetailId();
                SupDrugDetail supDrugDetail = supDrugDetailServiceImpl.getById(detailId);
                supOrderItemVo.setDosageForm(supDrugDetail.getDosageForm());
                supOrderItemVo.setPackingSpecs(supDrugDetail.getPackingSpecs());
                supOrderItemVo.setSpecs(supDrugDetail.getSpecs());
                supOrderItemVo.setDrugCompanyId(supDrugDetail.getDrugCompanyId());
                SupDrugCompany supDrugCompany = supDrugCompanyServiceImpl.getById(supDrugDetail.getDrugCompanyId());
                if (Objects.nonNull(supDrugCompany)) {
                    supOrderItemVo.setDrugCompanyName(supDrugCompany.getName());
                }
                orderItem.add(supOrderItemVo);
            }
            SupHospital hospital = supHospitalServiceImpl.getById(order.getHospitalId());
            SupRecommendDetailVo orderDetailVo = new SupRecommendDetailVo();
            orderDetailVo.setData(order);
            orderDetailVo.setOrderItem(orderItem);
            orderDetailVo.setHospital(hospital);

            return ResultUtil.successToObject(orderDetailVo);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }

    @ApiOperation("保存订单")
    @PostMapping("/save")
    public Result save(@RequestBody JSONObject map) throws Exception {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        SupHospital hospital = BeanUtil.convert(map.getJSONObject("hospital"), SupHospital.class);
        if (hospital == null) {
            return ResultUtil.error("用户未绑定医院，请联系管理员绑定。");
        }
        JSONObject source = map.getJSONObject("source");
        if (source == null) {
            return ResultUtil.error("未检测到订单药品，请选择药品下单。");
        }
        for (Object obj : source.values()) {
            if (obj == null) {
                return ResultUtil.error("未检测到订单药品，请选择药品下单。");
            }
            SupRecommendOrderDto supOrderDataDto = BeanUtil.convert((JSONObject) JSONObject.toJSON(obj), SupRecommendOrderDto.class);
            supOrderDataDto.setHospital(hospital);

            List<SupRecommendOrderItemDto> orderItem = supOrderDataDto.getOrderItem();
            if (CollectionUtils.isEmpty(orderItem)) {
                return ResultUtil.error("未检测到订单药品，请选择药品下单。");
            }
            BigDecimal totalPrice = new BigDecimal("0");
            for (SupRecommendOrderItemDto orderItemDto : orderItem) {
                BigDecimal itemPrice = orderItemDto.getUnitPrice().multiply(orderItemDto.getAmount());
                if (itemPrice.compareTo(orderItemDto.getItemPrice()) != 0) {
                    return ResultUtil.error("【" + orderItemDto.getCatalogName() + "】存在恶意篡改总价行为，无法提交");
                }
                if (StringUtil.isNotEmpty(orderItemDto.getSystemContrast())) {
                    if (orderItemDto.getSystemContrast().equals("0")) {
                        if (StringUtil.isEmpty(orderItemDto.getReason())) {
                            return ResultUtil.error("【" + orderItemDto.getCatalogName() + "】为非系统自动筛选结果，必须填写原因。");
                        }
                    }
                }
                totalPrice = totalPrice.add(itemPrice);
            }
            if (totalPrice.compareTo(supOrderDataDto.getTotalPrice()) != 0) {
                return ResultUtil.error("存在恶意篡改合计价格行为，无法提交");
            }
            supRecommendOrderServiceImpl.save(user, supOrderDataDto);
        }
        return ResultUtil.success();
    }
}

