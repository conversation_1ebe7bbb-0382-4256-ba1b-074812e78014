package com.inspur.ssp.supervise.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.jangod.iweb.core.action.AbstractController;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.core.bean.ResultList;
import org.jangod.iweb.util.BeanUtil;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 结算业务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@RestController
@RequestMapping("/supervise/supSettlement")
public class SupSettlementController extends AbstractController {

    @Value("${adminRole}")
    private String adminRole;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${hospitalAdmin}")
    private String hospitalAdmin;
    @Autowired
    private ISupSettlementService supSettlementServiceImpl;

    @Autowired
    private ISupSettlementItemService supSettlementItemServiceImpl;

    @Autowired
    private ISupSettlementCourseService supSettlementCourseServiceImpl;

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;

    @Autowired
    private ISupSettlementItemCourseService supSettlementItemCourseServiceImpl;

    @ApiOperation(value = "结算单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String num,
                       @RequestParam(required = false) String curNode, @RequestParam(required = false) String bizStatus,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        QueryWrapper<SupSettlement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtil.isNotEmpty(num), SupSettlement::getNum, num)
                .eq(StringUtil.isNotEmpty(curNode), SupSettlement::getCurNode, curNode)
                .eq(StringUtil.isNotEmpty(bizStatus), SupSettlement::getBizStatus, bizStatus)
                .between(StringUtil.isNotEmpty(startDate) && StringUtil.isNotEmpty(endDate), SupSettlement::getCreationTime, startDate, endDate);
        String roleValue = user.getRoleValue();
        if (!(roleValue.contains(adminRole) || roleValue.contains(medicalRole))) {
            queryWrapper.lambda().eq(SupSettlement::getCreator, user.getId());
        }
        queryWrapper.lambda().orderByDesc(SupSettlement::getCreationTime);
        PageHelper.startPage(page, limit);
        List<SupSettlement> list = supSettlementServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @ApiOperation(value = "结算查看")
    @RequestMapping("/show/{id}")
    public Result show(@PathVariable("id") String id, String type) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (StringUtil.isEmpty(id)) {
            return ResultUtil.error("结算单id不能为空。");
        }
        SupSettlement supSettlement = supSettlementServiceImpl.getById(id);

        HashMap<String, Object> params = new HashMap<>();
        params.put("settlementId", id);

        String roleValue = user.getRoleValue();
        if (roleValue.contains(hospitalAdmin)) {
            SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
            if (Objects.isNull(hospitalUser)) {
                return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
            } else {
                String hospitalId = hospitalUser.getHospitalId();
                params.put("hospitalId", hospitalId);

            }
        }

        List<Map<String, Object>> items = supSettlementItemServiceImpl.getSettlementItemList(params);

        List<SupSettlementCourse> courseList = supSettlementCourseServiceImpl.list(new QueryWrapper<SupSettlementCourse>().lambda()
                .eq(SupSettlementCourse::getBizId, id).eq(SupSettlementCourse::getActive, "0"));
        LinkedHashMap<String, List<SupSettlementCourse>> collect = courseList.stream().collect(
                Collectors.groupingBy((c) -> c.getCurNodeId() + "," + c.getCurNodeName(), LinkedHashMap::new, Collectors.toList()));
        HashMap<String, Object> result = new HashMap<>();
        result.put("data", supSettlement);
        result.put("items", items);
        result.put("courses", collect);
        return ResultUtil.successToObject(result);
    }

    @ApiOperation(value = "生成对账单的结算单详情列表")
    @RequestMapping("/rsItemList")
    public Result itemList(@RequestBody List<String> settlementIds) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("settlementIds", settlementIds);
        params.put("itemStatus", "1");
        List<Map<String, Object>> items = supSettlementItemServiceImpl.getSettlementItemList(params);
        return ResultUtil.successToList(items);
    }

    @ApiOperation("保存结算单")
    @PostMapping("/save")
    public Result save(@RequestBody JSONArray list) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.error("结算单不能为空。");
        }
        supSettlementServiceImpl.save(user, list);
        return ResultUtil.success();
    }

    @ApiOperation("待办列表")
    @PostMapping("/todo")
    public Result todo(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String num,
                       @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
//        Map<String, Object> params = new HashMap<>();
//        params.put("userId", user.getId());
//        params.put("num", num);
//        params.put("startDate", startDate);
//        params.put("endDate", endDate);
//        List<Map<String, Object>> list = supSettlementServiceImpl.settlementTodo(params);
        List<SupSettlementCourse> list = supSettlementCourseServiceImpl.list(new QueryWrapper<SupSettlementCourse>().lambda()
                .eq(SupSettlementCourse::getUserId, user.getId()).eq(SupSettlementCourse::getActive, "1")
                .eq(StringUtil.isNotEmpty(num), SupSettlementCourse::getBizNum, num)
                .orderByDesc(SupSettlementCourse::getReceiveTime));
        return ResultUtil.successToList(list);
    }

    @ApiOperation("下一步")
    @PostMapping("/nextData")
    public Result nextData(@RequestBody JSONObject obj) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (Objects.isNull(obj)) {
            return ResultUtil.error("数据不能为空。");
        }
        supSettlementServiceImpl.nextData(user, obj);
        return ResultUtil.success();
    }

    @ApiOperation("不通过")
    @PostMapping("/acceptFail")
    public Result acceptFail(@RequestBody JSONObject obj) {
        IUser user = this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }
        if (Objects.isNull(obj)) {
            return ResultUtil.error("数据不能为空。");
        }
        supSettlementServiceImpl.acceptFail(user, obj);
        return ResultUtil.success();
    }

    @ApiOperation(value = "结算查看")
    @RequestMapping("/itemCourse/{id}")
    public Result itemCourse(@PathVariable("id") String id) {
        List<SupSettlementItemCourse> list = this.supSettlementItemCourseServiceImpl.list(new QueryWrapper<SupSettlementItemCourse>().lambda().eq(SupSettlementItemCourse::getSettlementItemId, id)
                .orderByAsc(SupSettlementItemCourse::getCreationTime));
        return ResultUtil.successToList(list);
    }
}

