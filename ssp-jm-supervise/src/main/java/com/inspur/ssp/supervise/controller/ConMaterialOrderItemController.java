package com.inspur.ssp.supervise.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.PurchaseDrugDetailDto;
import com.inspur.ssp.supervise.bean.dto.purchase.ConMaterialOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.dto.purchase.SupOrderItemExportExcel;
import com.inspur.ssp.supervise.bean.entity.ConMaterialBatch;
import com.inspur.ssp.supervise.bean.entity.SupHospitalUser;
import com.inspur.ssp.supervise.bean.vo.SupDictVo;
import com.inspur.ssp.supervise.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@RestController
@RequestMapping("/supervise/conMaterialOrderItem")
public class ConMaterialOrderItemController extends AbstractController {
    @Autowired
    private IConMaterialOrderItemService conMaterialOrderItemServiceImpl;
    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupOrderItemService supOrderItemServiceImpl;
    @Autowired
    private IConMaterialBatchService conMaterialBatchServiceImpl;

    @Autowired
    private ISupDictItemService supDictItemServiceImpl;

    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${areaRegionAdmin}")
    private String areaRegionAdmin;

    @ApiOperation(value = "获取订单明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");
            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);
            }
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) &&!roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
            if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
                params.put("regionId",user.getOrgCode());
            }
            PageHelper.startPage(page, limit);  //进行分页
            List<Map<String, Object>> orderItemList = conMaterialOrderItemServiceImpl.getOrderItemList(params);
            PageList<Map<String, Object>> selectPage = new PageList<>(orderItemList);
            return ResultUtil.successToList(selectPage);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("查询失败");
        }
    }


    @ApiOperation(value = "耗材采购明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int")
    })
    @RequestMapping("/materialPurchaseList")
    public Result materialPurchaseList(@RequestParam int page, @RequestParam int limit) {
        try {
            Map<String, Object> params = this.getRequestParams();
            String beginOrderTime = (String) params.get("submitTime[0]");
            String endOrderTime = (String) params.get("submitTime[1]");
            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);
            }
            IUser user = (IUser) this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录超时，请重新登录。");
            }
            String roleValue = user.getRoleValue();
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole) &&!roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", user.getId()));
                if (Objects.isNull(hospitalUser)) {
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                } else {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
            if (roleValue.contains(areaRegionAdmin) && StringUtil.isNotBlank(user.getOrgCode()) && ! user.getOrgCode().equals("100")) {
                params.put("regionId",user.getOrgCode());
            }
            PageHelper.startPage(page, limit);  //进行分页
            List<Map<String, Object>> orderItemList = conMaterialOrderItemServiceImpl.getMaterialPurchaseList(params);
            PageList<Map<String, Object>> selectPage = new PageList<>(orderItemList);
            return ResultUtil.successToList(selectPage);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("查询失败");
        }
    }


    /**
     * 获取医院采购过的耗材以及相应的价格
     * @param purchaseDrugDetailDto
     * @return
     */
    @GetMapping("/info/purchase")
    public Result infoPurchase(PurchaseDrugDetailDto purchaseDrugDetailDto){
        PageHelper.startPage(purchaseDrugDetailDto.getPage(), purchaseDrugDetailDto.getLimit());
        Map<String, Object> map = conMaterialOrderItemServiceImpl.getByPurchaseMaterialId(purchaseDrugDetailDto);
        return ResultUtil.successToObject(map);
    }


    @RequestMapping("/exportOrderItem")
    public void exportOrderItem(HttpServletResponse response, HttpServletRequest request) throws Exception {
        Map<String, Object> params = this.getRequestParams();
        String submitTime = (String) params.get("submitTime");
        if (StringUtil.isNotBlank(submitTime)) {
            String beginOrderTime = submitTime.split(",")[0];
            String endOrderTime = submitTime.split(",")[1];
            if (StringUtil.isNotBlank(beginOrderTime) && StringUtil.isNotBlank(endOrderTime)) {
                params.put("startTime", beginOrderTime);
                params.put("endTime", endOrderTime);

            }
        }

        String stockInTime = (String) params.get("stockInTime");
        if (StringUtil.isNotBlank(stockInTime)) {
            String beginStockInTime = stockInTime.split(",")[0];
            String endStockInTime = stockInTime.split(",")[1];
            if (StringUtil.isNotBlank(beginStockInTime) && StringUtil.isNotBlank(endStockInTime)) {
                params.put("startStockInTime", beginStockInTime);
                params.put("endStockInTime", endStockInTime);

            }
        }
        String userId = (String) params.get("userId");
        String roleValue = (String) params.get("roleValue");
        if (StringUtil.isNotBlank(userId) && StringUtil.isNotBlank(roleValue)) {
            if (!roleValue.contains(adminRole) && !roleValue.contains(socialSecurityRole) && !roleValue.contains(medicalRole)&&!roleValue.contains(areaRegionAdmin)) {
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID", userId));
                if (Objects.nonNull(hospitalUser)) {
                    params.put("hospitalId", hospitalUser.getHospitalId());
                }
            }
        }
        List<ConMaterialOrderItemExportExcel> conMaterialOrderItemExportExcels = conMaterialOrderItemServiceImpl.getOrderItemListToExcel(params);
        List<SupDictVo> dictItem = supDictItemServiceImpl.getDictItem("SOURCE");
        for (ConMaterialOrderItemExportExcel supOrderItemExportExcel : conMaterialOrderItemExportExcels) {
            String systemContrast = supOrderItemExportExcel.getSystemContrast();
            supOrderItemExportExcel.setSystemContrast(systemContrast.equals("0") ? "否" : "是");
            String orderItemStatus = supOrderItemExportExcel.getOrderItemStatus();
            String s = supOrderItemServiceImpl.convertOrderStatus(orderItemStatus);
            supOrderItemExportExcel.setOrderItemStatus(s);
            String payStatus = supOrderItemExportExcel.getPayStatus();
            String stockStatus = supOrderItemExportExcel.getStockStatus();
            String pay = "1".equals(payStatus) ? "已支付" : ("2".equals(payStatus) ? "部分支付" : "未支付");
            String stock = "1".equals(stockStatus) ? "已入库" : ("2".equals(stockStatus) ? "部分入库" : "未入库");
            supOrderItemExportExcel.setPayStatus(pay);
            supOrderItemExportExcel.setStockStatus(stock);
            String source = supOrderItemExportExcel.getSource();
            Optional<SupDictVo> supDictVoData = dictItem.stream().filter(item -> item.getValue().equals(source)).findFirst();
            if (supDictVoData.isPresent()) {
                // 存在
                SupDictVo supDictVo = supDictVoData.get();
                String sourceName = supDictVo.getLabel();
                supOrderItemExportExcel.setSource(sourceName);
            }
            supOrderItemExportExcel.setCountryTag(StringUtil.isNotEmpty(supOrderItemExportExcel.getCountryTag()) &&
                    supOrderItemExportExcel.getCountryTag().equals("1") ? "是" : "否");

            supOrderItemExportExcel.setMinUnitPrice(getMinUnitPrice(supOrderItemExportExcel.getPackingSpecs(), supOrderItemExportExcel.getUnitPrice()));
            supOrderItemExportExcel.setMinUnitAmount(getMinUnitAmount(supOrderItemExportExcel.getPackingSpecs(), supOrderItemExportExcel.getAmount()));
        }
        String fileName = "";
        String country = (String) params.get("country");
        if ("0".equals(country)) {
            fileName = URLEncoder.encode("非集采采购明细数据", "UTF-8");
        }
        if ("1".equals(country)) {
            fileName = URLEncoder.encode("国集采购明细数据", "UTF-8");
        }

        if ("2".equals(country)) {
            fileName = URLEncoder.encode("线下采购明细数据", "UTF-8");
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");


        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(contentWriteFont);

        EasyExcel.write(response.getOutputStream(), ConMaterialOrderItemExportExcel.class)
                //设置输出excel版本,不设置默认为xlsx
                .excelType(ExcelTypeEnum.XLS).head(ConMaterialOrderItemExportExcel.class)
                //设置拦截器或自定义样式
                .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle))
                .sheet(fileName)
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                //这里的addsumColomn方法是个添加合计的方法,可删除
                .doWrite(conMaterialOrderItemExportExcels);
    }


    private String getMinUnitPrice(String packingSpecs, String unitPrice) {
        if (StringUtil.isNotBlank(packingSpecs)) {

            String pattern = "\\d+";
            Pattern r = Pattern.compile(pattern);

            // 现在创建 matcher 对象
            Matcher m = r.matcher(packingSpecs);
            if (m.find()) {
                String numArr = m.group(0);
                if (StringUtil.isNotBlank(unitPrice)) {
                    try {
                        Float price = Float.valueOf(unitPrice);
                        Float num = Float.valueOf(numArr);
                        return String.format("%.3f", (price / num));
                    } catch (Exception e) {
                        return "";
                    }
                } else {
                    return "";
                }
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    private String getMinUnitAmount(String packingSpecs, String amount) {
        if (StringUtil.isNotBlank(packingSpecs)) {

            String pattern = "\\d+";
            Pattern r = Pattern.compile(pattern);

            // 现在创建 matcher 对象
            Matcher m = r.matcher(packingSpecs);
            if (m.find()) {
                String numArr = m.group(0);
                if (StringUtil.isNotBlank(amount)) {
                    try {
                        Float price = Float.valueOf(amount);
                        Float num = Float.valueOf(numArr);
                        return String.format("%.3f", (price * num));
                    } catch (Exception e) {
                        return "";
                    }
                } else {
                    return "";
                }
            } else {
                return "";
            }
        } else {
            return "";
        }
    }


}

