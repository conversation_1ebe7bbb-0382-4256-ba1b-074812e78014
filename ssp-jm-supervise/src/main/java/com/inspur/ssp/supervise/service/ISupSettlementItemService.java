package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupSettlementItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结算业务明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ISupSettlementItemService extends IService<SupSettlementItem> {

    List<Map<String, Object>> getSettlementItemList(Map<String, Object> params);
}
