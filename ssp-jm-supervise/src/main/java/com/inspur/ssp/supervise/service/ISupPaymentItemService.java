package com.inspur.ssp.supervise.service;

import com.inspur.ssp.supervise.bean.entity.SupPaymentItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
public interface ISupPaymentItemService extends IService<SupPaymentItem> {

    List<Map<String, Object>> getPaymentItemList(HashMap<String, Object> params);
}
