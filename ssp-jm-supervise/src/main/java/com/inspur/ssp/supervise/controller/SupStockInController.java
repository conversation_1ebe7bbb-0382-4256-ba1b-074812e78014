package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.purchase.SupStockInExportDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.excel.ExcelUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@RestController
@RequestMapping("/supervise/supStockIn")
public class SupStockInController extends AbstractController {

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupStockInService supStockInServiceImpl;
    @Autowired
    private ISupStockInItemService supStockInItemServiceImpl;
    @Autowired
    private ISupFileService supFileServiceImpl;
    @Autowired
    private ICdBatchLogService cdBatchLogServiceImpl;
    @Value("${medicalRole}")
    private String medicalRole;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;

    @ApiOperation(value = "获取入库单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "code", value = "入库单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deliveryName", value = "配送企业", required = false, dataType = "String"),
    })
    @RequestMapping("/getStockInList")
    public Result getStockInList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        LambdaQueryWrapper<SupStockIn> queryWrapper = new LambdaQueryWrapper<SupStockIn>();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            String roleValue = user.getRoleValue();
             if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    queryWrapper.eq(SupStockIn::getHospitalId,hospitalId);
                }
            }
        }
        String code = (String) params.get("code");  //查询入库单号
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupStockIn::getCode,code);
        }
        String hospitalName = (String) params.get("hospitalName");  //查询医疗机构
        if (StringUtil.isNotBlank(hospitalName)){
            queryWrapper.like(SupStockIn::getHospitalName,hospitalName);
        }
        String deliveryName = (String) params.get("deliveryName");  //查询配送企业
        if (StringUtil.isNotBlank(deliveryName)){
            queryWrapper.like(SupStockIn::getDeliveryName,deliveryName);
        }
        queryWrapper.orderByDesc(SupStockIn::getStockInTime).orderByAsc(SupStockIn::getHospitalName);
        PageHelper.startPage(page, limit);
        List<SupStockIn> list = supStockInServiceImpl.list(queryWrapper);
        PageList<SupStockIn> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("上传入库单excel")
    @RequestMapping("/importCountryStockIn")
    public Result importCountryOrder(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_COUNTRY_STOCK_IN, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupStockInExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    @ApiOperation(value = "入库单详情")
    @RequestMapping("/show/{stockInCode}")
    public Result show(@PathVariable("stockInCode") String stockInCode) {
        try {
            Map<String, Object> map = new HashMap<>();
            LambdaQueryWrapper<SupStockIn> queryWrapper = new LambdaQueryWrapper<SupStockIn>().eq(SupStockIn::getCode, stockInCode);
            SupStockIn supStockIn = supStockInServiceImpl.getOne(queryWrapper);
            if(Objects.isNull(supStockIn)){
                return ResultUtil.error("无此入库单，请联系管理员");
            }else{
                map.put("stockInCode",stockInCode);
                List<Map<String, Object>> stockInItemDetail = supStockInItemServiceImpl.getStockInItemList(map);
                map.put("data",supStockIn);
                map.put("stockInItem",stockInItemDetail);
            }

            return ResultUtil.successToObject(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }
}

