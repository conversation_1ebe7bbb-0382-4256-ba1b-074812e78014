package com.inspur.ssp.supervise.controller;


import com.inspur.ssp.bsp.controller.BaseController;
import com.inspur.ssp.supervise.bean.dto.SupDrugDetailProvinceExcel;
import com.inspur.ssp.supervise.bean.entity.CdBatchLog;
import com.inspur.ssp.supervise.bean.entity.SupFile;
import com.inspur.ssp.supervise.bean.vo.SupDrugDetailExcel;
import com.inspur.ssp.supervise.bean.vo.SupHospitalDrugDetailExcel;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.ICdBatchLogService;
import com.inspur.ssp.supervise.service.ISupFileService;
import com.inspur.ssp.supervise.utils.excel.ExcelUtil;
import org.apache.xmlbeans.impl.util.Base64;
import org.jangod.iweb.core.annotation.AepSecurity;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.jboss.netty.handler.codec.http.HttpResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.CASE_INSENSITIVE;
import static java.util.regex.Pattern.compile;


/**
 * <p>
 * 文件 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@RestController
@RequestMapping("/file")
public class FileController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Value("${webdisk.uploadPath}")
    private String uploadPath;
    @Value("${webdisk.fileTypes}")
    private String fileTypes;

    @Autowired
    ICdBatchLogService cdBatchLogServiceImpl;

    @Autowired
    private ISupFileService supFileServiceImpl;


    @RequestMapping(value = "/upload/data", method = RequestMethod.POST)
    public Result uploadData(HttpServletRequest request) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录信息失效,请重新登录");
            }
            MultipartHttpServletRequest req = (MultipartHttpServletRequest) request;
            MultipartFile file = req.getFile("file");
            if (null == file) {
                return ResultUtil.error("请选择上传文件!");
            }
            String size = this.getParameter("size");
            SupFile supFile = uploadFile(user, file, size);//文件路径 文件类型  文件名称
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            String source = (String) params.get("source");
            String country = (String) params.get("country");
            if (StringUtil.isBlank(country)) {
                return ResultUtil.error("是否国家集中采购不能为空");
            }
            if (country.equals("0") && StringUtil.isBlank(source)) {
                return ResultUtil.error("国家来源不能为空");
            }
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_DRUG, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupDrugDetailExcel.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    /**
     * 导入省的药品目录
     * @param request
     * @return
     */
    @RequestMapping(value = "/upload/provinceData", method = RequestMethod.POST)
    public Result uploadProvinceData(HttpServletRequest request) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录信息失效,请重新登录");
            }
            MultipartHttpServletRequest req = (MultipartHttpServletRequest) request;
            MultipartFile file = req.getFile("file");
            if (null == file) {
                return ResultUtil.error("请选择上传文件!");
            }
            String size = this.getParameter("size");
            SupFile supFile = uploadFile(user, file, size);//文件路径 文件类型  文件名称
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            params.put("source","2");
            params.put("country","0");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), "provinceDrug", user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupDrugDetailProvinceExcel.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    /**
     * 收集医院的药品数据导入
     * @param request
     * @return
     */
    @RequestMapping(value = "/upload/hospitalDrugData", method = RequestMethod.POST)
    public Result hospitalDrugData(HttpServletRequest request) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("用户登录信息失效,请重新登录");
            }
            MultipartHttpServletRequest req = (MultipartHttpServletRequest) request;
            MultipartFile file = req.getFile("file");
            if (null == file) {
                return ResultUtil.error("请选择上传文件!");
            }
            String size = this.getParameter("size");
            SupFile supFile = uploadFile(user, file, size);//文件路径 文件类型  文件名称
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");

            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_HOSPITAL_DRUG, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupHospitalDrugDetailExcel.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Result upload(HttpServletRequest request) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                throw new Exception("用户登录信息失效,请重新登录");
            }
            MultipartHttpServletRequest req = (MultipartHttpServletRequest) request;
            MultipartFile file = req.getFile("file");
            if (null == file) {
                throw new Exception("请选择上传文件!");
            }
            String maxSize = this.getParameter("size");
            SupFile supFile = uploadFile(user, file, maxSize);
            return ResultUtil.successToObject(supFile);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }

    private SupFile uploadFile(IUser user, MultipartFile file, String maxSize) throws Exception {
        if (StringUtil.isNotBlank(maxSize)) {
            //说明有文件大小限制
            int iSize = Integer.parseInt(maxSize);
            int fileK = (int) (file.getSize() / 1024);
            if (fileK > iSize) {
                //超出上传范围，不允许上传
                throw new Exception("上传文件大小超出" + iSize + "K，不允许上传!");
            }
        }
        String id = Tools.genId() + "";
        String suf = getFileType(Objects.requireNonNull(file.getOriginalFilename()));
        if(StringUtil.isNotEmpty(fileTypes)){
            if(!fileTypes.toUpperCase().contains(suf.toUpperCase())){
                throw new Exception("非法文件，不允许上传");
            }
        }else{
            throw new Exception("未配置可上传文件类型");
        }
        String path = ResourceUtils.getURL("classpath:").getPath() + uploadPath;
        File newFile = new File(path);
        if (!newFile.exists()) {
            newFile.mkdirs();
        }
        String fileName = id + "." + suf;
        File cacheFile = new File(path, fileName);
        cacheFile.createNewFile();
        OutputStream os = new FileOutputStream(path + "/" + id + "." + suf);
        //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
        InputStream is = file.getInputStream();
        byte[] bytes = new byte[1024];
        int len;
        while ((len = is.read(bytes)) != -1) {
            os.write(bytes, 0, len);
        }
        os.flush();
        os.close();
        is.close();

        SupFile supFile = new SupFile();
        supFile.setId(Tools.genId() + "");
        supFile.setName(file.getOriginalFilename());
        supFile.setPath(path + "/" + id + "." + suf);
        supFile.setMime(suf);
        supFile.setSize(file.getSize());
        supFile.setCreator(user.getId());
        supFile.setCreationTime(new Date());
        supFileServiceImpl.save(supFile);


//        Map<String, Object> ret = new HashMap<String, Object>();
//        ret.put("docId", path + "/" + id + "." + suf);
//        ret.put("fileType", suf);
//        ret.put("name", file.getOriginalFilename());

        return supFile;
    }

    /**
     * 获取文件类型
     *
     * @param fileName
     * @return
     */
    public String getFileType(String fileName) {
        String fileType = "";
        if (fileName.indexOf(".") > 0) {
            fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        return fileType;
    }


    /**
     * 下载药品数据模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadDrugImportTemplate", method = RequestMethod.GET)
    public void downloadDeptImportExcel(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "药品数据导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/drugImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadInvoiceImportTemplate", method = RequestMethod.GET)
    public void downloadInvoiceImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "发票明细.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/invocieTemplate.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadBackOrderImportTemplate", method = RequestMethod.GET)
    public void downloadBackOrderImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "退货单明细.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/orderItemCancle.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 下载4+7配送导入模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadDeliveryImportTemplate", method = RequestMethod.GET)
    public void downloadDeliveryImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "4+7配送单导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/deliveryImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }


    /**
     * 下载4+7入库导入模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadStockInImportTemplate", method = RequestMethod.GET)
    public void downloadStockInImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "4+7入库单导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/stockInImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }




    /**
     * 下载4+7任务量模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadPurchaseImportTemplate", method = RequestMethod.GET)
    public void downloadPurchaseImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "4+7任务量导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/purchaseImport.xls");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }
    /**
     * 下载4+7执行量模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadPurchaseSpeedImportTemplate", method = RequestMethod.GET)
    public void downloadPurchaseSpeedImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "4+7任务量导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/purchaseSpeedImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 下载4+7订单导入模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadOrderImportTemplate", method = RequestMethod.GET)
    public void downloadOrderImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "订单导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/countryOrderImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 下载线下订单导入模板
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/downloadOfflineOrderTemplate", method = RequestMethod.GET)
    public void downloadOfflineOrderTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = null;
            String fileName = "线下订单导入模板.xlsx";
            file = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX + "config/offlineOrderImport.xlsx");
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            //response.addHeader("Content-Length", "");

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 下载文件
     *
     * @param request
     * @return
     */
    @AepSecurity(enable = false)
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public void download(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, Object> params = this.getRequestParams();
            String docId = (String) params.get("docId");
            SupFile supFile = supFileServiceImpl.getById(docId);
            File file = new File(supFile.getPath());
            String fileName = supFile.getName();
            if(StringUtil.isNotEmpty((String)params.get("fileName"))){
                fileName = (String)params.get("fileName");
            }
            response.setContentType("application/octet-stream");
            response.setHeader(
                    "Content-Disposition",
                    "attachment;"
                            + (new StringBuilder("filename=").append(encode(
                            request, fileName)).toString()));
            /*   response.addHeader("Content-Length", "");*/

            ServletOutputStream localServletOutputStream = response
                    .getOutputStream();
            //获取输入流 CommonsMultipartFile 中可以直接得到文件的流
            InputStream is = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = is.read(bytes)) != -1) {
                localServletOutputStream.write(bytes, 0, len);
            }
            is.close();
            localServletOutputStream.flush();
            localServletOutputStream.close();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    private String encode(HttpServletRequest request, String fileName)
            throws UnsupportedEncodingException {
        String agent = request.getHeader("USER-AGENT");
        if ((agent != null) && (-1 != agent.indexOf("MSIE")))
            return URLEncoder.encode(fileName, "UTF-8");
        if ((agent != null) && (-1 != agent.indexOf("Mozilla"))) {
            return "=?UTF-8?B?"
                    + new String(Base64.encode(fileName.getBytes("UTF-8")))
                    + "?=";
        }
        return fileName;
    }

    private String _uploadPath = null;

    public synchronized String getUploadPath(String subPath) {
        if (StringUtil.isEmpty(_uploadPath)) {
            String up = uploadPath;
            if (StringUtil.isEmpty(up)) {
                up = "tmp";
            }
            _uploadPath = up;
        }
        String fullPath = _uploadPath;
        if (!fullPath.endsWith("/")) {
            fullPath += "/";
        }
        if (subPath.startsWith("/")) {
            fullPath += subPath.substring(1);
        } else {
            fullPath += subPath;
        }
        return fullPath;
    }
    /**
     * 预览图片或者pdf
     * @param response
     * @param docId
     * @param fileName
     * @throws Exception
     */
    @AepSecurity(enable = false)
    @RequestMapping(value= "/previewByDocId",method = RequestMethod.GET)
    @ResponseBody
    public void previewByDocId(HttpServletResponse response, @RequestParam String docId, @RequestParam String fileName) throws Exception {
        OutputStream os = null;
        try{
        if (StringUtil.isNotBlank(docId)&&StringUtil.isNotBlank(fileName)) {
            SupFile supFile = supFileServiceImpl.getById(docId);
            Pattern wup = compile(".+\\.(jpg|gif|png|jpeg|bmp)$", CASE_INSENSITIVE);
            Matcher fm = wup.matcher(fileName.toLowerCase());
            String fileType = fileName.substring(fileName.lastIndexOf(".")+1);
            if(!fm.find()){
                response.setContentType("application/pdf;charset=UTF-8");
                response.setHeader("Content-Disposition", "inline;" + (new StringBuilder("filename=").append(encode(
                                request, fileName)).toString()));
            }else{
                if("png".equalsIgnoreCase(fileType)){
                    response.setContentType("image/png");
                }else if("bpm".equalsIgnoreCase(fileType)){
                    response.setContentType("image/bmp");
                }else {
                    response.setContentType("image/jpeg");
                }
            }
            if (supFile != null) {
                //读取本地图片输入流
                FileInputStream inputStream = new FileInputStream(supFile.getPath());
                os = response.getOutputStream();
                //byte数组用于存放图片字节数据
                byte[] b = new byte[1024];
                int n;
                while ((n = inputStream.read(b)) != -1) {
                    os.write(b, 0, n);
                    os.flush();
                }
                //记得关闭输入流
                inputStream.close();
            }else{
                response.sendError(HttpResponseStatus.NOT_FOUND.getCode(), "文件" + fileName + "不存在");
            }
        }

    } catch (IOException e) {
        try {
            response.sendError(HttpResponseStatus.NOT_FOUND.getCode(), "文件" + fileName + "不存在");
        } catch (Exception ee) {
            logger.error(ee.getMessage(), ee);
        }
    } finally {
        try{
            if(os != null){
                os.close();
            }
        }
        catch (Exception ex){
            logger.error(ex.getMessage(),ex);
        }
    }
    }
}
