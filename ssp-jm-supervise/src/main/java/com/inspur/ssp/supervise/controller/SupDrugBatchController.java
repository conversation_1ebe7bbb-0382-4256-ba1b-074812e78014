package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.entity.SupDrugBatch;
import com.inspur.ssp.supervise.service.ISupDrugBatchService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.jangod.iweb.util.Tools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.jangod.iweb.core.action.AbstractController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-15
 */
@RestController
@RequestMapping("/supervise/supDrugBatch")
public class SupDrugBatchController extends AbstractController {
    @Autowired
    private  ISupDrugBatchService supDrugBatchServiceImpl;

    @RequestMapping("/getBatchList")
    public Result getBatchList() {
        QueryWrapper<SupDrugBatch> queryWrapper = new QueryWrapper<SupDrugBatch>();
        queryWrapper.eq("status","1");
        queryWrapper.orderByDesc("sort_order");
        List<SupDrugBatch> list = supDrugBatchServiceImpl.list(queryWrapper);
        return ResultUtil.successToList(list);
    }

    @ApiOperation(value = "获取批次列表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "batchName", value = "名称", required = false, dataType = "String"),
            @ApiImplicitParam(name = "code", value = "编码", required = false, dataType = "String"),
    })
    @RequestMapping("/list")
    public Result list(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        LambdaQueryWrapper<SupDrugBatch> queryWrapper = new LambdaQueryWrapper<SupDrugBatch>()
                .ne(SupDrugBatch::getStatus,"-1").orderByDesc(SupDrugBatch::getSortOrder);
        //查询字段
        String code = (String) params.get("code");  //查询编码
        String batchName = (String) params.get("batchName");   //查询名称
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupDrugBatch::getCode,code);
        }
        if (StringUtil.isNotBlank(batchName)){
            queryWrapper.like(SupDrugBatch::getBatchName, batchName);
        }
        PageHelper.startPage(page, limit);
        List<SupDrugBatch> list = supDrugBatchServiceImpl.list(queryWrapper);
        PageList<SupDrugBatch> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }


    @ApiOperation("保存批次")
    @RequestMapping("/save")
    public Result save(@ApiParam(value = "supDrugBatch批次对象", required = true) SupDrugBatch supDrugBatch) {
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        SupDrugBatch batch = supDrugBatchServiceImpl.getOne(new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getCode, supDrugBatch.getCode()));
        if (batch != null && StringUtil.isNotBlank(batch.getId())) {
            return ResultUtil.error("批次编码已存在");
        }
        SupDrugBatch batchName = supDrugBatchServiceImpl.getOne(new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getBatchName, supDrugBatch.getBatchName()));
        if (batchName != null && StringUtil.isNotBlank(batchName.getId())) {
            return ResultUtil.error("批次名称已存在");
        }

        supDrugBatch.setStatus("1");
        supDrugBatch.setId(String.valueOf(Tools.genId()));
        supDrugBatch.setCreationTime(new Date());
        supDrugBatch.setLastModifiedTime(new Date());
        supDrugBatchServiceImpl.save(supDrugBatch);
        return ResultUtil.success("保存成功");

    }
    @ApiOperation(value = "修改批次")
    @RequestMapping("/update")
    public Result update(@ApiParam(value = "supDrugBatch批次对象", required = true) SupDrugBatch supDrugBatch) {
        SupDrugBatch batch = supDrugBatchServiceImpl.getOne(new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getCode, supDrugBatch.getCode()).ne(SupDrugBatch::getId,supDrugBatch.getId()));
        if (batch != null && StringUtil.isNotBlank(batch.getId())) {
            return ResultUtil.error("批次编码已存在");
        }

        SupDrugBatch batchName = supDrugBatchServiceImpl.getOne(new QueryWrapper<SupDrugBatch>().lambda().eq(SupDrugBatch::getBatchName, supDrugBatch.getBatchName()).ne(SupDrugBatch::getId,supDrugBatch.getId()));
        if (batchName != null && StringUtil.isNotBlank(batchName.getId())) {
            return ResultUtil.error("批次名称已存在");
        }

        supDrugBatch.setLastModifiedTime(new Date());
        if(this.getCurrentUser() == null){
            ResultUtil.error("获取用户失败，请重新登录");
        }
        supDrugBatchServiceImpl.updateById(supDrugBatch);
        return ResultUtil.success("修改成功");
    }

    @ApiOperation(value = "批次详细信息")
    @RequestMapping("/info/{supDrugBatchId}")
    public Result info(@ApiParam("批次id") @PathVariable("supDrugBatchId") String supDrugBatchId) {
        SupDrugBatch supDrugBatch = supDrugBatchServiceImpl.getById(supDrugBatchId);
        if (supDrugBatch == null) {
            return ResultUtil.error("批次ID不存在");
        }
        return ResultUtil.successToObject(supDrugBatch);
    }

    @ApiOperation(value = "删除批次")
    @RequestMapping("/delete/{supDrugBatchId}")
    public Result deleteById(@ApiParam("批次ID") @PathVariable("supDrugBatchId") String supDrugBatchId) {
        SupDrugBatch supDrugBatch = supDrugBatchServiceImpl.getById(supDrugBatchId);
        supDrugBatch.setStatus("-1");
        supDrugBatchServiceImpl.updateById(supDrugBatch);
        return ResultUtil.success("删除成功");
    }
    @ApiOperation(value = "更新批次状态")
    @RequestMapping("/updateStatus")
    public  Result updateStatus(@ApiParam(value = "supDrugBatch批次对象",required = true) SupDrugBatch supDrugBatch){
        UpdateWrapper<SupDrugBatch> updateWrapper = new UpdateWrapper<SupDrugBatch>();
        updateWrapper.eq("ID", supDrugBatch.getId());
        boolean update = supDrugBatchServiceImpl.update(supDrugBatch, updateWrapper);
        if(update){
            return ResultUtil.success("操作成功");
        }else{
            return ResultUtil.error("操作失败");
        }
    }

}
