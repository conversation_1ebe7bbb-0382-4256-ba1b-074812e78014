package com.inspur.ssp.supervise.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.inspur.ssp.supervise.bean.dto.purchase.SupInvoiceExportDto;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.constant.Constant;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.utils.excel.ExcelUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jangod.iweb.core.bean.IUser;
import org.jangod.iweb.core.bean.PageList;
import org.jangod.iweb.core.bean.Result;
import org.jangod.iweb.util.ResultUtil;
import org.jangod.iweb.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.jangod.iweb.core.action.AbstractController;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 发票表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@RestController
@RequestMapping("/supervise/supInvoice")
public class SupInvoiceController extends AbstractController {

    @Autowired
    private ISupHospitalUserService supHospitalUserServiceImpl;
    @Autowired
    private ISupInvoiceService supInvoiceServiceImpl;
    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;
    @Autowired
    private ISupFileService supFileServiceImpl;
    @Autowired
    private ICdBatchLogService cdBatchLogServiceImpl;
    @Value("${adminRole}")
    private String adminRole;
    @Value("${socialSecurityRole}")
    private String socialSecurityRole;
    @Value("${medicalRole}")
    private String medicalRole;

    @ApiOperation(value = "获取发票单列表/根据用户权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "页大小", required = true, dataType = "int"),
            @ApiImplicitParam(name = "no", value = "发票单号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "hospitalName", value = "医疗机构", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deliveryName", value = "配送企业", required = false, dataType = "String"),
    })
    @RequestMapping("/getInvoiceList")
    public Result getInvoiceList(@RequestParam int page, @RequestParam int limit) {
        Map<String, Object> params = this.getRequestParams();
        IUser user = (IUser) this.getCurrentUser();
        if (user == null) {
            return ResultUtil.error("用户登录超时，请重新登录。");
        }else{
            String roleValue = user.getRoleValue();
            if(!roleValue.contains(adminRole)&&!roleValue.contains(socialSecurityRole)&&!roleValue.contains(medicalRole)){
                SupHospitalUser hospitalUser = supHospitalUserServiceImpl.getOne(new QueryWrapper<SupHospitalUser>().eq("USER_ID",user.getId()));
                if(Objects.isNull(hospitalUser)){
                    return ResultUtil.errorToObject("用户未绑定医院，请联系管理员绑定。");
                }else{
                    String hospitalId = hospitalUser.getHospitalId();
                    params.put("hospitalId", hospitalId);
                }
            }
        }
        String startTime=(String) params.get("invoiceDate[0]");
        String endTime=(String) params.get("invoiceDate[1]");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            params.put("startTime", startTime);
            params.put("endTime", endTime);
        }
        /*String code = (String) params.get("code");  //查询发票单号
        String invoiceCode = (String) params.get("invoiceCode");
        if(StringUtil.isNotBlank(invoiceCode)){
            code=invoiceCode;
        }
        if (StringUtil.isNotBlank(code)){
            queryWrapper.like(SupInvoice::getNo,code);
        }
        String hospitalName = (String) params.get("hospitalName");  //查询医疗机构
        if (StringUtil.isNotBlank(hospitalName)){
            queryWrapper.like(SupInvoice::getHospitalName,hospitalName);
        }
        String payStatus = (String) params.get("payStatus");
        if (StringUtil.isNotBlank(payStatus)){
            queryWrapper.eq(SupInvoice::getStatus,payStatus);
        }
        String deliveryName = (String) params.get("deliveryName");  //查询配送企业
        if (StringUtil.isNotBlank(deliveryName)){
            queryWrapper.like(SupInvoice::getDeliveryName,deliveryName);
        }
        queryWrapper.orderByDesc(SupInvoice::getInvoiceDate).orderByAsc(SupInvoice::getHospitalName);*/

        PageHelper.startPage(page, limit);
        List<SupInvoice> list = supInvoiceServiceImpl.getList(params);
        PageList<SupInvoice> selectPage = new PageList<>(list);
        return ResultUtil.successToList(selectPage);
    }

    @ApiOperation("上传发票单excel")
    @RequestMapping("/importCountryInvoice")
    public Result importCountryOrder(String docId) {
        try {
            IUser user = this.getCurrentUser();
            if (user == null) {
                return ResultUtil.error("获取用户失败，请重新登录");
            }
            SupFile supFile = supFileServiceImpl.getById(docId);
            if (Objects.isNull(supFile)) {
                return ResultUtil.error("未检测到上传的文件。");
            }
            Map<String, Object> params = this.getRequestParams();
            String operationType = (String) params.get("type");
            if (StringUtil.isBlank(operationType)) {
                operationType = Constant.OPERATION_TYPE_ADD; //默认为新增
            }
            params.put("userId", user.getId());
            CdBatchLog cdBatchLog = cdBatchLogServiceImpl.save(supFile.getId(), supFile.getName(), Constant.DATA_TYPE_COUNTRY_INVOICE, user, operationType);//更新批次表-导入待处理
            //数据导入处理
            ExcelUtil.importExcel(supFile.getPath(), SupInvoiceExportDto.class, cdBatchLog, 1, params);
            return ResultUtil.successToObject(cdBatchLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage(), ex);
            return ResultUtil.errorToObject(ex.getMessage());
        }
    }


    @ApiOperation(value = "发票单详情")
    @RequestMapping("/show/{invoiceCode}")
    public Result show(@PathVariable("invoiceCode") String invoiceCode) {
        try {
            Map<Object, Object> map = new HashMap<>();
            LambdaQueryWrapper<SupInvoice> queryWrapper = new LambdaQueryWrapper<SupInvoice>().eq(SupInvoice::getNo, invoiceCode);
            SupInvoice supInvoice = supInvoiceServiceImpl.getOne(queryWrapper,false);
            if(Objects.isNull(supInvoice)){
                return ResultUtil.error("无此发票单，请联系管理员");
            }else{
                List<Map<String, Object>> invoiceItemDetail = supInvoiceItemServiceImpl.getInvoiceItemDetail(invoiceCode);
                map.put("data",supInvoice);
                map.put("invoiceItem",invoiceItemDetail);
            }

            return ResultUtil.successToObject(map);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultUtil.error("系统异常" + e.getMessage());
        }
    }





}

