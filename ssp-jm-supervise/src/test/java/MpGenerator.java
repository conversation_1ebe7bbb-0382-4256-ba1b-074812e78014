import com.baomidou.mybatisplus.annotation.DbType;
import org.jangod.iweb.util.generator.Config;
import org.jangod.iweb.util.generator.MybatisPlusGenerator;

import java.io.IOException;

public class MpGenerator {
    public static final String dbUserName="root";
    public static final String dbPassword="root";
    public static final String dbUrl = "************************************************************************************************************************************************";
    public static final DbType dbType = DbType.MYSQL;
    public static final String dbDriver = "com.mysql.jdbc.Driver";
    public static final String author="zjx";
    public static final String packageName = "com.inspur.ssp";
    public static final String moduleName="supervise";

    public static void main(String[] args) throws IOException {
//        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
//        System.out.print("请输入表名:");
//        String tableName = br.readLine();
        String tableName = "sup_contract_info";
        String projectPath = System.getProperty("user.dir")+"/ssp-jm-supervise";
        Config config = new Config();
        config.setAuthor(author);
        config.setDbDriver(dbDriver);
        config.setDbPassword(dbPassword);
        config.setDbUserName(dbUserName);
        config.setDbType(dbType);
        config.setDbUrl(dbUrl);
        config.setPackageName(packageName);
        config.setModuleName(moduleName);
        MybatisPlusGenerator.execute(config,projectPath,new String[]{tableName});
    }
}
