package excel;

import com.inspur.StartupApplication;
import lombok.Data;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2020-4-28
 */
@SpringBootTest(classes = StartupApplication.class)
@RunWith(SpringRunner.class)
@ComponentScan("org.jangod,com.inspur")
@MapperScan("com.inspur")
@ServletComponentScan("org.jangod,com.inspur")
@EnableTransactionManagement
@EnableScheduling
@Data
public class ExceTest {

    @Value("${server.port}")
    private String port;

    @Test
    public void excelImport() throws Exception {
        String fileName = "C:\\Users\\<USER>\\Desktop\\部门数据导入模板.xls";
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet
//        EasyExcel.read(fileName, Drug.class, new ExcelImportDataListener<Drug>("704725250759196672")).sheet().doRead();

    }
}
