import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.lang.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/10/12
 */
public class webServiceTest {

    /**
     * 访问服务
     *
     * @param wsdl   wsdl地址
     * @param ns     命名空间
     * @param method 方法名
     * @param list   参数
     * @return
     * @throws Exception
     */
    public synchronized static String accessService(String wsdl, String ns, String method, List<String> list, String result) throws Exception {
        StringBuffer stringBuffer = new StringBuffer();

        //拼接参数
        for (int i = 0; i < list.size(); i++) {
            stringBuffer.append("<arg" + i + ">" + list.get(i) + "</arg" + i + ">");
        }

        String soapResponseData = "";
        //拼接SOAP
        StringBuffer soapRequestData = new StringBuffer("");

        soapRequestData.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\""+ns+"\">");
        soapRequestData.append("<soapenv:Header/>");
        soapRequestData.append("<soapenv:Body>");
        soapRequestData.append("<web:" + method + ">");
        soapRequestData.append(stringBuffer);
        soapRequestData.append("</web:" + method + ">");
        soapRequestData.append("</soapenv:Body>" + "</soapenv:Envelope>");
        PostMethod postMethod = new PostMethod(wsdl);
        // 然后把Soap请求数据添加到PostMethod中
        byte[] b = null;
        InputStream is = null;
        try {
            b = soapRequestData.toString().getBytes("utf-8");
            is = new ByteArrayInputStream(b, 0, b.length);
            RequestEntity re = new InputStreamRequestEntity(is, b.length, "text/xml; charset=UTF-8");
            postMethod.setRequestEntity(re);
            HttpClient httpClient = new HttpClient();
            int status = httpClient.executeMethod(postMethod);

            if (status == 200) {
                soapResponseData = getMesage(postMethod.getResponseBodyAsString(), result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                is.close();
            }
        }
        return soapResponseData;
    }

    public static String getMesage(String soapAttachment, String result) {
        if (result == null) {
            return null;
        }
        if (soapAttachment != null && soapAttachment.length() > 0) {
            int begin = soapAttachment.indexOf("<return>");
            begin = soapAttachment.indexOf(">", begin);
            int end = soapAttachment.indexOf("</return>");
            String str = soapAttachment.substring(begin + 1, end);
            str = str.replaceAll("<", "<");
            str = str.replaceAll(">", ">");
            return str;
        } else {
            return "";
        }
    }

    /**
     * @param args
     */
    public static void main(String[] args) throws Exception {
        /*try {

            //获取data
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("userCode","hsybj");
            String dateStr = "1988-05-17 00:00:00";
            String dateStr1 = "2020-08-27 00:00:00";
            jsonObject.put("startDate",dateStr);
            jsonObject.put("endDate",dateStr1);
            String data = jsonObject.toString();


            //获取sign
            String str="S<EpEFX1K50iEqq@$HDxjx?"+data;
            String encName="SHA-1";
            byte[] digest = null;
            if (StringUtils.isBlank(encName)) {
                encName = "SHA-1";
            }
            try {
                MessageDigest md=MessageDigest.getInstance(encName); md.update(str.getBytes("UTF-8"));
                digest = md.digest();
            } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) { e.printStackTrace();
            }
            char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
            char[] resultCharArray = new char[digest.length * 2]; int index = 0;
            for (byte b : digest) {
                resultCharArray[index++] = hexDigits[b >>> 4 & 0xf]; resultCharArray[index++] = hexDigits[b & 0xf];
            }
            String sign = new String(resultCharArray);
            List<String> list = new ArrayList<>();
            list.add(data);
            list.add(sign);

            String wsdl = "https://tradejm_api.qywgpo.com/webservice/hsInterfaceWsService?wsdl";
            String ns = "http://webservice.trade.gpo.yaoling.com";
            String method = "getDrugsUsable";
            String response = accessService(wsdl, ns, method, list, "result");
            System.out.println(response);
            JSONObject object = JSONObject.parseObject(response);
            System.out.println(object);
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        getData("2022-01-01 00:00:00","2022-12-08 23:59:59","getFourSevenBatchInfo");
    }

    /**
     * 根据接口获取数据
     * @param startDate
     * @param endDate
     * @param interfaceName
     * @return
     * @throws Exception
     */
    public static JSONObject getData(String startDate,String endDate,String interfaceName) throws Exception {
        //String webServiceWsdl = "https://gateway.api.qywgpo.com/tradejm/webservice/jmSuperviseInterfaceService?wsdl";
        String webServiceWsdl = "https://gateway.qywgpo.com/tradejm/webservice/jmSuperviseInterfaceService?wsdl";

        String ns = "http://webservice.trade.gpo.yaoling.com";


        //获取data
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("userCode","jmybj");
    /*    String dateStr = "2020-11-05 00:00:00";
        String dateStr1 = "2020-11-05 16:00:00";*/
//        jsonObject.put("startDate",startDate);
//        jsonObject.put("endDate",endDate);
        jsonObject.put("orderCode","D23072400299");
        String data = jsonObject.toString();


        //获取sign
        String str="S<EpEFX1K50iEqq@$HDxjx?"+data;
        String encName="SHA-1";
        byte[] digest = null;
        if (StringUtils.isBlank(encName)) {
            encName = "SHA-1";
        }
        try {
            MessageDigest md=MessageDigest.getInstance(encName); md.update(str.getBytes("UTF-8"));
            digest = md.digest();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) { e.printStackTrace();
        }
        char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
        char[] resultCharArray = new char[digest.length * 2]; int index = 0;
        for (byte b : digest) {
            resultCharArray[index++] = hexDigits[b >>> 4 & 0xf]; resultCharArray[index++] = hexDigits[b & 0xf];
        }
        String sign = new String(resultCharArray);

        StringBuffer stringBuffer = new StringBuffer();

        //拼接参数
        stringBuffer.append("<arg" + 0 + ">" + data + "</arg" + 0 + ">").append("<arg" + 1 + ">" + sign + "</arg" + 1 + ">");

        String soapResponseData = "";
        //拼接SOAP
        StringBuffer soapRequestData = new StringBuffer("");

        soapRequestData.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\""+ns+"\">");
        soapRequestData.append("<soapenv:Header/>");
        soapRequestData.append("<soapenv:Body>");
        soapRequestData.append("<web:" + interfaceName + ">");
        soapRequestData.append(stringBuffer);
        soapRequestData.append("</web:" + interfaceName + ">");
        soapRequestData.append("</soapenv:Body>" + "</soapenv:Envelope>");
        System.out.println(JSONObject.toJSON(soapRequestData));
        PostMethod postMethod = new PostMethod(webServiceWsdl);
        // 然后把Soap请求数据添加到PostMethod中
        byte[] b = null;
        InputStream is = null;
        try {
            b = soapRequestData.toString().getBytes("utf-8");
            is = new ByteArrayInputStream(b, 0, b.length);
            RequestEntity re = new InputStreamRequestEntity(is, b.length, "text/xml; charset=UTF-8");
            postMethod.setRequestEntity(re);
            HttpClient httpClient = new HttpClient();
            int status = httpClient.executeMethod(postMethod);

            if (status == 200) {
                soapResponseData = getMesage(postMethod.getResponseBodyAsString(), "result");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                is.close();
            }
        }
        JSONObject resultObject = JSONObject.parseObject(soapResponseData);
        System.out.println(resultObject);
        return resultObject;
    }
}
