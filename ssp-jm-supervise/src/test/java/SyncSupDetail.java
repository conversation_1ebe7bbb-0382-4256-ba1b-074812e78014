import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gdsyj.webservice.cbo.RespContractData;
import com.gdsyj.webservice.common.CommandidEnum;
import com.github.pagehelper.PageHelper;
import com.inspur.StartupApplication;
import com.inspur.ssp.supervise.bean.entity.SupContractInfo;
import com.inspur.ssp.supervise.bean.entity.SupDrugDetail;
import com.inspur.ssp.supervise.bean.entity.SupOrderItem;
import com.inspur.ssp.supervise.service.ISupContractInfoService;
import com.inspur.ssp.supervise.service.ISupDrugDetailService;
import com.inspur.ssp.supervise.service.ISupOrderItemService;
import com.inspur.ssp.supervise.task.CountryPurchaseTask;
import com.inspur.ssp.supervise.task.GDInterfaceTask;
import com.inspur.ssp.supervise.task.ShenZhenInterfaceTask;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.util.BeanUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@SpringBootTest(classes = StartupApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class SyncSupDetail {
    @Autowired
    private GDInterfaceTask gdInterfaceTask;

    @Autowired
    private ShenZhenInterfaceTask shenZhenInterfaceTask;

    @Autowired
    private CountryPurchaseTask countryPurchaseTask;

    @Autowired
    @Qualifier("supOrderItemServiceImpl")
    private ISupOrderItemService orderItemService;

    @Autowired
    @Qualifier("supDrugDetailServiceImpl")
    private ISupDrugDetailService detailService;

    @Autowired
    @Qualifier("supContractInfoServiceImpl")
    private ISupContractInfoService supContractInfoService;

    @Autowired
    @Qualifier("supDrugDetailServiceImpl")
    private ISupDrugDetailService supDrugDetailService;


    /**
     * 同步药品信息
     * */
    @Test
    public void syncDetail() throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = simpleDateFormat.parse("2021-01-01 00:00:00");
        Date endTime = simpleDateFormat.parse("2021-01-02 00:00:00");
        gdInterfaceTask.addInterfaceLog(CommandidEnum.COMMANID_40005.getCode(),startTime,endTime,"1","10");
        gdInterfaceTask.insertDrugData();
    }

//    @Test
//    public void syncT40009(){
//        QueryWrapper<SupOrderItem> supOrderItemQueryWrapper = new QueryWrapper<>();
//        supOrderItemQueryWrapper.lambda().eq(SupOrderItem::getCountryBatch,"12");
//        List<SupOrderItem> supOrderItemList = orderItemService.list(supOrderItemQueryWrapper);
//        for (SupOrderItem supOrderItem : supOrderItemList) {
//            gdInterfaceTask.addDeliveryAndStockInLog(supOrderItem.getOrderNum());
//        }
//    }

    @Test
    public void syncT40009Data(){
        gdInterfaceTask.insertDeliveryAndStockInData();
    }

    @Test
    public void syncTest05Data () {


    }

    /**
     * 深圳药品医保码同步
     * */
    @Test
    public void syncSzTask() throws Exception {
        QueryWrapper<SupContractInfo> supContractInfoQueryWrapper = new QueryWrapper<>();
        supContractInfoQueryWrapper.lambda().eq(SupContractInfo::getSource,"1");
        int page = 1;
        int limit = 1000;
        PageHelper.startPage(page, limit);
        List<SupContractInfo> supContractInfos = supContractInfoService.list(supContractInfoQueryWrapper);
        while(supContractInfos.size() != 0){

            for (int i = 0;i < supContractInfos.size();i++) {
                String countryYibaoDrugsCode = "";
                JSONObject obj = shenZhenInterfaceTask.getData(null, null, "getAllContract", supContractInfos.get(i).getContractId());
                if (obj != null && obj.getBoolean("success")) {
                    JSONArray mx = obj.getJSONObject("data").getJSONArray("mx");
                    if (mx != null && mx.size() > 0) {
                        JSONObject contractInfo = mx.getJSONObject(0);
                        JSONArray contractDetailInfo = contractInfo.getJSONArray("mx");
                        for (int k = 0; k < contractDetailInfo.size(); k++) {
                            if (contractDetailInfo.getJSONObject(k).getString("contractItemNo").equals(supContractInfos.get(i).getContractDetailId())) {
                                countryYibaoDrugsCode = contractDetailInfo.getJSONObject(k).getString("countryYibaoDrugsCode");
                                break;
                            }
                        }
                    }
                }

                if (StringUtils.isNotBlank(countryYibaoDrugsCode)) {
                    SupDrugDetail supDrugDetail = supDrugDetailService.getById(supContractInfos.get(i).getDrugDetailId());
                    supDrugDetail.setMedicalInsuranceCode(countryYibaoDrugsCode);
                    supDrugDetailService.updateById(supDrugDetail);
                }
            }

            page++;
            PageHelper.startPage(page, limit);
            supContractInfos = supContractInfoService.list(supContractInfoQueryWrapper);
        }

    }

    /**
     * 广东药品医保码同步
     * */
    @Test
    public void syncGdTask() throws Exception {
        QueryWrapper<SupContractInfo> supContractInfoQueryWrapper = new QueryWrapper<>();
        supContractInfoQueryWrapper.lambda().eq(SupContractInfo::getSource,"2");
        int page = 1;
        int limit = 1000;
        PageHelper.startPage(page, limit);
        List<SupContractInfo> supContractInfos = supContractInfoService.list(supContractInfoQueryWrapper);
        while(supContractInfos.size() != 0){

            for (int i = 0;i < supContractInfos.size();i++) {
                try {
                    String countryYibaoDrugsCode = "";
                    JSONObject obj = gdInterfaceTask.getContraceInfo(1, null, null, CommandidEnum.COMMANID_40005.getCode(), supContractInfos.get(i).getContractId());
                    if ("0000".equals(obj.getString("code"))) {
                        JSONObject object = obj.getJSONArray("data").getJSONObject(0);
                        RespContractData supGdDrugDto = BeanUtil.convert(object, RespContractData.class);
                        if (supGdDrugDto.getContractdetailid().equals(supContractInfos.get(i).getContractDetailId())) {
                            countryYibaoDrugsCode = supGdDrugDto.getNhiGroupCodeFix();
                        }
                        if (StringUtils.isNotBlank(countryYibaoDrugsCode)) {
                            SupDrugDetail supDrugDetail = supDrugDetailService.getById(supContractInfos.get(i).getDrugDetailId());
                            supDrugDetail.setMedicalInsuranceCode(countryYibaoDrugsCode);
                            supDrugDetailService.updateById(supDrugDetail);
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }

            page++;
            PageHelper.startPage(page, limit);
            supContractInfos = supContractInfoService.list(supContractInfoQueryWrapper);
        }
    }

//    @Test
//    public void syncGdCountryPurchaseTask(){
//        countryPurchaseTask.updatePurchaseSchedule1("148");
//    }
}
