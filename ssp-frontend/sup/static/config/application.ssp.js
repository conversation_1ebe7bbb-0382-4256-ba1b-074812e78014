//生产环境
var configPro = {
    active: "ssp",
    context: "ssp",
    version: "1.0",
    baseContext: "/sspapi",//服务上下文
    base_url: "",
    title: "鹤山市城乡居民医保精准参保系统",
    indexPath: "/index",
    login_type: "test",                   //登录类型
    mqtt_port: 8083,
    mqtt_url: "127.0.0.1",//mqtt服务器地址
    mqtt_userName: "admin",
    mqtt_password: "public",
    treeCompetence: ['DEPT_ADMIN', 'STREET_ADMIN', "100002"],//行政机构树权限
    depends: [
        "ssp"
    ],
    appInfoUrl: "/sspapi/bsp/pubUser/getUserInfo",
    loginUrl: "/sspapi/bsp/pubUser/login",
    deptParentCode: "100001",
    streetParentCode: "100002",
    smsRole: ['SUPER_ADMIN', 'DATA_ADMIN'],
    app_key: "A0001",
    app_security: "A0001",
}
//公网环境
var configTest = {
    active : "ssp",
    context:"ssp",
    version:"1.0",
    baseContext:"/sspapi",//服务上下文
    base_url:"",
    title:"深圳权责清单系统",
    indexPath:"/index",
    login_type:"test",                   //登录类型
    mqtt_port:52083,
    mqtt_url:"14.116.155.132",//mqtt服务器地址
    mqtt_userName:"admin",
    mqtt_password:"public",
    treeCompetence:['DEPT_ADMIN','STREET_ADMIN',"100002"],//行政机构树权限
    depends:[
        "ssp"
    ],
    appInfoUrl:"/sspapi/bsp/pubUser/getUserInfo",
    loginUrl:"/sspapi/bsp/pubUser/login",
    deptParentCode:"100001",
    streetParentCode:"100002",
    smsRole:['SUPER_ADMIN','DATA_ADMIN'],
    app_key:"A0001",
    app_security:"A0001",
};
var configLocal = {
    active : "ssp",
    context:"ssp",
    version:"1.0",
    baseContext:"/local/sspapi",//服务上下文
    base_url:"",
    title:"鹤山市城乡居民医保精准参保系统",
    indexPath:"/index",
    login_type:"ssp",                   //登录类型
    mqtt_port:8083,
    mqtt_url:"120.78.231.152",//mqtt服务器地址
    mqtt_userName:"admin",
    mqtt_password:"public",
    treeCompetence:['DEPT_ADMIN','STREET_ADMIN',"100002"],//行政机构树权限
    depends:[
        "ssp"
    ],
    //loginUrl:"/local/sspapi/sso/bsp/login",
    appInfoUrl:"/local/sspapi/bsp/pubUser/getUserInfo",
    loginUrl:"/local/sspapi/bsp/pubUser/login",
    deptParentCode:"100001",
    streetParentCode:"100002",
    smsRole:['SUPER_ADMIN','DATA_ADMIN'],
    app_key:"A0001",
    app_security:"A0001",
};
return configLocal;