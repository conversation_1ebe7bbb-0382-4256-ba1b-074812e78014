(window.webpackJsonp=window.webpackJsonp||[]).push([[101],{Cag2:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("对接方")]),t("el-select",{attrs:{placeholder:"请选择对接方"},on:{change:e.changeSelect},model:{value:e.batchParams.codeType,callback:function(t){e.$set(e.batchParams,"codeType",t)},expression:"batchParams.codeType"}},[t("el-option",{attrs:{label:"深圳接口",value:"shenzhen"}}),t("el-option",{attrs:{label:"广东接口",value:"GD"}}),t("el-option",{attrs:{label:"广州接口",value:"GZ"}}),t("el-option",{attrs:{label:"广东耗材",value:"GDHC"}})],1),t("span",[e._v("导入状态")]),t("el-select",{attrs:{placeholder:"请选择导入状态"},model:{value:e.batchParams.importFlag,callback:function(t){e.$set(e.batchParams,"importFlag",t)},expression:"batchParams.importFlag"}},[t("el-option",{attrs:{label:"失败",value:"2"}})],1),t("span",[e._v("导入时间")]),t("el-date-picker",{staticStyle:{width:"20%","margin-right":"10px"},attrs:{"unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"center"},model:{value:e.batchParams.createTime,callback:function(t){e.$set(e.batchParams,"createTime",t)},expression:"batchParams.createTime"}}),e.batchParams.codeType?t("span",[e._v("接口类型")]):e._e(),"shenzhen"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"订单明细",value:"getAllOrder"}}),t("el-option",{attrs:{label:"配送明细",value:"getDeliverBills"}}),t("el-option",{attrs:{label:"发票明细",value:"getInvoice"}}),t("el-option",{attrs:{label:"入库明细",value:"getStockinBills"}}),t("el-option",{attrs:{label:"药品目录",value:"getDrugsUsable"}})],1):e._e(),"GD"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"订单明细",value:"40008"}}),t("el-option",{attrs:{label:"配送与入库明细",value:"40009"}}),t("el-option",{attrs:{label:"发票明细",value:"400010"}}),t("el-option",{attrs:{label:"药品目录",value:"40005"}})],1):e._e(),"GZ"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"订单明细",value:"/order/getPurchaseOrderInfo"}}),t("el-option",{attrs:{label:"配送与发票明细",value:"/order/getDistributeInfo"}}),t("el-option",{attrs:{label:"入库明细",value:"/order/getWarehouseInfo"}}),t("el-option",{attrs:{label:"药品目录",value:"/contract/getContractInfo"}})],1):e._e(),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.batchList,height:e.tableHeight}},[t("el-table-column",{attrs:{type:"index",width:"50",label:"序号"}}),t("el-table-column",{attrs:{label:"对接方",align:"center",prop:"codeType"},scopedSlots:e._u([{key:"default",fn:function(a){return["shenzhen"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("深圳市对接")])],1):e._e(),"GD"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("广东省对接")])],1):e._e(),"GZ"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("广州市对接")])],1):e._e(),"GDHC"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("广州省耗材对接")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"数据",align:"left",prop:"returnValue"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-popover",{attrs:{placement:"top-start",title:"数据(点击一键复制即可复制以下数据)",width:"500",trigger:"hover",content:a.row.returnValue}},[t("div",{staticStyle:{"text-overflow":"ellipsis",overflow:"hidden",display:"-webkit-box","-webkit-line-clamp":"2","line-clamp":"2","-webkit-box-orient":"vertical"},attrs:{slot:"reference"},slot:"reference"},[e._v("\n                "+e._s(a.row.returnValue)+"\n              ")])])]}}])}),t("el-table-column",{attrs:{label:"导入状态",align:"center",prop:"importFlag"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.importFlag?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("成功")])],1):e._e(),"2"==a.row.importFlag?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("失败")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"导入失败原因",align:"center",prop:"message","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{on:{click:function(t){return e.copyToClipboard(a.row.message)}}},[e._v(e._s(a.row.message))])]}}])}),t("el-table-column",{attrs:{label:"接口类型",align:"center",prop:"code"},scopedSlots:e._u([{key:"default",fn:function(a){return["getAllOrder"==a.row.code||"40008"==a.row.code||"/order/getPurchaseOrderInfo"==a.row.code||"/order/getPurchaseOrderDetail"==a.row.code||"6032"==a.row.code?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("订单明细")])],1):e._e(),"getDeliverBills"==a.row.code||"6033"==a.row.code?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("配送明细")])],1):e._e(),"getInvoice"==a.row.code||"400010"==a.row.code||"6034"==a.row.code?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("发票明细")])],1):e._e(),"getStockinBills"==a.row.code||"/order/getWarehouseInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("入库明细")])],1):e._e(),"getDrugsUsable"==a.row.code||"40005"==a.row.code||"/contract/getContractInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("药品目录")])],1):e._e(),"40009"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("配送与入库明细")])],1):e._e(),"/order/getDistributeInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("配送与发票明细")])],1):e._e()]}}])}),t("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",formatter:e.time}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.copyToClipboard(a.row.returnValue)}}},[e._v("一键复制数据")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{"page-size":e.batchParams.limit,layout:"total,prev, pager, next, jumper",total:e.batchParams.total},on:{"current-change":e.handleCurrentChange}})],1)],1)])],1)},t.staticRenderFns=[]},EF6c:function(e,t,a){"use strict";var r=a("Cag2");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},RrpF:function(e,t,a){},Tzms:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=n(a("Q9c5")),o=n(a("DWNM"));function n(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[o.default],name:"batchLog",data:function(){return{loading:!1,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}}]},tableHeight:100,batchList:[],batchParams:{createTime:[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],codeType:"shenzhen",page:1,total:0,limit:10,importFlag:"2",code:""}}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},this.searchBtn()},methods:{copyToClipboard:function(e){var t=this;navigator.clipboard.writeText(e).then(function(){t.$message({message:"文本已成功复制到剪贴板",type:"success"})}).catch(function(e){t.$message.error("复制失败",e)})},changeSelect:function(e){this.batchParams.importFlag="",this.batchParams.code=""},searchBtn:function(){var e=this;this.loading=!0,this.$http_post(r.default.baseContext+"/supervise/interfaceFailLog/query",this.batchParams).then(function(t){"1"==t.state?(e.batchList=t.rows,e.batchParams.total=t.records):e.$message.error(t.message),e.loading=!1})},onSearch:function(e){"reset"==e?(this.batchParams.createTime=[new Date((new Date).getTime()-2592e6).format("yyyy-MM-dd"),(new Date).format("yyyy-MM-dd")],this.batchParams.importFlag="",this.batchParams.code="",this.batchParams.codeType="shenzhen",this.searchBtn()):""!=this.batchParams.createTime||""!=this.batchParams.codeType||""!=this.batchParams.importFlag||""!=this.batchParams.code?(this.batchParams.page=1,this.searchBtn()):this.$message.warning("搜索内容不能为空")},handleCurrentChange:function(e){this.batchParams.page=e,this.searchBtn()},time:function(e,t){var a=new Date(e.createTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())}}}},hq9g:function(e,t,a){"use strict";a.r(t);var r=a("Tzms"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return r[e]})}(n);t.default=o.a},l0Sa:function(e,t,a){"use strict";a("RrpF")},or71:function(e,t,a){"use strict";a.r(t);var r=a("EF6c"),o=a("hq9g");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,function(){return o[e]})}(n);a("l0Sa");var l=a("gp09"),s=Object(l.a)(o.default,r.render,r.staticRenderFns,!1,null,"206b0f10",null);t.default=s.exports}}]);