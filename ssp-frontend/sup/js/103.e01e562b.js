(window.webpackJsonp=window.webpackJsonp||[]).push([[103],{"/7kn":function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a("Q9c5")),n=o(a("DWNM"));function o(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[n.default],data:function(){return{editAble:!0,dataList:[],params:{page:1,limit:10,records:0,code:"",name:""},formData:{id:"",name:"",status:"1",isCountry:"",code:"",day:1,remark:""},dialogVisible:!1,tableHeight:100,rules:{name:[{required:!0,message:"请输入预警名称",trigger:"blur"}],code:[{required:!0,message:"请输入预警编码",trigger:"blur"}]}}},components:{},props:{},computed:{},watch:{},filters:{formatDate:function(e){if(void 0==e||""==e)return"";var t=new Date(e),a=t.getFullYear()+"-",r=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",n=t.getDate()<10?"0"+t.getDate()+" ":t.getDate()+" ";t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return a+r+n}},methods:{add:function(e,t){var a=this;if("add"==e&&(this.editAble=!1,this.formData={id:"",otherName:"",name:"",status:"1",isCountry:"",remark:"",day:1},this.dialogVisible=!0),"edit"==e){this.editAble=!0;var n=this.openLoading();this.$http_post(r.default.baseContext+"/supervise/warningConfig/info/"+t.id,{}).then(function(e){1==e.state?null!=e.row?(a.formData={id:e.row.id,code:e.row.code,name:e.row.name,day:e.row.day,status:e.row.status,isCountry:e.row.isCountry,remark:e.row.remark},a.dialogVisible=!0):a.$message.error("系统异常"):null!=e.message?a.$message.error(e.message):a.$message.error("系统异常"),n.close()})}},save:function(){var e=this;this.$refs.form.validate(function(t){if(!t)return!1;var a="";a=null!=e.formData.id&&""!=e.formData.id?r.default.baseContext+"/supervise/warningConfig/update":r.default.baseContext+"/supervise/warningConfig/save";var n=e.openLoading();e.$http_post(a,e.formData).then(function(t){1==t.state?(e.dialogVisible=!1,e.task(e.formData,a),e.onQuery()):e.$alert(t.message),n.close()})})},task:function(e){var t=this;this.$alert("是否立即执行任务刷新数据(次日系统将自动刷新数据,若现在执行请考虑系统是否空闲。)","确认信息",{confirmButtonText:"执行",cancelButtonText:"关闭",showCancelButton:!0,type:"warning"}).then(function(){var a={isCountry:e.isCountry,warningCode:e.code};t.$message.warning("系统后台刷新中，请稍后查看"),t.$http_post(r.default.baseContext+"/supervise/warningConfig/warningTask",a).then(function(e){1==e.state||t.$message.error("刷新失败，请稍后再试")})}).catch(function(e){t.$message.success("修改成功"),console.log(e)})},del:function(e){var t=this;this.$alert("确定删除【"+e.name+"】数据吗","确认信息",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,type:"warning"}).then(function(){var a=t.openLoading();t.$http_post(r.default.baseContext+"/supervise/warningConfig/delete/"+e.id,null).then(function(e){1==e.state?(t.$message.success("删除成功"),t.onQuery(),a.close()):(a.close(),t.$message.error("删除失败，请稍后再试"))})}).catch(function(e){console.log(e)})},handleCurrentChange:function(e){},onSearch:function(e){"reset"==e?(this.params.code="",this.params.name="",this.onQuery()):""!=this.params.code||""!=this.params.name?(this.params.page=1,this.onQuery()):this.$message.warning("请输入预警名称或编码查询")},onPageClick:function(e){this.params.page=e,this.onQuery()},onQuery:function(){var e=this,t=this.params,a=this.openLoading(),n=r.default.baseContext+"/supervise/warningConfig/list";this.$http_post(n,t).then(function(t){if(1==t.state){var r=t.rows;e.dataList=r,e.params.records=t.records,a.close()}else a.close(),e.$alert(t.message)})}},mounted:function(){var e=this;this.onQuery(),this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}},beforeDestroy:function(){window.onresize=null}}},"5EgT":function(e,t,a){"use strict";a("5rV+")},"5rV+":function(e,t,a){},FOEE:function(e,t,a){"use strict";a.r(t);var r=a("OqJx"),n=a("PwwN");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return n[e]})}(o);a("5EgT");var s=a("gp09"),i=Object(s.a)(n.default,r.render,r.staticRenderFns,!1,null,"1a0c7634",null);t.default=i.exports},OqJx:function(e,t,a){"use strict";var r=a("eoXW");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},PwwN:function(e,t,a){"use strict";a.r(t);var r=a("/7kn"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,function(){return r[e]})}(o);t.default=n.a},eoXW:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("预警名称")]),t("el-input",{attrs:{placeholder:"请输入预警名称"},model:{value:e.params.name,callback:function(t){e.$set(e.params,"name",t)},expression:"params.name"}}),t("span",[e._v("预警编码")]),t("el-input",{attrs:{placeholder:"请输入预警编码"},model:{value:e.params.code,callback:function(t){e.$set(e.params,"code",t)},expression:"params.code"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.dataList,"highlight-current-row":"",height:e.tableHeight},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{prop:"name",label:"预警名称"}}),t("el-table-column",{attrs:{prop:"code",label:"预警编码"}}),t("el-table-column",{attrs:{prop:"day",label:"预警时限(天)"}}),t("el-table-column",{attrs:{prop:"isCountry",label:"是否国集"},scopedSlots:e._u([{key:"default",fn:function(a){return["1"==a.row.isCountry?t("el-tag",{attrs:{type:"success"}},[e._v("是")]):e._e(),"0"==a.row.isCountry?t("el-tag",{attrs:{type:"warning"}},[e._v("否")]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"remark",width:"500px",label:"备注","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{label:"操作",align:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.add("edit",a.row)}}},[e._v("编辑")])]}}])})],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{total:e.params.records,"page-size":e.params.rows,layout:"total, prev, pager, next, jumper"},on:{"current-change":e.onPageClick}})],1)],1)]),t("el-dialog",{attrs:{title:"预警配置",visible:e.dialogVisible,width:"45%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",{staticClass:"dialog-content"},[t("el-form",{ref:"form",staticClass:"item-form",attrs:{model:e.formData,"label-width":"120px",rules:e.rules}},[t("div",{staticClass:"fromBox"},[t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警名称",prop:"name"}},[t("el-input",{attrs:{readonly:e.editAble,placeholder:"请输入预警名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警编码",prop:"code"}},[t("el-input",{attrs:{readonly:e.editAble,placeholder:"请输入预警编码"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"是否国集",prop:"isCountry"}},[t("el-select",{attrs:{disabled:e.editAble},model:{value:e.formData.isCountry,callback:function(t){e.$set(e.formData,"isCountry",t)},expression:"formData.isCountry"}},[t("el-option",{attrs:{label:"是",value:"1"}}),t("el-option",{attrs:{label:"否",value:"0"}})],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"预警时限(天)",prop:"day"}},[t("el-input-number",{attrs:{min:1,max:999},model:{value:e.formData.day,callback:function(t){e.$set(e.formData,"day",t)},expression:"formData.day"}})],1)],1)],1),t("el-row",{attrs:{type:"flex"}},[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{readonly:e.editAble,type:"textarea"},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1)],1)],1)])],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确 定")])],1)])],1)},t.staticRenderFns=[]}}]);