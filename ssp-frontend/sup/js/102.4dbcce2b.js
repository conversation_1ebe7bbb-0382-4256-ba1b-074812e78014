(window.webpackJsonp=window.webpackJsonp||[]).push([[102],{"7/vI":function(e,t,a){"use strict";var r=a("gFXq");a.o(r,"render")&&a.d(t,"render",function(){return r.render}),a.o(r,"staticRenderFns")&&a.d(t,"staticRenderFns",function(){return r.staticRenderFns})},DYMU:function(e,t,a){"use strict";a("kGMV")},gFXq:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.render=function(){var e=this,t=e._self._c;return t("div",{ref:"tableH",staticClass:"content"},[t("div",{staticClass:"search-header flex-row"},[t("div",{staticClass:"left flex-row"},[t("span",[e._v("对接方")]),t("el-select",{attrs:{placeholder:"请选择对接方"},on:{change:e.changeSelect},model:{value:e.batchParams.codeType,callback:function(t){e.$set(e.batchParams,"codeType",t)},expression:"batchParams.codeType"}},[t("el-option",{attrs:{label:"深圳接口",value:"SHENZHEN"}}),t("el-option",{attrs:{label:"广东接口",value:"GD"}}),t("el-option",{attrs:{label:"广州接口",value:"GZ"}}),t("el-option",{attrs:{label:"广东耗材接口",value:"GDHC"}}),t("el-option",{attrs:{label:"集采任务",value:"CountryPurchaseTask"}}),t("el-option",{attrs:{label:"用户登陆",value:"userLogin"}}),t("el-option",{attrs:{label:"支付凭证上传",value:"payVoucher"}}),t("el-option",{attrs:{label:"药品信息",value:"drugDetail"}})],1),"SHENZHEN"==e.batchParams.codeType||"GD"==e.batchParams.codeType||"GZ"==e.batchParams.codeType||"GDHC"==e.batchParams.codeType?t("span",[e._v("接口类型")]):e._e(),"SHENZHEN"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"订单明细",value:"getAllOrder"}}),t("el-option",{attrs:{label:"配送明细",value:"getDeliverBills"}}),t("el-option",{attrs:{label:"发票明细",value:"getInvoice"}}),t("el-option",{attrs:{label:"入库明细",value:"getStockinBills"}}),t("el-option",{attrs:{label:"药品目录",value:"getDrugsUsable"}})],1):e._e(),"GD"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"订单明细",value:"40008"}}),t("el-option",{attrs:{label:"配送与入库明细",value:"40009"}}),t("el-option",{attrs:{label:"发票明细",value:"400010"}}),t("el-option",{attrs:{label:"药品目录",value:"40005"}})],1):e._e(),"GZ"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"订单明细",value:"/order/getPurchaseOrderInfo"}}),t("el-option",{attrs:{label:"配送与发票明细",value:"/order/getDistributeInfo"}}),t("el-option",{attrs:{label:"入库明细",value:"/order/getWarehouseInfo"}}),t("el-option",{attrs:{label:"药品目录",value:"/contract/getContractInfo"}})],1):e._e(),"GDHC"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"订单明细",value:"6032"}}),t("el-option",{attrs:{label:"配送与入库明细",value:"6033"}}),t("el-option",{attrs:{label:"发票明细",value:"6034"}}),t("el-option",{attrs:{label:"耗材目录",value:"6031"}})],1):e._e(),"drugDetail"==e.batchParams.codeType?t("el-select",{attrs:{placeholder:"请选择接口类型"},model:{value:e.batchParams.code,callback:function(t){e.$set(e.batchParams,"code",t)},expression:"batchParams.code"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"药品生产企业",value:"drugCompanyName"}}),t("el-option",{attrs:{label:"药品目录",value:"drugCatalog"}}),t("el-option",{attrs:{label:"药品明细",value:"drugDetail"}})],1):e._e(),t("span",[e._v("创建时间")]),t("el-date-picker",{staticStyle:{width:"20%","margin-right":"10px"},attrs:{"unlink-panels":"","value-format":"yyyy-MM-dd",type:"daterange","picker-options":e.pickerOptions,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"center"},model:{value:e.batchParams.createTime,callback:function(t){e.$set(e.batchParams,"createTime",t)},expression:"batchParams.createTime"}}),t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.onSearch}},[e._v("查询")]),t("el-button",{attrs:{icon:"el-icon-search"},on:{click:function(t){return e.onSearch("reset")}}},[e._v("重置")]),t("el-checkbox",{staticStyle:{"margin-left":"20px"},attrs:{label:"备选项1",border:""},on:{change:e.onSearch},model:{value:e.batchParams.checked,callback:function(t){e.$set(e.batchParams,"checked",t)},expression:"batchParams.checked"}},[e._v("树形结构展示")])],1)]),t("el-row",{staticClass:"con"},[t("div",{staticClass:"con-left"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"qz-table-new",staticStyle:{width:"100%"},attrs:{data:e.batchList,"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},height:e.tableHeight}},[t("el-table-column",{attrs:{type:"index",width:"50",label:"序号"}}),t("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",formatter:e.cTime}}),t("el-table-column",{attrs:{label:"对接方",align:"center",prop:"codeType"},scopedSlots:e._u([{key:"default",fn:function(a){return["SHENZHEN"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("深圳市对接")])],1):e._e(),"GD"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("广东省对接")])],1):e._e(),"GZ"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("广州市对接")])],1):e._e(),"GDHC"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("广东省耗材对接")])],1):e._e(),"CountryPurchaseTask"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("集采完成情况")])],1):e._e(),"userLogin"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("用户登陆")])],1):e._e(),"payVoucher"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("支付凭证上传")])],1):e._e(),"drugDetail"==a.row.codeType?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("药品信息")])],1):e._e()]}}])}),"drugDetail"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"药品信息",align:"center",prop:"operationName"}}):e._e(),"userLogin"!=e.batchParams.codeType&&"payVoucher"!=e.batchParams.codeType||1!=e.batchParams.checked?e._e():t("el-table-column",{attrs:{label:"用户",align:"center",prop:"userName"},scopedSlots:e._u([{key:"default",fn:function(a){return["All"==a.row.code?t("div",{staticClass:"tag-group"},[t("el-tag",{attrs:{type:"primary",effect:"dark"}},[e._v("全部")])],1):e._e(),null==a.row.code?t("div",{staticClass:"tag-group"},[t("el-tag",{attrs:{type:"primary",effect:"success"}},[e._v(e._s(a.row.userName))])],1):e._e()]}}],null,!1,1563931882)}),"userLogin"==e.batchParams.codeType||"payVoucher"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"总人数",align:"center",prop:"sumCount"}}):e._e(),"payVoucher"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"发票号",align:"center",prop:"invoiceNo"}}):e._e(),"payVoucher"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"凭证文件名",align:"center",prop:"fileName"}}):e._e(),"userLogin"!=e.batchParams.codeType&&"payVoucher"!=e.batchParams.codeType&&"CountryPurchaseTask"!=e.batchParams.codeType?t("el-table-column",{attrs:{label:"接口类型",align:"center",prop:"code"},scopedSlots:e._u([{key:"default",fn:function(a){return["getAllOrder"==a.row.code||"40008"==a.row.code||"/order/getPurchaseOrderDetail"==a.row.code||"6032"==a.row.code?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("订单明细")])],1):e._e(),"/order/getPurchaseOrderInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"success"}},[e._v("订单")])],1):e._e(),"All"==a.row.code?t("div",{staticClass:"tag-group"},[t("el-tag",{attrs:{type:"primary",effect:"dark"}},[e._v("全部")])],1):e._e(),"getDeliverBills"==a.row.code?t("div",[t("el-tag",{attrs:{type:"danger"}},[e._v("配送明细")])],1):e._e(),"getInvoice"==a.row.code||"400010"==a.row.code||"6034"==a.row.code?t("div",[t("el-tag",{attrs:{type:"info"}},[e._v("发票明细")])],1):e._e(),"getStockinBills"==a.row.code||"/order/getWarehouseInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("入库明细")])],1):e._e(),"getDrugsUsable"==a.row.code||"40005"==a.row.code||"/contract/getContractInfo"==a.row.code||"6031"==a.row.code?t("div",[t("el-tag",{attrs:{type:"warning"}},[e._v("药品目录")])],1):e._e(),"40009"==a.row.code||"6033"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("配送与入库明细")])],1):e._e(),"/order/getDistributeInfo"==a.row.code?t("div",[t("el-tag",{attrs:{type:"primary"}},[e._v("配送与发票明细")])],1):e._e()]}}],null,!1,26927855)}):e._e(),"CountryPurchaseTask"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"集采批次情况",align:"center",prop:"code"}}):e._e(),"SHENZHEN"==e.batchParams.codeType||"GD"==e.batchParams.codeType||"GZ"==e.batchParams.codeType||"GDHC"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"成功数",align:"center",prop:"scount"}}):e._e(),"SHENZHEN"==e.batchParams.codeType||"GD"==e.batchParams.codeType||"GZ"==e.batchParams.codeType||"GDHC"==e.batchParams.codeType?t("el-table-column",{attrs:{label:"失败数",align:"center",prop:"fcount"}}):e._e(),"userLogin"!=e.batchParams.codeType&&"payVoucher"!=e.batchParams.codeType?t("el-table-column",{attrs:{label:"执行开始时间",align:"center",prop:"addStartTime",formatter:e.sTime}}):e._e(),"userLogin"!=e.batchParams.codeType&&"payVoucher"!=e.batchParams.codeType?t("el-table-column",{attrs:{label:"执行结束时间",align:"center",prop:"addEndTime",formatter:e.fTime}}):e._e()],1),t("div",{staticClass:"block"},[t("el-pagination",{attrs:{"page-size":e.batchParams.limit,layout:"total,prev, pager, next, jumper",total:e.batchParams.total},on:{"current-change":e.handleCurrentChange}})],1)],1)]),t("div",{staticStyle:{position:"absolute",bottom:"0px"}},["CountryPurchaseTask"==e.batchParams.codeType?t("span",{staticStyle:{color:"red"}},[e._v("注：标记了(*)的批次近7天没有订单数据，跳过统计")]):e._e()])],1)},t.staticRenderFns=[]},kGMV:function(e,t,a){},kgAw:function(e,t,a){"use strict";a.r(t);var r=a("y1eB"),o=a.n(r);for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return r[e]})}(l);t.default=o.a},nOtb:function(e,t,a){"use strict";a.r(t);var r=a("7/vI"),o=a("kgAw");for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,function(){return o[e]})}(l);a("DYMU");var c=a("gp09"),s=Object(c.a)(o.default,r.render,r.staticRenderFns,!1,null,"7983404f",null);t.default=s.exports},y1eB:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=l(a("Q9c5")),o=l(a("DWNM"));function l(e){return e&&e.__esModule?e:{default:e}}t.default={mixins:[o.default],name:"batchLog",data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-6048e5).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-2592e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=(new Date).format("yyyy-MM-dd"),a=new Date,r=new Date(a.getTime()-7776e6).format("yyyy-MM-dd");e.$emit("pick",[r,t])}}]},loading:!1,tableHeight:100,batchList:[],batchParams:{createTime:[(new Date).format("yyyy-MM-dd"),new Date((new Date).getTime()+864e5).format("yyyy-MM-dd")],codeType:"CountryPurchaseTask",page:1,total:0,limit:10,code:"",checked:!1}}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=e.$refs.tableH.offsetHeight-100}),window.onresize=function(){e.tableHeight=e.$refs.tableH.offsetHeight-100},this.searchBtn()},methods:{changeSelect:function(e){this.batchParams.code="",this.searchBtn()},searchBtn:function(){var e=this;this.loading=!0,this.$http_post(r.default.baseContext+"/supervise/Operation/getOperationLogList",this.batchParams).then(function(t){"1"==t.state?(e.batchList=t.rows,e.batchParams.total=t.records,e.loading=!1):(e.$message.error(t.message),e.loading=!1)}).catch(function(t){return[e.loading=!1]})},onSearch:function(e){"reset"==e?(this.batchParams.createTime=[(new Date).format("yyyy-MM-dd"),new Date((new Date).getTime()+864e5).format("yyyy-MM-dd")],this.batchParams.code="",this.batchParams.codeType="CountryPurchaseTask",this.searchBtn()):""!=this.batchParams.createTime||""!=this.batchParams.codeType||""!=this.batchParams.code?(this.batchParams.page=1,this.searchBtn()):this.$message.warning("搜索内容不能为空")},handleCurrentChange:function(e){this.batchParams.page=e,this.searchBtn()},cTime:function(e,t){var a=new Date(e.createTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},sTime:function(e,t){var a=new Date(e.addStartTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())},fTime:function(e,t){var a=new Date(e.addEndTime);return a.getFullYear()+"-"+((a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1)+"-")+(a.getDate()<10?"0"+a.getDate()+" ":a.getDate()+" ")+(a.getHours()<10?"0"+a.getHours()+":":a.getHours()+":")+(a.getMinutes()<10?"0"+a.getMinutes()+":":a.getMinutes()+":")+(a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds())}}}}}]);