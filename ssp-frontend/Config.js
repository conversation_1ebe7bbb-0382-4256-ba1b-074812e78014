var Config = {
    active : "cms",
    version:"0.4.0",
    base_url:"/ebus",
    //base_url:"/ia",
    app_key:"zwfw",
    app_security:"472349087241338881",
    app_qzsecurity:"479321221788336129",//权责获取秒批业务接口（203环境）
    //app_key:"statis",
    //app_security:"553154303640993793",//
    app_code:"INSPUR-DZZW-V4",          //APP Code
    indexPath:"/pa/dashboard",
    // 打包
    // login_type:"sso",                   //登录类型
    // 本地
    login_type:"jwt",                   //登录类型
    depends:[
        "qz"
    ]
}
import ActiveConfig from './src/pages/cms/Config.js'
Config = { ...Config, ...ActiveConfig };
export default Config;