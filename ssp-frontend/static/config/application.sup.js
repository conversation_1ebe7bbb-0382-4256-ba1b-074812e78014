//生产环境
var configPro = {
    active: "sup",
    isSecret: false,
    context: "sup",
    version: "1.0",
    baseContext: "/sup/supapi",//服务上下文
    base_url: "",
    title: "江门市医保药品采购监管系统",
    indexPath: "/index",
    login_type: "test",                   //登录类型
    treeCompetence: ['DEPT_ADMIN', 'STREET_ADMIN', "100002"],//行政机构树权限
    depends: [
        "sup"
    ],
    appInfoUrl: "/sup/supapi/bsp/pubUser/getUserInfo",
    loginUrl: "/sup/supapi/bsp/pubUser/login",
    deptParentCode: "100001",
    streetParentCode: "100002",
    smsRole: ['SUPER_ADMIN', 'DATA_ADMIN'],
    app_key: "A0001",
    app_security: "A0001",
    supAdmin:"SUPER_ADMIN",
    medicalAdmin:"MEDICAL_INSURANCE_ADMIN",
    hospitalAdmin:"HOSPITAL_ADMIN",
    socialAdmin:"SOCIAL_SECURITY_ADMIN",
    SZCartTestLink:"http://tradejm.test.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳测试购物车链接
    SZCartProLink:"http://tradejm.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳正式购物车链接
    GZCartTestLink:"https://gpo.gzggzy.cn/testtrade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州测试购物车链接
    GZCartProLink:"https://gpo.gzggzy.cn/trade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州正式购物车链接
    GDCartProLink:"http://yp.gdyjs.cn:9020/"//广东省购物车链接
}
//公网环境
var configTest = {
    isSecret: false,
    active: "sup",
    context: "sup",
    version: "1.0",
    baseContext: "/supapi",//服务上下文
    base_url: "",
    title: "深圳权责清单系统",
    indexPath: "/index",
    login_type: "test",                   //登录类型
    treeCompetence: ['DEPT_ADMIN', 'STREET_ADMIN', "100002"],//行政机构树权限
    depends: [
        "sup"
    ],
    appInfoUrl: "/supapi/bsp/pubUser/getUserInfo",
    loginUrl: "/supapi/bsp/pubUser/login",
    deptParentCode: "100001",
    streetParentCode: "100002",
    smsRole: ['SUPER_ADMIN', 'DATA_ADMIN'],
    app_key: "A0001",
    app_security: "A0001",
    SZCartTestLink:"http://tradejm.test.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳测试购物车链接
    SZCartProLink:"http://tradejm.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳正式购物车链接
    GZCartTestLink:"https://gpo.gzggzy.cn/testtrade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州测试购物车链接
    GZCartProLink:"https://gpo.gzggzy.cn/trade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州正式购物车链接
    GDCartProLink:"http://yp.gdyjs.cn:9020/"//广东省购物车链接
};
var configLocal = {
    active: "sup",
    isSecret: false,
    context: "sup",
    version: "1.0",
    baseContext: "/local/supapi",//服务上下文
    base_url: "",
    title: "工单管理系统",
    indexPath: "/index",
    login_type: "sup",                   //登录类型
    treeCompetence: ['DEPT_ADMIN', 'STREET_ADMIN', "100002"],//行政机构树权限
    depends: [
        "sup"
    ],
    isSecret: false,
    //loginUrl:"/local/supapi/sso/bsp/login",
    appInfoUrl: "/local/supapi/bsp/pubUser/getUserInfo",
    loginUrl: "/local/supapi/bsp/pubUser/login",
    deptParentCode: "100001",
    streetParentCode: "100002",
    smsRole: ['SUPER_ADMIN', 'DATA_ADMIN'],
    app_key: "A0001",
    app_security: "A0001",
    SZCartTestLink:"http://tradejm.test.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳测试购物车链接
    SZCartProLink:"http://tradejm.qywgpo.com/#/Order/ShoppingCart/ShopItem?drugsCode=",//深圳正式购物车链接
    GZCartTestLink:"https://gpo.gzggzy.cn/testtrade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州测试购物车链接
    GZCartProLink:"https://gpo.gzggzy.cn/trade/drugpurPurchaseOrderdetailRecent/addDrugpurOrderDetailByHeShan.html?contractId=",//广州正式购物车链接
    GDCartProLink:"http://yp.gdyjs.cn:9020/"//广东省购物车链接
};
return configLocal;