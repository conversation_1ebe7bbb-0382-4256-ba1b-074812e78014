import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

// route-level code splitting
const LoginPage = () => import('../pages/common/login/login.vue')

const Error = () => import('../pages/common/error/error.vue')
const Error404 = () => import('../pages/common/error/404.vue')
const NoPermission = () => import('../pages/common/error/noPermission.vue')
import AdminFrame from '../components/frame/admin/index.vue'
import Config from "@_src/api/app.config";
import BspApi from "@_src/api/BspApi";

const Base64 = require('js-base64').Base64;

/**
 * 全局路由配置
 * @type {*[]}
 */
const Common = [
    {
        path: "/",
        name: "index",
        component: AdminFrame,
        redirect: Config.indexPath,
        children: []
    },
    {
        path: "/login",
        name: "login",
        component: LoginPage,
        meta: {
            requireAuth: false
        }
    },
    {
        path: "/admin/",
        component: Error,
        children: [
            {path: "404", component: Error404},
            {path: "noPermission", component: NoPermission}
        ],
        meta: {
            requireAuth: false
        },
        hidden: true
    },
    {path: "/mmcs/*", redirect: "/admin/404", hidden: true},
    {path: "/logout", hidden: true}
]

var router = new Router({
    mode: "history",
    base: "/" + Config.context,
    scrollBehavior: () => ({y: 0}),
    routes: Common
});

// router.beforeEach((to, from, next) => {
//   next();
// });
function LoadRoutes() {
    var arrDepend = [];
    for (var ci in Config.depends) {
        var depend = Config.depends[ci];
        arrDepend.push(import("../pages/" + depend + "/router.js"));
    }
    Promise.all(arrDepend).then((res) => {
        var routes = [];
        if (res && res.length == arrDepend.length) {
            for (var i = 0; i < res.length; i++) {
                routes.push(...res[i].default);
            }
        }
        router.addRoutes(routes);
    });
}


LoadRoutes();

function canAccess (userMenuList, targetPath) {

    const allMenus=Store.getters.getAllMenus;
    if(targetPath==="/admin/404"){
        return true;
    }
    if (!userMenuList || !targetPath) return false;

   var flag= allMenus.some(item=>{
        if (item.path === targetPath) {
            return true;
        }
    })
    if(flag){
        for (let i = 0; i < userMenuList.length; i++) {
            if (userMenuList[i].PATH === targetPath) {
                return true;
            }
        }
    }else{
        return true;
    }

    console.warn("权限判断未通过",JSON.parse(JSON.stringify(userMenuList)),targetPath)
    return false;
}
router.canAccess = canAccess

let Store = null;
router.beforeEach((to, from, next) => {
    //获取当前用户菜单权限
    // console.log("获取当前用户菜单权限", to.fullPath, Store.getters.curUser)

    const currUser=Store.getters.curUser
    if(currUser && currUser.menus){
        if(!canAccess(currUser.menus,to.fullPath)){
            // 禁止访问
            console.error("禁止访问")
            next({
                path: '/admin/404',
                query: {}
            })
        }
    }

    if (from.fullPath.indexOf("/ebus/") == 0) {
        next();
        return;
    } else if (to.fullPath.indexOf("/logout") == 0) {
        var redirect = from.fullPath;
        if (redirect != null && redirect != undefined && (redirect.indexOf('admin/404') != -1 || redirect.indexOf('admin/noPermission') != -1)) {
            redirect = '/'
        }
        Store.dispatch("logout");
        Store.dispatch("setUser", null);
        var type = to.query.type;
        if (type === 'sso') {
            window.location.href = '/sso/bsp/login?redirect=' + Base64.encode("/" + Config.context + redirect)
        } else {
            next({
                path: '/login',
                query: {redirect: Base64.encode(redirect)}
            })
        }
        return;
    } else if (false != to.meta.requireAuth) {
        // 判断该路由是否需要登录权限
        const token = Store.getters.token
        if (token != null && token.length > 0) {
            next();
        } else {
            BspApi.getCurToken(ret => {
                if (ret.state == 1) {
                    var token = ret.message;
                    Store.commit("setToken", token);
                    next();
                } else {
                    var redirect = to.fullPath;
                    next({
                        path: '/login',
                        query: {redirect: Base64.encode(redirect)}
                    })
                }

            })
        }
    } else {
        next();
    }
});

export function createRouter(store) {
    Store = store;
    return router;
}
