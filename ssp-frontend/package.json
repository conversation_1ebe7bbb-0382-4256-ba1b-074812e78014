{"name": "ssp-frontend", "version": "1.0.0", "description": "社会保障信息服务平台", "author": "", "private": true, "scripts": {"dev": "iweb server", "build": "iweb build"}, "dependencies": {"axios": "^0.18.0", "babel-core": "^6.26.3", "crypto-js": "^3.1.9-1", "echarts": "^4.7.0", "element-ui": "^2.8.2", "es6-promise": "^4.2.4", "html2canvas": "^1.0.0-rc.7", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "mavon-editor": "^2.7.7", "pinyin": "^2.9.0", "prettier": "1.12.1", "print-js": "^1.5.0", "quill-image-drop-module": "^1.0.3", "relative": "^3.0.2", "sortablejs": "^1.10.0", "vue": "^2.5.16", "vue-clipboard2": "^0.3.1", "vue-drag-resize": "^1.3.2", "vue-print-nb": "^1.5.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue-wangeditor": "^1.3.10", "vuex": "^3.0.1"}, "devDependencies": {"@jangod/iweb-action-publish": "latest", "@jangod/iweb-fix-babel": "latest", "@jangod/iweb-fix-eslint": "latest", "@jangod/iweb-fix-scene": "latest", "@jangod/iweb-fix-splitchunk": "latest", "@jangod/iweb-fix-vue": "latest", "babel-eslint": "^8.2.6", "copy-webpack-plugin": "^5.0.3", "eslint": "^2.2.0", "eslint-config-standard": "^5.1.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0"}, "engines": {"node": ">= 8.0.0", "npm": ">= 5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}