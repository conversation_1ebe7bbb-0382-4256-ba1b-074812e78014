# 0. 准备动作 
```
所需环境
cnpm@7.1.1
npm@6.14.15
npm install -g npm@6.14.15
node@14.18.0

1.node最新环境
2.安装cnpm npm install -g cnpm@7.1.1
3.切换到私有仓库
cnpm config set disturl https://npmmirror.com/mirrors/node
cnpm config set registry http://117.73.3.108:7001
4.安装wefe cnpm install -g @jangod/iweb-cli
5.cnpm install
```
# 1. 运行项目0
```
1.cnpm run dev
```
# 2. 安装依赖
```
npm install
```

# 3. web端本地调试
```
cnpm run dev 

访问地址
http://127.0.0.1:9394/local/html/index.html
```

# 4. 编译发布
```
cnpm run build
```

# 5. 项目结构
```
├── dist                              编译目录
│   ├── html                          静态模板
│   │   └── index.html           
│   ├── css                           样式文件
│   │   └── index.css
│   │   js                            脚本文件
│   │   └── index.js
├── src                               源文件目录
│   ├── pages                         项目页面集合
│   │   └── index                     /local/html/index.html
│   │       ├── components            页面相关组件
│   │       ├── images                图片集
│   │       ├── main.js               页面入口
│   │       ├── index.vue             视图文件
│   │       ├── index.js              业务逻辑文件
│   │       ├── index.less            样式文件
│   │       └── data.js               cgi数据文件
│   ├── components                    公共组件库
│   │   ├── {componentName}           组件名
│   │   │   ├── index.js              组件入口
│   │   │   └── src                   组件vue
│   ├── core                          公共核心方法（提供给vue mixins）
│   │   ├── base.js
│   ├── styles                        公共样式
│   ├── fonts                         公共字体
│   ├── utils                         公共工具类
│   └── images                        公共图片
│       
├── README.md
├── package.json
└── iweb.config.js                    iweb编译器配置
```
